# Knowledge Sphere Deployment Guide

This guide provides comprehensive instructions for deploying the Knowledge Sphere application to production environments.

_Last Updated: June 2025_

## 🚀 Deployment Overview

### Supported Platforms

- **Vercel** (Recommended) - Seamless Next.js deployment
- **Railway** - Full-stack deployment with PostgreSQL
- **Docker** - Containerized deployment for any platform
- **Self-hosted** - Custom server deployment

### Prerequisites

- Node.js 18+ runtime environment
- PostgreSQL 14+ with pgvector extension
- Cloudflare R2 bucket for file storage
- OpenAI or Azure OpenAI API access
- Email service (Resend) for transactional emails

## 🔧 Environment Configuration

### Required Environment Variables

Create a `.env.production` file with the following variables:

```bash
# Database Configuration
DATABASE_URL="****************************************/knowledge_sphere"

# Authentication
LUCIA_SECRET="your-secure-lucia-secret-key-min-32-chars"
NEXTAUTH_URL="https://your-domain.com"

# File Storage (Cloudflare R2)
CLOUDFLARE_ACCOUNT_ID="your-cloudflare-account-id"
CLOUDFLARE_ACCESS_KEY_ID="your-r2-access-key"
CLOUDFLARE_SECRET_ACCESS_KEY="your-r2-secret-key"
CLOUDFLARE_BUCKET_NAME="your-bucket-name"
CLOUDFLARE_R2_ENDPOINT="https://your-account-id.r2.cloudflarestorage.com"

# AI Services
OPENAI_API_KEY="your-openai-api-key"
# OR for Azure OpenAI
AZURE_OPENAI_API_KEY="your-azure-openai-key"
AZURE_OPENAI_ENDPOINT="https://your-resource.openai.azure.com"
AZURE_OPENAI_DEPLOYMENT_NAME="your-deployment-name"

# Email Service
RESEND_API_KEY="your-resend-api-key"
FROM_EMAIL="<EMAIL>"

# Application Settings
NODE_ENV="production"
NEXT_PUBLIC_APP_URL="https://your-domain.com"
```

### Security Considerations

- Use strong, unique secrets for all keys
- Enable SSL/TLS for all connections
- Restrict database access to application servers only
- Use environment-specific API keys
- Enable CORS protection for production domains

## 🌐 Vercel Deployment (Recommended)

### Step 1: Prepare Repository

```bash
# Ensure all dependencies are up to date
pnpm install

# Build and test locally
pnpm run build
pnpm run start
```

### Step 2: Database Setup

```bash
# Set up PostgreSQL with pgvector
# Option 1: Vercel Postgres (recommended)
# Option 2: External provider (Supabase, Railway, etc.)

# Run migrations
pnpm run db:migrate
```

### Step 3: Vercel Configuration

Create `vercel.json`:

```json
{
  "framework": "nextjs",
  "buildCommand": "pnpm run build",
  "devCommand": "pnpm run dev",
  "installCommand": "pnpm install",
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 60
    }
  },
  "env": {
    "PNPM_FLAGS": "--shamefully-hoist"
  }
}
```

### Step 4: Deploy

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Set environment variables
vercel env add DATABASE_URL
vercel env add LUCIA_SECRET
# ... add all other environment variables
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json pnpm-lock.yaml* ./
RUN corepack enable pnpm && pnpm i --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client and build
RUN corepack enable pnpm && pnpm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - '3000:3000'
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - LUCIA_SECRET=${LUCIA_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      # ... other environment variables
    depends_on:
      - postgres

  postgres:
    image: pgvector/pgvector:pg16
    environment:
      POSTGRES_DB: knowledge_sphere
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'

volumes:
  postgres_data:
```

## 🗄️ Database Setup

### PostgreSQL with pgvector

#### Option 1: Managed Service (Recommended)

```bash
# Supabase (includes pgvector)
# 1. Create project at supabase.com
# 2. Enable pgvector extension in SQL editor:
CREATE EXTENSION IF NOT EXISTS vector;

# Vercel Postgres
# 1. Add Vercel Postgres to your project
# 2. Enable pgvector extension
```

#### Option 2: Self-hosted

```bash
# Install PostgreSQL and pgvector
sudo apt-get install postgresql postgresql-contrib
git clone https://github.com/pgvector/pgvector.git
cd pgvector
make
sudo make install

# Create database and enable extension
sudo -u postgres createdb knowledge_sphere
sudo -u postgres psql knowledge_sphere -c "CREATE EXTENSION vector;"
```

### Database Migration

```bash
# Run migrations in production
DATABASE_URL="your-production-db-url" pnpm run db:migrate

# Verify schema
DATABASE_URL="your-production-db-url" pnpm run db:studio
```

## ☁️ File Storage Setup

### Cloudflare R2 Configuration

#### Step 1: Create R2 Bucket

```bash
# Using Cloudflare Dashboard
# 1. Go to R2 Object Storage
# 2. Create bucket with unique name
# 3. Configure CORS policy
```

#### Step 2: CORS Policy

```json
[
  {
    "AllowedOrigins": ["https://your-domain.com"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
```

#### Step 3: API Tokens

```bash
# Create API token with R2 permissions
# Scope: Zone:Zone:Read, Zone:Zone Settings:Edit
# Account: Include all accounts
```

## 📧 Email Service Setup

### Resend Configuration

```bash
# 1. Sign up at resend.com
# 2. Verify your domain
# 3. Create API key
# 4. Configure DNS records for domain verification
```

### Email Templates

The application includes built-in email templates for:

- Email verification
- Password reset
- Space invitations

## 🔍 Monitoring & Logging

### Application Monitoring

```bash
# Recommended monitoring services
# - Vercel Analytics (for Vercel deployments)
# - Sentry for error tracking
# - LogRocket for user session recording
# - Uptime monitoring (Pingdom, UptimeRobot)
```

### Database Monitoring

```bash
# Monitor key metrics:
# - Connection pool usage
# - Query performance
# - Vector search performance
# - Storage usage
```

### Performance Optimization

```bash
# Enable compression
# Configure CDN for static assets
# Implement database connection pooling
# Set up Redis for caching (optional)
```

## 🔒 Security Checklist

### Pre-deployment Security

- [ ] All environment variables secured
- [ ] Database access restricted
- [ ] API rate limiting configured
- [ ] CORS policies set correctly
- [ ] SSL/TLS certificates installed
- [ ] Security headers configured
- [ ] Input validation enabled
- [ ] File upload restrictions in place

### Post-deployment Security

- [ ] Regular security updates
- [ ] Database backups automated
- [ ] Access logs monitored
- [ ] Intrusion detection enabled
- [ ] Vulnerability scanning scheduled

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Errors

```bash
# Check connection string format
# Verify network access
# Confirm pgvector extension installed
# Check connection pool limits
```

#### File Upload Issues

```bash
# Verify R2 bucket permissions
# Check CORS configuration
# Confirm API token scopes
# Validate file size limits
```

#### AI Service Errors

```bash
# Verify API key validity
# Check rate limits
# Confirm model availability
# Monitor token usage
```

### Health Checks

```bash
# Application health endpoint
GET /api/health

# Database connectivity
GET /api/health/db

# File storage connectivity
GET /api/health/storage
```

## 📈 Scaling Considerations

### Horizontal Scaling

- Load balancer configuration
- Database read replicas
- CDN for static assets
- Microservices architecture (future)

### Vertical Scaling

- Server resource allocation
- Database performance tuning
- Connection pool optimization
- Caching strategies

---

_This deployment guide covers the essential steps for production deployment. For specific platform configurations, consult the respective platform documentation._
