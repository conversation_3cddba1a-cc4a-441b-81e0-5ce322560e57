# Knowledge Sphere Technology Stack

This document provides a comprehensive overview of the technologies, frameworks, and tools used in the Knowledge Sphere application. Each section includes implementation details and key file references.

_Last Updated: June 2025_

## 🏗️ Core Framework & Runtime

### Next.js 14 (App Router)

- **Purpose**: React framework with server-side rendering and static site generation
- **Version**: 14.2.15 with App Router architecture
- **Configuration**: `next.config.mjs`
- **Key Directories**:
  - `src/app/` - App Router structure
  - `src/app/(main)/` - Main application routes
  - `src/app/(auth)/` - Authentication routes
  - `src/app/api/` - API routes and endpoints

### React 18 & TypeScript

- **React**: 18.x with modern hooks and concurrent features
- **TypeScript**: ~5.5.0 for type safety across the application
- **Configuration**: `tsconfig.json`
- **Type Definitions**: `src/types/` directory

## 🔐 Authentication & Security

### Lucia Auth

- **Purpose**: Modern authentication library with session management
- **Adapter**: `@lucia-auth/adapter-drizzle` for database integration
- **Implementation**:
  - Setup: `src/lib/auth.ts`
  - Schema: `src/database/schema/index.ts` (users, sessions tables)
  - Email Verification: `src/app/api/auth/verify/route.ts`
  - Password Reset: `src/app/api/auth/reset-password/route.ts`

### Security Features

- **Password Hashing**: Argon2 with `@node-rs/argon2`
- **Session Management**: Secure cookie-based sessions
- **CSRF Protection**: Built-in protection with Lucia
- **Input Validation**: Zod schemas throughout the application

## 💾 Database & Data Management

### PostgreSQL with Vector Extensions

- **Database**: PostgreSQL with pgvector extension for embeddings
- **Connection**: Configured in `src/database/drizzle.ts`
- **Vector Support**: 1536-dimensional embeddings for semantic search

### Drizzle ORM

- **Purpose**: Type-safe SQL query builder and ORM
- **Version**: 0.34.1 with Drizzle Kit 0.25.0
- **Key Files**:
  - Schema: `src/database/schema/index.ts`
  - Configuration: `drizzle.config.ts`
  - Migrations: `src/database/migrations/`
  - Repositories: `src/database/repository/`

### Database Schema Overview

- **Users**: Authentication and profile management
- **Spaces**: Collaborative workspaces with sharing
- **Documents**: File metadata with vector embeddings
- **Space Members**: Role-based access control
- **Chat Topics**: Conversation management
- **Document Chunks**: Processed content for retrieval

## 🔗 API Layer & Communication

### tRPC (Type-Safe APIs)

- **Purpose**: End-to-end type-safe API layer
- **Version**: 11.0.0-rc.584
- **Implementation**:
  - Router Setup: `src/server/api/trpc.ts`
  - Client API: `src/lib/trpc/client-api.ts`
  - Server Routers: `src/server/api/routers/`
  - Procedures: `src/server/api/procedures/`

### TanStack Query (React Query)

- **Purpose**: Data fetching and caching
- **Version**: 5.59.15
- **Integration**: Used with tRPC for optimistic updates and caching
- **Configuration**: Integrated in tRPC client setup

## 🎨 UI Framework & Styling

### Shadcn/UI Component System

- **Purpose**: Accessible and customizable UI components
- **Built on**: Radix UI primitives + Tailwind CSS
- **Components**: `src/components/ui/`
- **Configuration**: `components.json`

### Radix UI Primitives

- **Purpose**: Unstyled, accessible UI primitives
- **Components Used**:
  - Dialog, Dropdown Menu, Tooltip, Select
  - Avatar, Checkbox, Progress, Tabs
  - Scroll Area, Separator, Switch

### Tailwind CSS

- **Purpose**: Utility-first CSS framework
- **Version**: 3.4.1
- **Configuration**: `tailwind.config.ts`
- **Plugins**:
  - `@tailwindcss/typography` for rich text
  - `tailwindcss-animate` for animations

### Styling Features

- **Dark Mode**: Built-in theme switching with `next-themes`
- **Icons**: Lucide React icon library
- **Animations**: CSS animations with Tailwind
- **Responsive Design**: Mobile-first responsive layouts

## 📝 Forms & Data Validation

### React Hook Form

- **Purpose**: Performant form handling with minimal re-renders
- **Version**: 7.53.1
- **Implementation**: Used throughout feature components
- **Key Features**: Validation, error handling, form state management

### Zod Schema Validation

- **Purpose**: TypeScript-first schema validation
- **Version**: 3.23.8
- **Integration**: `@hookform/resolvers/zod` for form validation
- **Schema Definitions**: `src/features/*/schemas/`
- **Usage**: API validation, form validation, type inference

## ☁️ File Storage & Processing

### Cloudflare R2 (S3-Compatible)

- **Purpose**: Object storage for uploaded documents
- **SDK**: `@aws-sdk/client-s3` (S3-compatible API)
- **Configuration**: `src/lib/s3-client.ts`
- **Features**: Presigned URLs, secure uploads, automatic cleanup

### Document Processing Pipeline

- **PDF Processing**: `pdf-parse` and `pdfjs-dist` for text extraction
- **DOCX Processing**: `mammoth` for Word document parsing
- **Text Processing**: Custom chunking algorithms for optimal retrieval
- **Implementation**: `src/lib/materials/processor.ts`

### Document Viewing

- **PDF Viewer**: `@react-pdf-viewer/core` with navigation plugins
- **Document Preview**: `src/components/documents/document-viewer.tsx`
- **Multi-format Support**: PDF, DOCX, DOC, TXT files

## 🤖 AI & Machine Learning

### AI SDK Integration

- **Framework**: Vercel AI SDK (v4.0.13)
- **Providers**:
  - `@ai-sdk/openai` for OpenAI integration
  - `@ai-sdk/azure` for Azure OpenAI services
- **Features**: Streaming responses, structured output, embeddings

### LangChain Ecosystem

- **Core**: `@langchain/core` (0.3.13) for base abstractions
- **Community**: `@langchain/community` (0.3.6) for integrations
- **LangGraph**: `@langchain/langgraph` (0.3.1) for agent workflows
- **SDK**: `@langchain/langgraph-sdk` (0.0.84) for React integration

### AI Features Implementation

- **Embeddings**: Vector generation for semantic search
- **Chat Streaming**: Real-time AI response streaming
- **Agent Workflows**: Multi-step reasoning with LangGraph
- **Context Retrieval**: RAG (Retrieval-Augmented Generation)

## 🔧 State Management & Utilities

### Zustand

- **Purpose**: Lightweight state management
- **Version**: 5.0.3
- **Implementation**:
  - User Store: `src/stores/user.ts`
  - Chat Store: `src/stores/chat.ts`
- **Features**: Simple API, TypeScript support, minimal boilerplate

### Utility Libraries

- **Date/Time**: `date-fns` (4.1.0) and `luxon` (3.5.0) for date manipulation
- **Class Names**: `clsx` and `tailwind-merge` for conditional styling
- **UUID**: `uuid` (10.0.0) for unique identifier generation
- **Hooks**: `usehooks-ts` (3.1.0) for common React hooks

## 🎯 User Experience Enhancements

### Notifications & Feedback

- **Toast Notifications**: Sonner (1.5.0) for user feedback
- **Loading States**: Custom loading components and progress indicators
- **Error Handling**: Comprehensive error boundaries and user-friendly messages

### Interactive Components

- **Icons**: Lucide React (0.453.0) for consistent iconography
- **Animations**: CSS animations with Tailwind and custom transitions
- **Responsive Design**: Mobile-first approach with breakpoint-specific layouts

## 🛠️ Development Tools & Quality

### Code Quality

- **TypeScript**: ~5.5.0 with strict configuration
- **ESLint**: JavaScript/TypeScript linting with Next.js config
- **Prettier**: Code formatting with Tailwind plugin
- **Configuration Files**:
  - `tsconfig.json` - TypeScript configuration
  - `eslint.config.js` - Linting rules
  - `.prettierrc` - Code formatting rules

### Git Workflow

- **Husky**: Git hooks for pre-commit validation
- **Commitlint**: Conventional commit message validation
- **Pretty Quick**: Staged file formatting on commit

### Build & Development

- **Package Manager**: pnpm for efficient dependency management
- **Build Tool**: Next.js built-in build system with SWC
- **Development**: Hot reload and fast refresh for optimal DX

## 📧 Communication Services

### Email Service

- **Provider**: Resend (4.3.0) for transactional emails
- **Implementation**: `src/lib/email.ts`
- **Templates**: Email templates for verification and notifications
- **Features**: Reliable delivery, analytics, template management

## 🌐 Environment & Configuration

### Environment Management

- **Validation**: `@t3-oss/env-nextjs` (0.11.1) for type-safe environment variables
- **Configuration**: `src/lib/env.mjs`
- **Security**: Separate client and server environment variables

### Runtime Configuration

- **Next.js Config**: `next.config.mjs` for build and runtime settings
- **PostCSS**: `postcss.config.mjs` for CSS processing
- **Tailwind Config**: `tailwind.config.ts` for design system configuration

## 🏗️ Application Architecture

### Project Structure

The application follows a feature-based architecture with clear separation of concerns:

```
src/
├── app/                    # Next.js App Router
│   ├── (main)/            # Main application routes
│   ├── (auth)/            # Authentication routes
│   └── api/               # API endpoints
├── components/            # Reusable UI components
│   ├── ui/               # Shadcn/UI components
│   ├── chat/             # Chat-specific components
│   ├── documents/        # Document viewing components
│   └── space/            # Space management components
├── database/             # Database layer
│   ├── schema/           # Drizzle schema definitions
│   ├── migrations/       # Database migrations
│   └── repository/       # Data access layer
├── features/             # Feature-specific code
│   ├── materials/        # Document management
│   └── spaces/           # Space management
├── lib/                  # Utility functions and shared code
│   ├── auth.ts          # Authentication utilities
│   ├── langgraph/       # AI agent implementation
│   └── materials/       # Document processing
├── server/               # Server-side code
│   └── api/             # tRPC routers and procedures
├── stores/               # Zustand state management
└── types/                # TypeScript type definitions
```

### Key Integration Points

#### Space Management System

- **Database**: `src/database/schema/index.ts` (spaces, spaceMembers, spaceInvitations)
- **UI Components**: `src/components/space/share-space-dialog.tsx`
- **Feature Logic**: `src/features/spaces/components/`
- **API**: `src/server/api/routers/space.ts`

#### Document Processing Pipeline

- **Upload Interface**: `src/features/materials/components/upload-material-dialog.tsx`
- **Processing Logic**: `src/lib/materials/processor.ts`
- **Storage Integration**: `src/lib/s3-client.ts`
- **API Endpoints**: `src/app/api/materials/upload/route.ts`

#### AI Chat System

- **Chat Interface**: `src/components/chat/chat-ui.tsx`
- **Enhanced Hook**: `src/hooks/use-chat-enhanced.ts`
- **LangGraph Agent**: `src/lib/langgraph/graph.ts`
- **API Endpoint**: `src/app/api/chat/[chatId]/langgraph/route.ts`

#### Search & Retrieval

- **Search Logic**: `src/lib/materials/search.ts`
- **Vector Storage**: PostgreSQL with pgvector extension
- **AI Integration**: Embedding generation and semantic search

## 🔄 Data Flow Architecture

### Request Flow

1. **Client Request** → Next.js App Router
2. **API Layer** → tRPC procedures with validation
3. **Business Logic** → Repository pattern for data access
4. **Database** → PostgreSQL with optimized queries
5. **Response** → Type-safe data back to client

### AI Processing Flow

1. **User Query** → LangGraph agent initialization
2. **Query Analysis** → Intent classification and keyword extraction
3. **Document Retrieval** → Vector and full-text search
4. **Response Generation** → Streaming AI responses
5. **Citation Management** → Automatic source references

## 📦 Deployment Considerations

### Production Requirements

- **Node.js**: 18+ runtime environment
- **PostgreSQL**: Database with pgvector extension
- **Cloudflare R2**: Object storage for documents
- **AI Services**: OpenAI or Azure OpenAI API access
- **Email Service**: Resend for transactional emails

### Performance Optimizations

- **Database Indexing**: Optimized indexes for vector and text search
- **Caching**: React Query for client-side caching
- **Streaming**: Real-time AI responses and file uploads
- **Code Splitting**: Automatic code splitting with Next.js

---

_This technology documentation reflects the current state of the Knowledge Sphere application. For feature-specific details, see the [Features Documentation](./features.md)._
