# AWS Textract OCR Integration - Usage Examples

This document provides comprehensive examples of how to use the AWS Textract OCR integration in the knowledge-sphere platform.

## Overview

The OCR integration provides:

- **Enhanced text extraction** using AWS Textract
- **Visual highlighting** in PDF viewer with confidence indicators
- **Layout recognition** (titles, headers, paragraphs, etc.)
- **Interactive features** for text selection and AI referencing
- **Fallback support** to standard PDF parsing when Textract is unavailable

## Basic Usage

### 1. Document Upload with OCR Processing

```typescript
// The upload process automatically uses Textract for PDF files
const uploadResult = await fetch('/api/materials/upload', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    files: [
      {
        key: 'documents/sample.pdf',
        name: 'sample.pdf',
        type: 'application/pdf',
        size: 1024000,
      },
    ],
    spaceIds: ['space-id-123'],
  }),
});
```

**What happens behind the scenes:**

1. Document status is set to `processing`
2. File is downloaded from R2 storage
3. Textract analyzes the document for text and layout
4. Extracted text is chunked with geometry data preserved
5. Embeddings are generated for search
6. OCR analysis is stored in database
7. Document status is set to `completed`

### 2. Enhanced PDF Viewer Usage

```typescript
import { DocumentViewer } from '@/components/documents/document-viewer';

// Basic PDF viewing (standard mode)
<DocumentViewer
  url="https://your-cdn.com/document.pdf"
  fileType={DocumentType.PDF}
/>

// Enhanced PDF viewing with OCR highlights
<DocumentViewer
  url="https://your-cdn.com/document.pdf"
  fileType={DocumentType.PDF}
  options={{
    enableOCRHighlights: true,
    documentId: "doc-123",
    fileName: "Research Paper.pdf",
    onTextReference: (text, context) => {
      console.log('Selected text:', text);
      console.log('Context:', context);
      // Add to chat or copy to clipboard
    }
  }}
/>
```

### 3. Using the Enhanced PDF Viewer Directly

```typescript
import { DocumentViewerEnhanced } from '@/components/documents/document-viewer-enhanced';

function PDFViewerPage() {
  const handleTextReference = (text: string, context: any) => {
    // Add selected text to chat input
    setChatInput(prev => `${prev}\n\nReferenced from ${context.fileName}:\n"${text}"`);
  };

  return (
    <DocumentViewerEnhanced
      documentId="doc-123"
      documentUrl="https://your-cdn.com/document.pdf"
      fileName="Research Paper.pdf"
      onTextReference={handleTextReference}
      className="h-screen"
    />
  );
}
```

## Advanced Usage

### 1. Custom Highlight Controls

```typescript
import { useDocumentHighlights } from '@/hooks/use-document-highlights';
import { HighlightControls } from '@/components/documents/highlight-controls';

function CustomPDFViewer({ documentId }: { documentId: string }) {
  const {
    highlights,
    statistics,
    confidenceFilter,
    updateConfidenceFilter,
    updateLayoutTypeFilter,
  } = useDocumentHighlights(documentId, {
    source: 'textract', // Use raw Textract data instead of processed chunks
    minConfidence: 0.7,  // Only show high-confidence highlights
  });

  return (
    <div className="flex">
      <HighlightControls
        documentId={documentId}
        showConfidenceIndicators={true}
        enableInteractions={true}
      />
      <div className="flex-1">
        {/* Your PDF viewer here */}
        <div className="p-4">
          <h3>Document Statistics</h3>
          <p>Total highlights: {statistics?.total}</p>
          <p>Average confidence: {Math.round((statistics?.averageConfidence || 0) * 100)}%</p>
          <p>Layout types: {statistics?.layoutTypes.join(', ')}</p>
        </div>
      </div>
    </div>
  );
}
```

### 2. Manual Highlight Fetching

```typescript
// Fetch highlights for a specific document
const fetchHighlights = async (documentId: string) => {
  const response = await fetch(
    `/api/documents/${documentId}/highlights?source=textract&minConfidence=0.8`
  );
  const data = await response.json();

  console.log('Highlights:', data.highlights);
  console.log('Processing info:', {
    totalBlocks: data.totalBlocks,
    averageConfidence: data.averageConfidence,
    processingTime: data.processingTime,
  });

  return data.highlights;
};
```

### 3. Processing Custom Documents

```typescript
import { MaterialRepository } from '@/database/repository/material';
import { processMaterialFromR2Enhanced } from '@/lib/materials/processor';

// Process a document with enhanced OCR
const processDocument = async (fileKey: string, fileName: string) => {
  try {
    // Process with Textract
    const result = await processMaterialFromR2Enhanced(
      fileKey,
      fileName,
      'application/pdf',
      true // Enable debug logging
    );

    console.log('OCR Analysis:', result.ocrAnalysis);
    console.log('Extracted text blocks:', result.ocrAnalysis?.extractedText.length);
    console.log('Layout elements:', result.ocrAnalysis?.layoutElements);

    // Store in database
    const document = await MaterialRepository.storeMaterial({
      fileName,
      fileType: 'PDF',
      s3Key: fileKey,
      uploadedBy: 'user-id',
      summary: result.summary?.summary || null,
      summaryEmbedding: result.summary?.summaryEmbedding || null,
    });

    // Store enhanced chunks with OCR data
    await MaterialRepository.storeChunksEnhanced({
      documentId: document.id,
      chunks: result.chunks,
    });

    // Store OCR analysis
    if (result.ocrAnalysis) {
      await MaterialRepository.storeOcrAnalysis(
        document.id,
        result.ocrAnalysis,
        0 // Processing time would be tracked separately
      );
    }

    return document;
  } catch (error) {
    console.error('Processing failed:', error);
    throw error;
  }
};
```

## Integration with Chat/AI Features

### 1. Text Selection and AI Reference

```typescript
import { useState } from 'react';
import { DocumentViewerEnhanced } from '@/components/documents/document-viewer-enhanced';
import { ChatInterface } from '@/components/chat/chat-interface';

function DocumentChatPage({ documentId }: { documentId: string }) {
  const [chatInput, setChatInput] = useState('');
  const [selectedText, setSelectedText] = useState<string | null>(null);

  const handleTextReference = (text: string, context: any) => {
    const reference = `Based on "${text}" from ${context.fileName} (${context.layoutType}, ${Math.round(context.confidence * 100)}% confidence):`;
    setChatInput(reference);
    setSelectedText(text);
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim()) return;

    // Send message with document context
    await sendChatMessage(chatInput, {
      documentId,
      referencedText: selectedText,
      // Additional context can be added here
    });

    setChatInput('');
    setSelectedText(null);
  };

  return (
    <div className="grid grid-cols-2 h-screen gap-4">
      <DocumentViewerEnhanced
        documentId={documentId}
        documentUrl="/api/documents/view"
        fileName="Document.pdf"
        onTextReference={handleTextReference}
      />
      <div className="border-l pl-4">
        <ChatInterface
          input={chatInput}
          onInputChange={setChatInput}
          onSend={handleSendMessage}
          selectedText={selectedText}
        />
      </div>
    </div>
  );
}
```

### 2. Highlight-Based Context Search

```typescript
import {
  convertTextractToHighlights,
  groupHighlightsByLayoutType,
} from '@/lib/materials/ocr-utils';

// Find relevant highlights for a query
const findRelevantHighlights = (highlights: OCRHighlight[], query: string) => {
  const queryLower = query.toLowerCase();

  return highlights.filter((highlight) => {
    const textMatch = highlight.text.toLowerCase().includes(queryLower);
    const isHighConfidence = highlight.confidence > 0.8;
    const isImportantLayout = ['TITLE', 'HEADER', 'SECTION_HEADER'].includes(highlight.layoutType);

    return textMatch && (isHighConfidence || isImportantLayout);
  });
};

// Use in search context
const enhanceSearchWithHighlights = async (documentId: string, query: string) => {
  const response = await fetch(`/api/documents/${documentId}/highlights`);
  const { highlights } = await response.json();

  const relevantHighlights = findRelevantHighlights(highlights, query);
  const layoutGroups = groupHighlightsByLayoutType(relevantHighlights);

  return {
    highlights: relevantHighlights,
    titles: layoutGroups.TITLE || [],
    headers: layoutGroups.HEADER || [],
    paragraphs: layoutGroups.PARAGRAPH || [],
  };
};
```

## Error Handling and Fallbacks

### 1. Graceful Degradation

```typescript
const processDocumentWithFallback = async (fileKey: string, fileName: string, fileType: string) => {
  try {
    // Try enhanced processing first
    const result = await processMaterialFromR2Enhanced(fileKey, fileName, fileType, true);

    if (result.ocrAnalysis) {
      console.log('✅ Textract processing successful');
      return { ...result, processingMethod: 'textract' };
    } else {
      console.log('⚠️ Textract not available, used standard processing');
      return { ...result, processingMethod: 'standard' };
    }
  } catch (error) {
    console.error('Enhanced processing failed, falling back to standard:', error);

    // Fallback to standard processing
    const result = await processMaterialFromR2(fileKey, fileName, fileType, true);
    return { ...result, processingMethod: 'fallback' };
  }
};
```

### 2. Service Availability Check

```typescript
import { checkTextractAvailability } from '@/lib/aws/textract';

// Check service before processing
const conditionalProcessing = async (document: DocumentInput) => {
  const isTextractAvailable = await checkTextractAvailability();

  if (!isTextractAvailable) {
    console.warn('Textract service unavailable, using standard processing');
    return processWithStandardMethod(document);
  }

  return processWithTextract(document);
};
```

## Performance Optimization

### 1. Caching Strategy

```typescript
// Cache OCR results to avoid reprocessing
const cache = new Map<string, OCRHighlight[]>();

const getCachedHighlights = async (documentId: string): Promise<OCRHighlight[]> => {
  if (cache.has(documentId)) {
    return cache.get(documentId)!;
  }

  const response = await fetch(`/api/documents/${documentId}/highlights`);
  const { highlights } = await response.json();

  cache.set(documentId, highlights);
  return highlights;
};
```

### 2. Lazy Loading

```typescript
const LazyPDFViewer = ({ documentId }: { documentId: string }) => {
  const [showHighlights, setShowHighlights] = useState(false);

  const { highlights, isLoading } = useDocumentHighlights(documentId, {
    enabled: showHighlights, // Only load when needed
  });

  return (
    <div>
      <button onClick={() => setShowHighlights(true)}>
        Show OCR Highlights
      </button>

      {showHighlights && (
        <DocumentViewerEnhanced
          documentId={documentId}
          documentUrl="..."
          fileName="..."
        />
      )}
    </div>
  );
};
```

## Testing and Development

### 1. Debug Mode

```typescript
// Enable detailed logging
const result = await processMaterialFromR2Enhanced(
  fileKey,
  fileName,
  fileType,
  true // Debug mode
);

// Output will include:
// - Textract API response details
// - Processing times and performance metrics
// - Confidence score distributions
// - Layout element counts
// - Error details and fallback behavior
```

### 2. Mock Data for Development

```typescript
// Create mock highlights for testing UI
const createMockHighlights = (): OCRHighlight[] => [
  {
    id: 'mock-1',
    pageIndex: 0,
    boundingBox: { left: 0.1, top: 0.1, width: 0.8, height: 0.05 },
    text: 'Document Title',
    layoutType: 'TITLE',
    confidence: 0.95,
  },
  {
    id: 'mock-2',
    pageIndex: 0,
    boundingBox: { left: 0.1, top: 0.2, width: 0.8, height: 0.15 },
    text: 'This is a sample paragraph with high confidence extraction.',
    layoutType: 'PARAGRAPH',
    confidence: 0.89,
  },
  // Add more mock data...
];
```

### 3. Cost Monitoring

```typescript
// Track Textract usage for cost monitoring
const trackTextractUsage = async (
  documentId: string,
  pageCount: number,
  processingTime: number
) => {
  const estimatedCost = pageCount * 0.0015; // $0.0015 per page

  console.log(`Textract usage:`, {
    documentId,
    pageCount,
    processingTime,
    estimatedCost: `$${estimatedCost.toFixed(4)}`,
  });

  // Store usage metrics in your analytics system
  await logAnalytics('textract_usage', {
    document_id: documentId,
    page_count: pageCount,
    processing_time_ms: processingTime,
    estimated_cost_usd: estimatedCost,
    timestamp: new Date().toISOString(),
  });
};
```

## Production Considerations

### 1. Rate Limiting

```typescript
// Implement rate limiting for Textract API
const rateLimiter = {
  calls: 0,
  resetTime: Date.now() + 60000, // Reset every minute
  maxCalls: 50, // AWS Textract limit: 50 calls per minute

  async checkLimit() {
    if (Date.now() > this.resetTime) {
      this.calls = 0;
      this.resetTime = Date.now() + 60000;
    }

    if (this.calls >= this.maxCalls) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }

    this.calls++;
  },
};
```

### 2. Error Recovery

```typescript
const processWithRetry = async (
  fileKey: string,
  fileName: string,
  fileType: string,
  maxRetries = 3
) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await processMaterialFromR2Enhanced(fileKey, fileName, fileType);
    } catch (error) {
      console.error(`Processing attempt ${attempt} failed:`, error);

      if (attempt === maxRetries) {
        // Final attempt - use fallback
        console.log('All Textract attempts failed, using fallback processing');
        return await processMaterialFromR2(fileKey, fileName, fileType);
      }

      // Wait before retry (exponential backoff)
      await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};
```

This integration provides a robust, scalable solution for enhanced document processing with visual feedback and interactive features while maintaining backward compatibility and graceful fallbacks.
