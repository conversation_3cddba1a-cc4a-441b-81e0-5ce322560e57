# Knowledge Sphere Database Guide

This document provides comprehensive information about the database architecture, schema, and management procedures for the Knowledge Sphere application.

_Last Updated: June 2025_

## 🗄️ Database Overview

### Technology Stack

- **Database**: PostgreSQL 14+ with pgvector extension
- **ORM**: Drizzle ORM with TypeScript support
- **Migration Tool**: Drizzle Kit for schema management
- **Connection**: Node.js with pg driver

### Key Features

- **Vector Search**: pgvector extension for semantic search capabilities
- **Full-Text Search**: PostgreSQL built-in text search
- **ACID Compliance**: Reliable transactions and data integrity
- **Indexing**: Optimized indexes for performance

## 📊 Database Schema

### Core Entities

#### Users Table

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  password TEXT NOT NULL,
  email_verified BOOLEAN DEFAULT false NOT NULL,
  verification_token TEXT,
  verification_token_expiry TIMESTAMPTZ,
  reset_token TEXT,
  reset_token_expiry TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Spaces Table

```sql
CREATE TABLE spaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  owner_id UUID NOT NULL REFERENCES users(id),
  is_shared BOOLEAN DEFAULT false NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ
);
```

#### Documents Table

```sql
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  file_name TEXT NOT NULL,
  file_type document_type NOT NULL,
  s3_key TEXT NOT NULL UNIQUE,
  uploaded_by UUID NOT NULL REFERENCES users(id),
  summary TEXT,
  summary_embedding VECTOR(1536),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ
);
```

### Relationship Tables

#### Space Members (Many-to-Many)

```sql
CREATE TABLE space_members (
  id SERIAL PRIMARY KEY,
  space_id UUID NOT NULL REFERENCES spaces(id),
  user_id UUID NOT NULL REFERENCES users(id),
  role space_role DEFAULT 'viewer' NOT NULL,
  invitation_id INTEGER REFERENCES space_invitations(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Document Chunks (Vector Storage)

```sql
CREATE TABLE document_chunks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES documents(id),
  content TEXT NOT NULL,
  embedding VECTOR(1536) NOT NULL,
  chunk_index INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔧 Database Management

### Migration Commands

#### Generate New Migration

```bash
# Create a new migration with descriptive name
pnpm run db:generate --name=add_user_preferences

# Interactive CLI will guide you through:
# - Schema changes detection
# - Migration type selection
# - Conflict resolution
```

#### Apply Migrations

```bash
# Apply all pending migrations
pnpm run db:migrate

# This command:
# - Executes migrations in order
# - Updates migration tracking
# - Provides rollback information
```

#### Development Schema Push

```bash
# Direct schema synchronization (development only)
pnpm run db:push

# ⚠️ Warning: Use only in development
# - Bypasses migration system
# - Can cause data loss
# - Not recommended for production
```

#### Database Studio

```bash
# Open Drizzle Studio for visual database management
pnpm run db:studio

# Features:
# - Visual schema browser
# - Data editing interface
# - Query execution
# - Relationship visualization
```

### Migration Best Practices

#### Production Migrations

1. **Always use migrations** in production environments
2. **Test migrations** on staging environment first
3. **Backup database** before applying migrations
4. **Review generated SQL** before execution
5. **Plan for rollback** scenarios

#### Development Workflow

1. **Modify schema** in `src/database/schema/index.ts`
2. **Generate migration** with descriptive name
3. **Review generated SQL** for accuracy
4. **Test migration** locally
5. **Commit migration files** with schema changes

## 🔍 Indexing Strategy

### Performance Indexes

```sql
-- Vector similarity search
CREATE INDEX document_chunks_embedding_idx
ON document_chunks USING hnsw (embedding vector_cosine_ops);

-- Full-text search
CREATE INDEX document_chunks_content_fts_idx
ON document_chunks USING gin (to_tsvector('english', content));

-- User lookups
CREATE INDEX users_email_idx ON users(email);

-- Space access
CREATE INDEX space_members_user_space_idx
ON space_members(user_id, space_id);
```

### Query Optimization

- **Vector Search**: HNSW index for approximate nearest neighbor
- **Text Search**: GIN index for full-text search
- **Foreign Keys**: Automatic indexes on referenced columns
- **Composite Indexes**: Multi-column indexes for common queries

## 🔒 Security & Access Control

### Row-Level Security

```sql
-- Enable RLS on sensitive tables
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE space_members ENABLE ROW LEVEL SECURITY;

-- Example policy for document access
CREATE POLICY document_access_policy ON documents
FOR ALL TO authenticated_users
USING (
  uploaded_by = current_user_id() OR
  id IN (
    SELECT d.id FROM documents d
    JOIN document_spaces ds ON d.id = ds.document_id
    JOIN space_members sm ON ds.space_id = sm.space_id
    WHERE sm.user_id = current_user_id()
  )
);
```

### Data Protection

- **Soft Deletes**: Use `deleted_at` timestamps instead of hard deletes
- **Audit Trails**: Track creation and modification timestamps
- **Access Logging**: Monitor database access patterns
- **Encryption**: Sensitive data encryption at application level

## 📈 Performance Monitoring

### Key Metrics

- **Query Performance**: Monitor slow queries and execution plans
- **Index Usage**: Track index effectiveness and unused indexes
- **Connection Pool**: Monitor connection utilization
- **Vector Search**: Track embedding search performance

### Optimization Techniques

- **Query Analysis**: Use `EXPLAIN ANALYZE` for query optimization
- **Index Maintenance**: Regular `REINDEX` for vector indexes
- **Statistics Update**: Keep table statistics current with `ANALYZE`
- **Connection Pooling**: Optimize connection pool settings

## 🔄 Backup & Recovery

### Backup Strategy

```bash
# Full database backup
pg_dump -h localhost -U username -d knowledge_sphere > backup.sql

# Schema-only backup
pg_dump -h localhost -U username -d knowledge_sphere --schema-only > schema.sql

# Data-only backup
pg_dump -h localhost -U username -d knowledge_sphere --data-only > data.sql
```

### Recovery Procedures

```bash
# Restore full database
psql -h localhost -U username -d knowledge_sphere < backup.sql

# Restore with create database
psql -h localhost -U username < backup.sql
```

## 🚀 Environment Setup

### Local Development

```bash
# Install PostgreSQL with pgvector
brew install postgresql pgvector

# Start PostgreSQL service
brew services start postgresql

# Create database
createdb knowledge_sphere

# Enable pgvector extension
psql -d knowledge_sphere -c "CREATE EXTENSION vector;"
```

### Production Considerations

- **Connection Pooling**: Use PgBouncer or similar
- **Read Replicas**: For read-heavy workloads
- **Monitoring**: Set up database monitoring and alerting
- **Backup Automation**: Automated backup schedules

---

_This database guide covers the essential aspects of managing the Knowledge Sphere database. For schema implementation details, see the source files in `src/database/schema/`._
