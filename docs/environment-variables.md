# Environment Variables Configuration

This document lists all environment variables required for the AWS Textract OCR integration and the knowledge-sphere platform.

## Required Environment Variables

### AWS Configuration

#### AWS Textract Service

```bash
# AWS Region for Textract service (default: us-east-1)
AWS_REGION=us-east-1

# AWS Access Key ID with Textract permissions
AWS_ACCESS_KEY_ID=your_aws_access_key_id

# AWS Secret Access Key
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# AWS S3 for Textract async processing (optional, enables multi-page PDF support)
AWS_S3_BUCKET=knowledge-sphere-textract-temp
AWS_S3_REGION=us-east-1

# AWS SNS for Textract completion notifications (optional, for async processing)
AWS_SNS_TOPIC_ARN=arn:aws:sns:us-east-1:123456789012:textract-completion
AWS_ROLE_ARN=arn:aws:iam::123456789012:role/TextractServiceRole
```

**Required AWS Permissions:**
Your AWS user/role needs the following permissions:

**Basic Textract Permissions:**

- `textract:AnalyzeDocument` - For synchronous document analysis
- `textract:GetDocumentAnalysis` - For async job results retrieval
- `textract:StartDocumentAnalysis` - For async multi-page processing

**S3 Permissions (for async processing):**

- `s3:GetObject` - Read documents from S3
- `s3:PutObject` - Upload documents to S3 for processing
- `s3:DeleteObject` - Cleanup temporary files after processing

**SNS Permissions (for completion notifications):**

- `sns:Publish` - Required for Textract service role to send notifications

#### Sample IAM Policy

**Application Role Policy (for your application):**

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "textract:AnalyzeDocument",
        "textract:GetDocumentAnalysis",
        "textract:StartDocumentAnalysis"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
      "Resource": "arn:aws:s3:::knowledge-sphere-textract-temp/*"
    }
  ]
}
```

**Textract Service Role Policy (for SNS notifications):**

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["sns:Publish"],
      "Resource": "arn:aws:sns:*:*:textract-completion"
    }
  ]
}
```

### Existing Platform Variables

#### Azure OpenAI (for embeddings and chat)

```bash
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_RESOURCE_NAME=your-resource-name
AZURE_OPENAI_API_KEY=your_api_key
```

#### Google AI (alternative LLM provider)

```bash
GOOGLE_GENERATIVE_AI_API_KEY=your_google_api_key
```

#### Cloudflare R2 (document storage)

```bash
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_ACCESS_KEY_ID=your_access_key_id
CLOUDFLARE_SECRET_ACCESS_KEY=your_secret_access_key
CLOUDFLARE_BUCKET_NAME=your_bucket_name
```

#### Database

```bash
DATABASE_URL=postgres://admin:password@localhost:5432/knowledge_sphere
```

#### Email (Resend)

```bash
RESEND_API_KEY=your_resend_api_key
FROM_EMAIL=<EMAIL>
```

#### Application

```bash
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
# NEXT_PUBLIC_APP_URL=local_url
NEXT_PUBLIC_MATERIAL_ACCESS_URL=https://your-cdn-url
```

## Environment Variable Validation

The platform uses `@t3-oss/env-nextjs` for environment variable validation. All variables are validated at build time.

### Schema Location

Environment variables are validated in `/src/lib/env.mjs`

### Adding New Variables

To add new environment variables:

1. Add to the schema in `env.mjs`:

```javascript
export const env = createEnv({
  server: {
    // Add new server variables here
    NEW_API_KEY: z.string().min(1),
  },
  client: {
    // Add new client variables here (must start with NEXT_PUBLIC_)
    NEXT_PUBLIC_NEW_CONFIG: z.string().min(1),
  },
});
```

2. Reference in your code:

```typescript
import { env } from '@/lib/env.mjs';

const apiKey = env.NEW_API_KEY;
```

## Development Setup

### 1. Copy Environment File

```bash
cp .env.example .env.local
```

### 2. Configure AWS Credentials

**Option A: Environment Variables**
Set the AWS variables in your `.env.local` file.

**Option B: AWS Profile**
Use AWS CLI configuration:

```bash
aws configure --profile knowledge-sphere
# Enter your credentials when prompted
```

Then reference the profile:

```bash
AWS_PROFILE=knowledge-sphere
```

**Option C: IAM Roles (Production)**
For production deployment, use IAM roles attached to EC2/ECS/Lambda instead of access keys.

### 3. Test Configuration

Run the type checking to ensure all environment variables are properly configured:

```bash
npm run check-types
```

## Production Deployment

### Security Best Practices

1. **Never commit secrets** to version control
2. **Use IAM roles** instead of access keys when possible
3. **Rotate credentials** regularly
4. **Use environment-specific** configuration
5. **Monitor usage** and costs

### AWS Cost Considerations

AWS Textract pricing (as of 2024):

- **AnalyzeDocument API**: ~$0.0015 per page
- **Layout Analysis**: Included in base price
- **No minimum charges**

**Cost Optimization Tips:**

- Cache Textract results to avoid re-processing
- Monitor usage with CloudWatch
- Set up billing alerts
- Use appropriate confidence thresholds

### Monitoring

Set up monitoring for:

- Textract API calls and costs
- Processing success/failure rates
- Document processing times
- Error rates and types

### Environment-Specific Configs

#### Development

```bash
AWS_REGION=us-east-1
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
```

#### Staging

```bash
AWS_REGION=us-east-1
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=staging
```

#### Production

```bash
AWS_REGION=us-east-1
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production
```

## Troubleshooting

### Common Issues

#### 1. AWS Credentials Error

```
Error: The security token included in the request is invalid
```

**Solution**: Check AWS credentials and permissions

#### 2. Textract Service Unavailable

```
Error: Textract analysis failed: Service unavailable
```

**Solution**: Check AWS region availability and service status

#### 3. Environment Variable Missing

```
Error: Invalid environment variables
```

**Solution**: Ensure all required variables are set in your `.env.local`

#### 4. Database Migration Issues

```
Error: relation "ocr_analysis" does not exist
```

**Solution**: Run database migrations:

```bash
npm run db:migrate
```

### Debug Mode

Enable debug logging for OCR processing:

```typescript
// In your processing code
const result = await processMaterialFromR2Enhanced(
  fileKey,
  fileName,
  fileType,
  true // Enable debug mode
);
```

This will log detailed information about:

- Textract API calls and responses
- Processing times and performance
- Confidence scores and layout analysis
- Error details and fallback behavior

## Support

For issues with:

- **AWS Textract**: Check AWS documentation and support
- **Environment configuration**: Review this guide and check validation errors
- **Platform integration**: Check application logs and debug mode output
