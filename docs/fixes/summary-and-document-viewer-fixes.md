# Summary and Document Viewer Fixes

## Issues Fixed

### 1. MaterialSummary Not Showing All Content

#### Problem

The MaterialSummary component on the material page was not showing the complete summary content by default. Users had to click "Expand" to see the full summary, which was not intuitive.

#### Root Cause

The component was initialized with `isExpanded = false`, meaning only the executive summary was visible by default, with all other content (detailed summary, chapters, key topics, metadata) hidden behind the expand button.

#### Solution

Changed the default state to show all content immediately:

```typescript
// Before: Started collapsed
const [isExpanded, setIsExpanded] = useState(false);

// After: Start expanded to show all content
const [isExpanded, setIsExpanded] = useState(true);
```

#### Benefits

- **Immediate access**: Users see all summary content without additional clicks
- **Better UX**: No hidden content that users might miss
- **Consistent behavior**: Matches the expectation that summaries should be fully visible
- **Maintains functionality**: Users can still collapse if they want to save space

### 2. Document Viewer Not Working in Chat

#### Problem

The document viewer in the chat page was not working when users clicked on chunk badges. The PDF viewer showed errors or failed to load documents.

#### Root Cause

**URL Generation Issue**: The chat document viewer was using a different URL generation method than the working material page:

- **Material page** (working): `${env.NEXT_PUBLIC_MATERIAL_ACCESS_URL}/${material.s3Key}`
- **Chat page** (broken): `getDocumentPreviewUrl(selectedReference.key)` with inconsistent environment variable access

#### Solution

Fixed the `getDocumentPreviewUrl` function to use the same URL pattern as the material page:

```typescript
export function getDocumentPreviewUrl(key: string) {
  // Use the same URL pattern as the material page
  // NEXT_PUBLIC_ variables are available on both client and server
  const baseUrl = process.env.NEXT_PUBLIC_MATERIAL_ACCESS_URL;
  if (!baseUrl) {
    console.error('NEXT_PUBLIC_MATERIAL_ACCESS_URL is not defined');
    return '';
  }
  const fullUrl = `${baseUrl}/${key}`;
  console.log('Generated document URL:', fullUrl, 'for key:', key);
  return fullUrl;
}
```

#### Additional Improvements

- **Error handling**: Added proper error checking for missing environment variables
- **Debug logging**: Added console logging to help troubleshoot URL generation
- **Consistency**: Ensured both material page and chat use the same URL format

## Technical Details

### Environment Variable Handling

The fix ensures consistent access to `NEXT_PUBLIC_MATERIAL_ACCESS_URL`:

- **Material page**: Uses `env.NEXT_PUBLIC_MATERIAL_ACCESS_URL` (server-side env object)
- **Chat page**: Now uses `process.env.NEXT_PUBLIC_MATERIAL_ACCESS_URL` (standard env access)
- **Both approaches**: Access the same environment variable value

### URL Format Consistency

Both implementations now generate URLs in the format:

```
${NEXT_PUBLIC_MATERIAL_ACCESS_URL}/${documentKey}
```

### Debug Information

Added logging to help troubleshoot issues:

- URL generation logging in `getDocumentPreviewUrl`
- PDF loading logging in `PDFViewer` component
- Error details in PDF viewer error states

## Testing Verification

### MaterialSummary Testing

1. **Navigate to any material page**: `/materials/[material_id]`
2. **Check summary display**: Should show all sections immediately
   - Executive Summary
   - Detailed Summary
   - Chapter Summaries
   - Key Topics
   - Document Metadata
3. **Test collapse/expand**: Button should still work to hide/show content

### Document Viewer Testing

1. **Open chat page**: Navigate to chat interface
2. **Trigger AI response**: Ask a question that retrieves documents
3. **Click chunk badges**: Should open document viewer with correct document
4. **Check console**: Should see URL generation logs
5. **Verify PDF loading**: Documents should load without "Invalid PDF structure" errors

### URL Verification

Check browser console for logs like:

```
Generated document URL: https://your-domain.com/path/document-key for key: document-key
PDFViewer: Loading PDF from URL: https://your-domain.com/path/document-key
```

## Benefits

### For MaterialSummary

1. **Better UX**: Users immediately see all available summary information
2. **Reduced friction**: No need to click expand to access content
3. **Consistent expectations**: Summaries are fully visible as expected
4. **Maintained flexibility**: Users can still collapse if desired

### For Document Viewer

1. **Functional chat integration**: Clickable badges now work properly
2. **Consistent behavior**: Same document viewing experience across the app
3. **Better debugging**: Console logs help identify issues quickly
4. **Reliable URL generation**: Proper environment variable handling

## Future Improvements

### MaterialSummary

1. **User preference**: Remember user's expand/collapse preference
2. **Smart defaults**: Auto-collapse for very long summaries
3. **Progressive disclosure**: Show summary sections progressively
4. **Search within summary**: Find specific content in long summaries

### Document Viewer

1. **Remove debug logs**: Clean up console logging for production
2. **Retry mechanism**: Auto-retry failed document loads
3. **Offline support**: Cache documents for offline viewing
4. **Performance optimization**: Lazy load large documents

## Configuration Notes

Ensure your environment variables are properly set:

```env
NEXT_PUBLIC_MATERIAL_ACCESS_URL=https://your-cloudflare-r2-domain.com
```

This URL should point to your Cloudflare R2 bucket or CDN endpoint where documents are stored.
