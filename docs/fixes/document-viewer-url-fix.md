# Document Viewer URL Fix

## Problem

The document viewer in chat and search pages was not working because it was using `getDocumentPreviewUrl()` which relied on `process.env.NEXT_PUBLIC_MATERIAL_ACCESS_URL` in client components. However, the working material page used `env.NEXT_PUBLIC_MATERIAL_ACCESS_URL` from the server-side env object.

## Root Cause

**Client vs Server Environment Variable Access**:

- **Material page** (working): `${env.NEXT_PUBLIC_MATERIAL_ACCESS_URL}/${material.s3Key}` - server component
- **Chat/Search pages** (broken): `getDocumentPreviewUrl(key)` using `process.env` - client components

The issue was that client components couldn't reliably access the environment variable the same way server components do.

## Solution

**Pass the URL from server components to client components**, following the same pattern as the working material page.

### 1. Chat Page Fix

#### Server Component (page.tsx)

```typescript
// Import server-side env
import { env } from '@/lib/env.mjs';

// Pass URL to client component
<ChatUIEnhanced
  initialTopic={topic}
  materialAccessUrl={env.NEXT_PUBLIC_MATERIAL_ACCESS_URL}
/>
```

#### Client Component (chat-ui-enhanced.tsx)

```typescript
// Accept URL as prop
type ChatUIEnhancedProps = {
  initialTopic: TopicWithDocuments;
  materialAccessUrl: string;
};

// Use passed URL directly (same pattern as material page)
<DocumentViewer
  url={selectedReference ? `${materialAccessUrl}/${selectedReference.key}` : ''}
  fileType={selectedReference?.fileType || DocumentType.PDF}
/>
```

### 2. Search Page Fix

#### Server Component (page.tsx)

```typescript
// Import server-side env
import { env } from '@/lib/env.mjs';

// Pass URL to client component
<SearchPage
  // ... other props
  materialAccessUrl={env.NEXT_PUBLIC_MATERIAL_ACCESS_URL}
/>
```

#### Client Component (search-page.tsx)

```typescript
// Accept URL as prop
type SearchPageProps = {
  // ... other props
  materialAccessUrl: string;
};

// Use passed URL directly (same pattern as material page)
<DocumentViewer
  url={`${materialAccessUrl}/${selectedDocument.key}`}
  fileType={selectedDocument.fileType}
/>
```

## Files Changed

### Chat Implementation

1. **`src/app/(main)/chat/[chatId]/enhanced/page.tsx`**

   - Added `env` import
   - Passed `materialAccessUrl` prop to `ChatUIEnhanced`

2. **`src/components/chat/chat-ui-enhanced.tsx`**
   - Added `materialAccessUrl` to props type
   - Updated `DocumentViewer` to use passed URL
   - Removed `getDocumentPreviewUrl` import
   - Fixed React Hook dependency warning

### Search Implementation

1. **`src/app/(main)/search/page.tsx`**

   - Added `env` import
   - Passed `materialAccessUrl` prop to `SearchPage`

2. **`src/components/search/search-page.tsx`**
   - Added `materialAccessUrl` to props type
   - Updated `DocumentViewer` to use passed URL
   - Removed `getDocumentPreviewUrl` import

## Benefits

### Consistency

- **Same URL pattern**: All pages now use `${baseUrl}/${key}` format
- **Same data source**: All pages use server-side `env.NEXT_PUBLIC_MATERIAL_ACCESS_URL`
- **Same behavior**: Document viewing works identically across the app

### Reliability

- **Server-side env access**: Reliable environment variable access
- **No client-side env issues**: Eliminates potential client-side environment variable problems
- **Proven pattern**: Uses the same approach as the working material page

### Maintainability

- **Single source of truth**: Environment variable accessed once per page
- **Clear data flow**: URL passed explicitly from server to client
- **Type safety**: Props are properly typed

## Testing

### Chat Page Testing

1. **Navigate to chat**: `/chat/[chatId]/enhanced`
2. **Ask a question**: Trigger AI response with document retrieval
3. **Click chunk badges**: Should open document viewer
4. **Verify PDF loads**: Documents should display correctly

### Search Page Testing

1. **Navigate to search**: `/search`
2. **Search for content**: Find documents
3. **Click search results**: Should open document viewer
4. **Verify PDF loads**: Documents should display correctly

### URL Verification

Check browser console for logs:

```
Generated document URL: https://your-domain.com/path/document-key for key: document-key
PDFViewer: Loading PDF from URL: https://your-domain.com/path/document-key
```

## Future Improvements

### Code Cleanup

1. **Remove `getDocumentPreviewUrl`**: Function is no longer needed
2. **Remove debug logs**: Clean up console logging for production
3. **Standardize pattern**: Apply same pattern to any other document viewers

### Environment Variable Management

1. **Validation**: Add runtime validation for required environment variables
2. **Fallbacks**: Provide fallback URLs for development
3. **Documentation**: Document all required environment variables

## Configuration

Ensure your environment variable is properly set:

```env
NEXT_PUBLIC_MATERIAL_ACCESS_URL=https://your-cloudflare-r2-domain.com
```

This URL should point to your Cloudflare R2 bucket or CDN endpoint where documents are stored.

## Summary

The fix ensures that:

- ✅ **Chat document viewer works** - clicking badges opens documents
- ✅ **Search document viewer works** - clicking results opens documents
- ✅ **Consistent behavior** - same URL generation across all pages
- ✅ **Reliable access** - server-side environment variable access
- ✅ **Type safety** - proper TypeScript types for all props
