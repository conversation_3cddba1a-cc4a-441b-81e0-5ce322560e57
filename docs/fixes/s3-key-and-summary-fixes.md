# S3 Key and Summary Content Fixes

## Issues Identified and Fixed

### 1. S3 Key Already Available in Search Results ✅

#### Investigation Results

Upon investigation, the search functions were already correctly returning the S3 key:

**Material Search (chunks)**:

```typescript
// src/lib/materials/search.ts - Line 125
.select({
  id: documents.id,
  chunkId: documentChunks.id,
  fileName: documents.fileName,
  content: documentChunks.content,
  key: documents.s3Key,  // ✅ S3 key already included
  similarity: similarity,
  fileType: documents.fileType,
  metadata: documentChunks.metadata,
  uploadedBy: documents.uploadedBy,
})
```

**Summary Search**:

```typescript
// src/lib/materials/summary-search.ts - Line 75
.select({
  id: documents.id,
  fileName: documents.fileName,
  summary: documents.summary,
  key: documents.s3Key,  // ✅ S3 key already included
  fileType: documents.fileType,
  uploadedBy: documents.uploadedBy,
  rank: sql<number>`ts_rank(...)`,
})
```

**Type Definitions**:

```typescript
// Both types already have the key field
export type MaterialSearchResult = {
  id: string;
  chunkId: number;
  fileName: string;
  key: string; // ✅ S3 key field exists
  content: string;
  // ...
};

export interface DocumentSummary {
  id: string;
  fileName: string;
  summary: string;
  similarity?: number;
  key: string; // ✅ S3 key field exists
  fileType: string;
}
```

**Conclusion**: The S3 keys were already being returned correctly. The document viewer issue was due to URL generation problems (which we fixed earlier), not missing S3 keys.

### 2. Full Summary Content via tRPC ✅

#### Problem

The agent steps were not storing the complete summary content to avoid bloating the database. Users clicking summary badges only saw partial content from the agent steps.

#### Solution

Created a new tRPC endpoint to fetch the complete summary content on demand.

#### Implementation

**New tRPC Endpoint**:

```typescript
// src/server/trpc/routers/material.ts
getSummary: protectedProcedure.input(z.string()).query(async ({ ctx, input }) => {
  const material = await MaterialRepository.getMaterialById(input);
  if (!material || material.length === 0) {
    throw new TRPCError({ code: 'NOT_FOUND', message: 'Material not found' });
  }

  const doc = material[0];

  // Check if user has access to this document
  // Either they own it, or it's in a space they have access to
  if (doc.uploadedBy !== ctx.user.id) {
    // Check if document is in any accessible spaces
    const userSpaces = await SpaceRepository.getAllSpacesForUser(ctx.user.id);
    const userSpaceIds = userSpaces.map(s => s.id);

    const docSpaces = await db
      .select({ spaceId: documentSpaces.spaceId })
      .from(documentSpaces)
      .where(eq(documentSpaces.documentId, input));

    const hasAccess = docSpaces.some(ds => userSpaceIds.includes(ds.spaceId));

    if (!hasAccess) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to access this material',
      });
    }
  }

  return {
    id: doc.id,
    fileName: doc.fileName,
    summary: doc.summary,
    key: doc.s3Key,
    fileType: doc.fileType,
  };
}),
```

**Updated SummaryViewer Component**:

```typescript
// src/components/chat/summary-viewer.tsx
export function SummaryViewer({ summary, isOpen, onClose }: SummaryViewerProps) {
  const { setSelectedReference } = useReferenceStore();

  // Fetch the full summary content using tRPC
  const {
    data: fullSummaryData,
    isLoading,
    error,
  } = clientApi.material.getSummary.useQuery(summary?.id || '', {
    enabled: !!summary?.id && isOpen, // Only fetch when we have an ID and dialog is open
  });

  // Use the full summary from tRPC if available, otherwise fall back to the summary from agent steps
  const summaryContent = fullSummaryData?.summary || summary.summary;
  const parsedSummary = parseSummary(summaryContent);

  // ... rest of component uses summaryContent for display
}
```

## Benefits

### Performance Optimization

- **Agent steps remain lightweight**: No large summary content stored in agent_steps
- **On-demand loading**: Full summaries only fetched when users click summary badges
- **Efficient caching**: tRPC automatically caches summary data

### Security & Access Control

- **Proper authorization**: Checks user ownership or space access before returning summary
- **Consistent permissions**: Uses same access control logic as other material endpoints
- **Type safety**: Full TypeScript support for the new endpoint

### User Experience

- **Complete content**: Users see the full summary, not truncated versions
- **Loading states**: Clear feedback while summary is being fetched
- **Error handling**: Graceful fallback to partial content if fetch fails
- **Seamless integration**: Works with existing summary viewer UI

## Technical Details

### tRPC Integration

- **Endpoint**: `material.getSummary`
- **Input**: Document ID (string)
- **Output**: Complete document summary with metadata
- **Caching**: Automatic query caching via React Query

### Access Control Logic

1. **Check ownership**: If user owns the document, grant access
2. **Check space access**: If user has access to any space containing the document, grant access
3. **Deny access**: If neither condition is met, return 403 Forbidden

### Error Handling

- **Not found**: Returns 404 if document doesn't exist
- **Forbidden**: Returns 403 if user lacks access
- **Loading states**: Shows spinner while fetching
- **Fallback content**: Uses partial summary from agent steps if fetch fails

## Testing

### Summary Content Testing

1. **Click summary badge**: Should show loading spinner, then complete summary
2. **Check content completeness**: Verify all summary sections are displayed
3. **Test access control**: Ensure users can only access summaries they have permission for
4. **Test error states**: Verify graceful handling of network errors

### Performance Testing

1. **Initial load**: Summary badges should appear immediately (using agent step data)
2. **On-demand fetch**: Full content should load quickly when badge is clicked
3. **Caching**: Subsequent clicks should use cached data

## Future Improvements

### Performance Enhancements

1. **Prefetching**: Preload summaries for visible badges
2. **Compression**: Compress large summary content
3. **Pagination**: For extremely long summaries, implement pagination

### Feature Enhancements

1. **Summary editing**: Allow users to edit summaries
2. **Summary regeneration**: Regenerate summaries with updated AI models
3. **Summary sharing**: Share summaries independently of documents
4. **Summary search**: Search within summary content specifically

## Summary

Both issues have been resolved:

1. **✅ S3 Keys**: Were already correctly included in search results and types
2. **✅ Full Summary Content**: Now fetched on-demand via tRPC with proper access control

The document viewer should now work correctly with the proper S3 keys, and users will see complete summary content when clicking summary badges.
