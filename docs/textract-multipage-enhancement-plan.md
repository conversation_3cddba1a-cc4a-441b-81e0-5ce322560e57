# AWS Textract Multi-Page PDF Enhancement Plan

## Executive Summary

This document outlines the comprehensive enhancement plan to upgrade the knowledge-sphere platform's AWS Textract integration from synchronous single-page processing to asynchronous multi-page PDF processing. The enhancement leverages AWS's StartDocumentAnalysis/GetDocumentAnalysis APIs with SNS notifications, integrated into the existing Upstash workflow system.

## Problem Statement

### Current Limitations

- **Single-Page Constraint**: Current implementation uses synchronous `AnalyzeDocumentCommand` which works well for single-page PDFs but fails or provides suboptimal results for multi-page documents
- **Manual PDF Splitting**: The current workaround splits multi-page PDFs into individual pages, losing cross-page context and document structure
- **Performance Issues**: Large documents cause timeout issues due to synchronous processing
- **Processing Inefficiency**: Page-by-page processing is slower and more expensive than native multi-page processing

### User Impact

- Poor text extraction quality for multi-page documents
- Missing cross-page elements (tables, paragraphs spanning pages)
- Slower processing times for complex documents
- Higher AWS Textract costs due to inefficient processing

## Current State Analysis

### Existing Textract Implementation (`src/lib/aws/textract.ts`)

- **Synchronous Processing**: Uses `AnalyzeDocumentCommand` for immediate results
- **Fallback Strategy**: Splits PDFs when single-page analysis fails
- **Feature Support**: Already supports LAYOUT feature with geometry data
- **Error Handling**: Robust error handling with confidence scoring

### Existing Infrastructure Assets

- **Upstash Workflow System**: 5-step document processing pipeline
- **Database Schema**: Ready with `ocrAnalysis.textractJobId` field for async jobs
- **Status Tracking**: Granular processing status (`ocr_extracting`, `embedding`, etc.)
- **Storage System**: Cloudflare R2 for primary storage

### Integration Points

- **Workflow Context**: Uses `WorkflowContext` with `context.run()` for step management
- **Error Recovery**: Built-in failure handling and retry mechanisms
- **Real-time Status**: UI components for processing status tracking

## Technical Solution

### Architecture Overview

```mermaid
graph TD
    A[Multi-page PDF Upload] --> B[Store in Cloudflare R2]
    B --> C[Copy to AWS S3 temporarily]
    C --> D[Start Async Textract Job]
    D --> E[Upstash Workflow waitForEvent]
    E --> F[AWS SNS Notification]
    F --> G[Webhook Endpoint]
    G --> H[Resume Workflow]
    H --> I[Get Textract Results]
    I --> J[Process & Store Data]
    J --> K[Cleanup S3 Files]
    K --> L[Complete Processing]
```

### Key Technical Decisions

#### 1. R2 ↔ S3 Bridge Strategy

**Decision**: Implement bridge approach rather than full S3 migration

- **Rationale**: Preserve existing R2 infrastructure and cost benefits
- **Implementation**: Temporary S3 copies only for Textract processing
- **Cleanup**: Automated removal of S3 files after processing

#### 2. Upstash waitForEvent Integration

**Decision**: Use existing workflow infrastructure with `context.waitForEvent()`

- **Rationale**: Cleaner integration with existing job tracking
- **Benefits**: Single workflow system, built-in error handling
- **Alternative Rejected**: Separate SNS/SQS queue system (adds complexity)

#### 3. Backward Compatibility

**Decision**: Maintain all existing synchronous capabilities

- **Rationale**: Ensure zero-downtime deployment and reliability
- **Fallback**: Smart routing based on document characteristics
- **Migration**: Gradual rollout with feature flags

## Implementation Plan

### Phase 1: AWS S3 Bridge Infrastructure (Days 1-2)

#### 1.1 S3 Service Setup

**File**: `src/lib/aws/s3.ts` (new)

```typescript
export class S3Bridge {
  async copyFromR2ToS3(r2Key: string): Promise<string>;
  async cleanupS3File(s3Key: string): Promise<void>;
  async scheduleCleanup(s3Key: string, delayMs: number): Promise<void>;
}
```

#### 1.2 Environment Configuration

**Files**: `.env.example`, `src/lib/env.mjs`

```env
# AWS S3 for Textract processing
AWS_S3_BUCKET=your-textract-bucket
AWS_S3_REGION=us-east-1

# SNS Configuration
AWS_SNS_TOPIC_ARN=arn:aws:sns:region:account:textract-completion
AWS_ROLE_ARN=arn:aws:iam::account:role/TextractServiceRole
```

#### 1.3 Database Schema Updates

**File**: `src/database/migrations/0016_add_s3_tracking.sql`

```sql
-- Add S3 tracking to documents table
ALTER TABLE documents ADD COLUMN aws_s3_key text;
ALTER TABLE documents ADD COLUMN s3_cleanup_scheduled_at timestamp;

-- Index for cleanup jobs
CREATE INDEX idx_documents_s3_cleanup ON documents(s3_cleanup_scheduled_at)
WHERE s3_cleanup_scheduled_at IS NOT NULL;
```

### Phase 2: Async Textract API Integration (Days 3-5)

#### 2.1 Enhanced OCR Analysis Schema

**File**: `src/database/migrations/0017_enhance_ocr_analysis.sql`

```sql
-- Enhance ocrAnalysis for async processing
ALTER TABLE ocr_analysis ADD COLUMN job_status text;
ALTER TABLE ocr_analysis ADD COLUMN sns_message_id text;
ALTER TABLE ocr_analysis ADD COLUMN started_at timestamp;
ALTER TABLE ocr_analysis ADD COLUMN completed_at timestamp;
ALTER TABLE ocr_analysis ADD COLUMN page_count integer;

-- Indexes for async job management
CREATE INDEX idx_ocr_analysis_job_status ON ocr_analysis(job_status);
CREATE INDEX idx_ocr_analysis_textract_job_id ON ocr_analysis(textract_job_id);
```

#### 2.2 Async Textract Functions

**File**: `src/lib/aws/textract.ts` (enhance existing)

```typescript
// Add alongside existing functions
export async function startAsyncDocumentAnalysis(
  s3Bucket: string,
  s3Key: string,
  snsTopicArn: string,
  roleArn: string
): Promise<string>; // Returns JobId

export async function getAsyncDocumentAnalysis(jobId: string): Promise<TextractResult>;

export async function checkAsyncJobStatus(
  jobId: string
): Promise<'IN_PROGRESS' | 'SUCCEEDED' | 'FAILED' | 'PARTIAL_SUCCESS'>;
```

#### 2.3 Smart Processing Logic

**File**: `src/lib/aws/textract.ts` (enhance existing `analyzeDocumentWithTextract`)

```typescript
export async function analyzeDocumentWithTextract(
  documentBytes: Buffer,
  debug = false,
  forceAsync = false
): Promise<TextractResult> {
  // Determine processing strategy
  const pageCount = await getPageCount(documentBytes);
  const shouldUseAsync = forceAsync || (pageCount > 1 && pageCount <= 100);

  if (shouldUseAsync) {
    try {
      return await processAsyncTextract(documentBytes);
    } catch (error) {
      console.warn('Async processing failed, falling back to sync', error);
      return await processCurrentMethod(documentBytes);
    }
  } else {
    return await processSyncTextract(documentBytes);
  }
}
```

### Phase 3: SNS Webhook + Workflow Integration (Days 6-8)

#### 3.1 SNS Webhook Endpoint

**File**: `src/app/api/webhooks/textract-completion/route.ts` (new)

```typescript
export async function POST(request: NextRequest) {
  // Verify SNS signature
  // Parse SNS message
  // Extract JobId and status
  // Trigger waitForEvent completion
  // Update database status
}
```

#### 3.2 Workflow Integration

**File**: `src/lib/workflows/document-processing.ts` (enhance Step 2)

```typescript
export async function step2OCRExtraction(
  payload: DocumentProcessingPayload,
  context: WorkflowContext
): Promise<StepResult> {
  // Existing sync processing logic...

  // For multi-page PDFs, use async processing
  if (shouldUseAsyncProcessing) {
    // Copy to S3
    const s3Key = await copyToS3(fileKey);

    // Start async job
    const jobId = await startAsyncTextractJob(s3Key);
    await storeTextractJobId(documentId, jobId);

    // Wait for completion
    const completionEvent = await context.waitForEvent('textract-completion', {
      timeout: '30m',
      filter: { jobId, documentId },
    });

    // Retrieve results
    const results = await getTextractResults(jobId);

    // Schedule S3 cleanup
    await scheduleS3Cleanup(s3Key);

    return { success: true, data: { chunks: results.chunks } };
  }

  // Existing sync processing...
}
```

#### 3.3 Event Management

**File**: `src/lib/workflows/event-manager.ts` (new)

```typescript
export class WorkflowEventManager {
  static async triggerTextractCompletion(
    jobId: string,
    documentId: string,
    status: string
  ): Promise<void>;

  static async handleTextractFailure(
    jobId: string,
    documentId: string,
    error: string
  ): Promise<void>;
}
```

### Phase 4: S3 Cleanup & Monitoring (Day 9)

#### 4.1 Cleanup Service

**File**: `src/lib/aws/s3-cleanup.ts` (new)

```typescript
export class S3CleanupService {
  async scheduleCleanup(s3Key: string, delayMinutes = 60): Promise<void>;
  async processCleanupQueue(): Promise<void>;
  async emergencyCleanup(olderThanHours = 24): Promise<void>;
}
```

#### 4.2 Monitoring & Logging

**File**: `src/lib/monitoring/textract-metrics.ts` (new)

```typescript
export class TextractMetrics {
  static trackAsyncJobStart(jobId: string, pageCount: number): void;
  static trackAsyncJobComplete(jobId: string, duration: number): void;
  static trackFallbackUsage(reason: string): void;
  static trackS3Usage(operation: 'copy' | 'cleanup'): void;
}
```

### Phase 5: Testing & Documentation (Day 10)

#### 5.1 Test Coverage

- **Unit Tests**: Async Textract functions
- **Integration Tests**: End-to-end workflow
- **Load Tests**: Multiple concurrent async jobs
- **Fallback Tests**: Error scenarios

#### 5.2 Documentation Updates

- **API Documentation**: New webhook endpoints
- **Environment Setup**: S3 and SNS configuration
- **Troubleshooting Guide**: Common async processing issues
- **Migration Guide**: Feature rollout strategy

## Database Schema Changes

### Documents Table Enhancements

```sql
-- Phase 1: S3 Bridge Support
ALTER TABLE documents ADD COLUMN aws_s3_key text;
ALTER TABLE documents ADD COLUMN s3_cleanup_scheduled_at timestamp;

-- Indexes
CREATE INDEX idx_documents_s3_cleanup ON documents(s3_cleanup_scheduled_at)
WHERE s3_cleanup_scheduled_at IS NOT NULL;
```

### OCR Analysis Table Enhancements

```sql
-- Phase 2: Async Job Tracking
ALTER TABLE ocr_analysis ADD COLUMN job_status text; -- 'IN_PROGRESS', 'SUCCEEDED', 'FAILED'
ALTER TABLE ocr_analysis ADD COLUMN sns_message_id text;
ALTER TABLE ocr_analysis ADD COLUMN started_at timestamp;
ALTER TABLE ocr_analysis ADD COLUMN completed_at timestamp;
ALTER TABLE ocr_analysis ADD COLUMN page_count integer;

-- Performance indexes
CREATE INDEX idx_ocr_analysis_job_status ON ocr_analysis(job_status);
CREATE INDEX idx_ocr_analysis_textract_job_id ON ocr_analysis(textract_job_id);
```

## Environment Configuration

### Required Environment Variables

```env
# Existing AWS Textract
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# New S3 Bridge Configuration
AWS_S3_BUCKET=knowledge-sphere-textract-temp
AWS_S3_REGION=us-east-1

# SNS Configuration for Async Processing
AWS_SNS_TOPIC_ARN=arn:aws:sns:us-east-1:123456789012:textract-completion
AWS_ROLE_ARN=arn:aws:iam::123456789012:role/TextractServiceRole

# Webhook Security
TEXTRACT_WEBHOOK_SECRET=your-webhook-secret
```

### AWS IAM Permissions

#### Textract Service Role

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["sns:Publish"],
      "Resource": "arn:aws:sns:*:*:textract-completion"
    }
  ]
}
```

#### Application Role Enhancements

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["textract:StartDocumentAnalysis", "textract:GetDocumentAnalysis"],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject"],
      "Resource": "arn:aws:s3:::knowledge-sphere-textract-temp/*"
    }
  ]
}
```

## Architecture Benefits

### Scalability Improvements

- **Parallel Processing**: Multiple documents can be processed simultaneously
- **Resource Efficiency**: Async processing doesn't tie up server resources
- **Cost Optimization**: Native multi-page processing reduces API calls

### Reliability Enhancements

- **Fault Tolerance**: SNS ensures reliable completion notifications
- **Graceful Degradation**: Fallback to existing sync processing
- **Error Recovery**: Built-in retry mechanisms and error tracking

### Performance Gains

- **Faster Processing**: Native multi-page analysis vs. page splitting
- **Better Accuracy**: Maintains document context across pages
- **Reduced Timeouts**: Async processing eliminates timeout issues

## Risk Mitigation

### Technical Risks

1. **AWS Service Dependencies**: Mitigated by fallback to existing sync processing
2. **Network Failures**: SNS retry mechanisms and webhook reliability
3. **Storage Costs**: Automated S3 cleanup and monitoring

### Operational Risks

1. **Complex Debugging**: Enhanced logging and monitoring systems
2. **Configuration Errors**: Comprehensive environment validation
3. **Migration Issues**: Gradual rollout with feature flags

### Cost Management

1. **S3 Storage**: Automatic cleanup after 1 hour
2. **Textract Usage**: Smart routing to avoid unnecessary async calls
3. **SNS Charges**: Minimal cost impact, monitored usage

## Success Metrics

### Performance Metrics

- **Processing Time**: Reduction in overall document processing time
- **Success Rate**: Percentage of successfully processed multi-page PDFs
- **Error Rate**: Reduction in OCR-related failures

### Quality Metrics

- **Extraction Accuracy**: Improvement in text extraction quality
- **Context Preservation**: Better handling of cross-page elements
- **User Satisfaction**: Reduced support tickets for PDF processing issues

### Operational Metrics

- **System Reliability**: Uptime and availability metrics
- **Cost Efficiency**: AWS usage costs vs. processing volume
- **Monitoring Coverage**: Error detection and alerting effectiveness

## Deployment Strategy

### Phase 1: Infrastructure Setup

- Deploy S3 bridge components
- Configure SNS topics and roles
- Run database migrations

### Phase 2: Feature Flag Rollout

- Enable async processing for test documents
- Monitor performance and error rates
- Gradual rollout to larger document sets

### Phase 3: Full Production

- Enable for all multi-page PDFs
- Monitor and optimize performance
- Documentation and training updates

## Monitoring & Maintenance

### Real-time Monitoring

- **Async Job Status**: Track job progression and completion rates
- **S3 Usage**: Monitor storage usage and cleanup effectiveness
- **Error Tracking**: Alert on processing failures and retry attempts

### Regular Maintenance

- **Database Cleanup**: Archive old OCR analysis records
- **Performance Tuning**: Optimize async job parameters
- **Cost Review**: Monthly AWS usage analysis

### Alerting Strategy

- **Critical**: Processing failures, webhook downtime
- **Warning**: High error rates, S3 cleanup delays
- **Info**: Usage metrics, performance trends

## Future Enhancements

### Short-term (3 months)

- **Performance Optimization**: Fine-tune async job parameters
- **Enhanced Monitoring**: Advanced metrics and dashboards
- **Cost Optimization**: Smart caching and processing strategies

### Medium-term (6 months)

- **Multi-region Support**: Geographic distribution for faster processing
- **Advanced Features**: Form extraction, table analysis improvements
- **Batch Processing**: Bulk document processing capabilities

### Long-term (12 months)

- **ML Optimization**: Document-specific processing strategies
- **Real-time Processing**: Ultra-low latency for priority documents
- **Advanced Analytics**: Processing pattern analysis and optimization

## Conclusion

This enhancement plan provides a comprehensive solution for multi-page PDF processing while maintaining system reliability and leveraging existing infrastructure. The phased approach ensures minimal risk while delivering significant improvements in processing capability, accuracy, and user experience.

The integration with the existing Upstash workflow system provides a clean, maintainable solution that builds upon proven infrastructure rather than requiring a complete architectural overhaul. The fallback strategy ensures zero-downtime deployment and maintains service reliability throughout the transition.

Expected timeline: 10 days for complete implementation and testing, with immediate benefits visible for multi-page document processing upon deployment.
