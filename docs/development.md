# Knowledge Sphere Development Guide

This guide provides comprehensive instructions for setting up a local development environment and contributing to the Knowledge Sphere project.

_Last Updated: June 2025_

## 🛠️ Development Environment Setup

### Prerequisites

- **Node.js**: 18.0.0 or higher
- **pnpm**: 8.0.0 or higher (recommended package manager)
- **PostgreSQL**: 14+ with pgvector extension
- **Git**: Latest version
- **VS Code**: Recommended IDE with extensions

### Required VS Code Extensions

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json"
  ]
}
```

## 🚀 Quick Start

### 1. Clone Repository

```bash
git clone <repository-url>
cd knowledge-sphere
```

### 2. Install Dependencies

```bash
# Install pnpm if not already installed
npm install -g pnpm

# Install project dependencies
pnpm install
```

### 3. Environment Setup

```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
nano .env.local
```

### 4. Database Setup

```bash
# Start PostgreSQL (if using local installation)
brew services start postgresql  # macOS
sudo systemctl start postgresql # Linux

# Create database
createdb knowledge_sphere

# Enable pgvector extension
psql knowledge_sphere -c "CREATE EXTENSION IF NOT EXISTS vector;"

# Run migrations
pnpm run db:migrate

# (Optional) Seed database with sample data
pnpm run db:seed
```

### 5. Start Development Server

```bash
pnpm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
knowledge-sphere/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (main)/            # Main application routes
│   │   ├── (auth)/            # Authentication routes
│   │   └── api/               # API endpoints
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Shadcn/UI components
│   │   ├── chat/             # Chat-specific components
│   │   ├── documents/        # Document components
│   │   └── space/            # Space management components
│   ├── database/             # Database layer
│   │   ├── schema/           # Drizzle schema definitions
│   │   ├── migrations/       # Database migrations
│   │   └── repository/       # Data access layer
│   ├── features/             # Feature-specific code
│   │   ├── materials/        # Document management
│   │   └── spaces/           # Space management
│   ├── lib/                  # Utility functions
│   │   ├── auth.ts          # Authentication utilities
│   │   ├── langgraph/       # AI agent implementation
│   │   └── materials/       # Document processing
│   ├── server/               # Server-side code
│   │   └── api/             # tRPC routers and procedures
│   ├── stores/               # Zustand state management
│   └── types/                # TypeScript type definitions
├── docs/                     # Documentation
├── public/                   # Static assets
└── tests/                    # Test files
```

## 🔧 Development Workflow

### Code Style & Formatting

```bash
# Format code
pnpm run format

# Lint code
pnpm run lint

# Type checking
pnpm run check-types

# Run all checks
pnpm run check-all
```

### Database Development

```bash
# Generate new migration
pnpm run db:generate --name=your_migration_name

# Apply migrations
pnpm run db:migrate

# Push schema changes (development only)
pnpm run db:push

# Open database studio
pnpm run db:studio

# Reset database (careful!)
pnpm run db:reset
```

### Testing

```bash
# Run unit tests
pnpm run test

# Run tests in watch mode
pnpm run test:watch

# Run integration tests
pnpm run test:integration

# Generate test coverage
pnpm run test:coverage
```

## 🧪 Testing Strategy

### Unit Tests

- **Framework**: Jest with React Testing Library
- **Location**: `__tests__` directories alongside components
- **Coverage**: Aim for 80%+ coverage on critical paths

### Integration Tests

- **Framework**: Playwright for E2E testing
- **Location**: `tests/` directory
- **Focus**: User workflows and API endpoints

### Test Database

```bash
# Set up test database
createdb knowledge_sphere_test
psql knowledge_sphere_test -c "CREATE EXTENSION IF NOT EXISTS vector;"

# Run tests with test database
TEST_DATABASE_URL="postgresql://localhost/knowledge_sphere_test" pnpm run test
```

## 🔍 Debugging

### Development Tools

```bash
# Enable debug mode
DEBUG=true pnpm run dev

# Database query logging
DATABASE_LOGGING=true pnpm run dev

# AI agent debugging
LANGGRAPH_DEBUG=true pnpm run dev
```

### VS Code Debugging

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node-terminal",
      "request": "launch",
      "command": "pnpm run dev"
    },
    {
      "name": "Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000"
    }
  ]
}
```

### Common Debug Scenarios

```bash
# Database connection issues
pnpm run db:studio

# AI agent step debugging
# Check browser console for LangGraph events

# File upload debugging
# Check network tab for upload progress

# Authentication issues
# Clear browser cookies and localStorage
```

## 🔄 Git Workflow

### Branch Naming Convention

```bash
# Feature branches
feature/user-authentication
feature/document-upload

# Bug fixes
fix/chat-streaming-issue
fix/database-migration

# Documentation
docs/api-documentation
docs/deployment-guide
```

### Commit Message Format

```bash
# Format: type(scope): description
feat(auth): add email verification
fix(chat): resolve streaming connection issues
docs(readme): update installation instructions
refactor(db): optimize query performance
```

### Pull Request Process

1. Create feature branch from `main`
2. Implement changes with tests
3. Run all checks: `pnpm run check-all`
4. Create pull request with description
5. Request code review
6. Address feedback and merge

## 🧩 Adding New Features

### 1. Database Changes

```bash
# Modify schema
# src/database/schema/index.ts

# Generate migration
pnpm run db:generate --name=add_new_feature

# Test migration
pnpm run db:migrate
```

### 2. API Endpoints

```bash
# Add tRPC procedure
# src/server/api/routers/feature.ts

# Add types
# src/types/feature.ts

# Test with tRPC client
```

### 3. UI Components

```bash
# Create component
# src/components/feature/new-component.tsx

# Add to feature module
# src/features/feature/components/

# Write tests
# src/components/feature/__tests__/
```

### 4. Documentation

```bash
# Update relevant docs
# docs/features.md
# docs/api.md

# Add inline documentation
# JSDoc comments for functions
```

## 🔧 Configuration Files

### TypeScript Configuration

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [{ "name": "next" }],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
```

### ESLint Configuration

```json
// eslint.config.js
{
  "extends": ["next/core-web-vitals", "@typescript-eslint/recommended", "prettier"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error"
  }
}
```

## 🚀 Performance Optimization

### Development Performance

```bash
# Enable SWC minification
# next.config.mjs
swcMinify: true

# Optimize bundle analysis
pnpm run build:analyze

# Monitor build times
ANALYZE=true pnpm run build
```

### Database Performance

```bash
# Monitor slow queries
# Enable query logging in development

# Optimize indexes
# Check query execution plans

# Connection pooling
# Configure appropriate pool sizes
```

## 🔒 Security in Development

### Environment Security

- Never commit `.env` files
- Use different API keys for development
- Rotate development secrets regularly
- Use HTTPS in development when testing auth

### Code Security

- Validate all inputs with Zod schemas
- Sanitize user-generated content
- Use parameterized queries
- Implement proper error handling

## 📚 Learning Resources

### Project-Specific

- [Features Documentation](./features.md)
- [API Documentation](./api.md)
- [Database Guide](./db.md)
- [Technology Stack](./tech.md)

### External Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [tRPC Documentation](https://trpc.io/docs)
- [Drizzle ORM](https://orm.drizzle.team/)
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [Shadcn/UI Components](https://ui.shadcn.com/)

## 🤝 Contributing Guidelines

### Code Review Checklist

- [ ] Code follows project conventions
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes without discussion
- [ ] Performance impact considered
- [ ] Security implications reviewed

### Getting Help

- Check existing documentation first
- Search GitHub issues for similar problems
- Ask questions in team chat
- Create detailed issue reports

---

_This development guide covers the essential aspects of working with the Knowledge Sphere codebase. For specific implementation details, refer to the inline code documentation and other guides in the `docs/` directory._
