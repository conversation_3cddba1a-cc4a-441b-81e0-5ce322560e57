# Knowledge Sphere API Documentation

This document provides comprehensive information about the Knowledge Sphere API endpoints, data structures, and integration patterns.

_Last Updated: June 2025_

## 🔗 API Architecture

### Technology Stack

- **Framework**: tRPC for type-safe APIs
- **Authentication**: <PERSON> Auth with session-based security
- **Database**: PostgreSQL with Drizzle ORM
- **File Storage**: Cloudflare R2 with presigned URLs
- **AI Integration**: LangGraph with streaming responses

### Base URL Structure

```
/api/trpc/[...trpc]     # tRPC endpoints
/api/auth/              # Authentication endpoints
/api/materials/         # File upload endpoints
/api/chat/              # AI chat endpoints
```

## 🔐 Authentication

### Session Management

All API endpoints require valid session authentication:

```typescript
// Session validation
const session = await validateRequest();
if (!session.user) {
  throw new TRPCError({ code: 'UNAUTHORIZED' });
}
```

### Authentication Endpoints

- `POST /api/auth/signin` - User sign in
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signout` - User sign out
- `POST /api/auth/verify` - Email verification
- `POST /api/auth/reset-password` - Password reset

## 📁 Space Management API

### tRPC Procedures

#### `space.list`

Get all spaces accessible to the user.

```typescript
// Request
const spaces = await trpc.space.list.query();

// Response
type SpaceWithDetails = {
  id: string;
  name: string;
  description: string | null;
  isShared: boolean;
  ownerId: string;
  role: 'owner' | 'editor' | 'viewer';
  memberCount: number;
  documentCount: number;
  createdAt: Date;
  updatedAt: Date;
};
```

#### `space.create`

Create a new space.

```typescript
// Request
const space = await trpc.space.create.mutate({
  name: 'My Research Space',
  description: 'Space for research documents',
  documentIds: ['doc-1', 'doc-2'], // Optional
});

// Response
type Space = {
  id: string;
  name: string;
  description: string | null;
  ownerId: string;
  isShared: boolean;
  createdAt: Date;
  updatedAt: Date;
};
```

#### `space.update`

Update space details.

```typescript
// Request
await trpc.space.update.mutate({
  id: 'space-id',
  name: 'Updated Name',
  description: 'Updated description',
});
```

#### `space.delete`

Delete a space (owner only).

```typescript
// Request
await trpc.space.delete.mutate({ id: 'space-id' });
```

### Space Sharing API

#### `space.toggleSharing`

Enable or disable space sharing.

```typescript
// Request
await trpc.space.toggleSharing.mutate({
  id: 'space-id',
  isShared: true,
});
```

#### `space.createInvitation`

Generate invitation code for space.

```typescript
// Request
const invitation = await trpc.space.createInvitation.mutate({
  spaceId: 'space-id',
  expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
  maxUses: 10,
});

// Response
type SpaceInvitation = {
  id: number;
  code: string;
  spaceId: string;
  expiresAt: Date | null;
  maxUses: number | null;
  useCount: number;
  createdAt: Date;
};
```

#### `space.joinWithCode`

Join space using invitation code.

```typescript
// Request
const membership = await trpc.space.joinWithCode.mutate({
  code: 'invitation-code',
});
```

#### `space.getMembers`

Get space members (owner/editor only).

```typescript
// Request
const members = await trpc.space.getMembers.query({
  spaceId: 'space-id',
});

// Response
type SpaceMember = {
  id: number;
  userId: string;
  role: 'owner' | 'editor' | 'viewer';
  user: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: Date;
};
```

## 📄 Document Management API

### tRPC Procedures

#### `material.list`

Get all documents accessible to the user.

```typescript
// Request
const materials = await trpc.material.list.query();

// Response
type MaterialWithSpaces = {
  id: string;
  fileName: string;
  fileType: 'pdf' | 'docx' | 'doc' | 'txt';
  s3Key: string;
  uploadedBy: string;
  summary: string | null;
  createdAt: Date;
  spaces: Array<{
    id: string;
    name: string;
    role: 'owner' | 'editor' | 'viewer';
  }>;
};
```

#### `material.search`

Search documents with filters.

```typescript
// Request
const results = await trpc.material.search.query({
  query: 'machine learning',
  spaceIds: ['space-1', 'space-2'],
  documentIds: ['doc-1'],
  limit: 10,
});

// Response
type MaterialSearchResult = {
  id: string;
  fileName: string;
  fileType: string;
  similarity: number;
  chunk: {
    id: string;
    content: string;
    chunkIndex: number;
  };
  spaces: Array<{ id: string; name: string }>;
};
```

#### `material.delete`

Delete a document (owner only).

```typescript
// Request
await trpc.material.delete.mutate({ id: 'material-id' });
```

### File Upload API

#### `POST /api/materials/upload`

Upload documents with processing.

```typescript
// Request (multipart/form-data)
const formData = new FormData();
formData.append('files', file1);
formData.append('files', file2);
formData.append('spaceIds', JSON.stringify(['space-1', 'space-2']));

const response = await fetch('/api/materials/upload', {
  method: 'POST',
  body: formData,
});

// Response
type UploadResponse = {
  success: boolean;
  materials: Array<{
    id: string;
    fileName: string;
    fileType: string;
    s3Key: string;
  }>;
  errors?: Array<{
    fileName: string;
    error: string;
  }>;
};
```

## 🤖 AI Chat API

### LangGraph Streaming API

#### `POST /api/chat/[chatId]/langgraph`

Start AI conversation with streaming response.

```typescript
// Request
const response = await fetch(`/api/chat/${chatId}/langgraph`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    content: 'What is machine learning?',
    config: {
      spaceIds: ['space-1'],
      documentIds: ['doc-1', 'doc-2'],
      maxDocuments: 5,
    },
  }),
});

// Streaming Response (Server-Sent Events)
// Progress events: step updates and thinking process
// Content events: real-time AI response streaming
// Completion events: final answer with references
```

### Chat Management

#### `chat.getTopicWithDocuments`

Get chat topic with associated documents.

```typescript
// Request
const topic = await trpc.chat.getTopicWithDocuments.query({
  topicId: 'topic-id',
});

// Response
type TopicWithDocuments = {
  id: string;
  name: string;
  userId: string;
  createdAt: Date;
  documents: Array<{
    id: string;
    fileName: string;
    fileType: string;
  }>;
  spaces: Array<{
    id: string;
    name: string;
  }>;
};
```

## 🔍 Search API

### Enhanced Search

#### `search.materials`

Advanced document search with multiple strategies.

```typescript
// Request
const results = await trpc.search.materials.query({
  query: 'artificial intelligence',
  spaceIds: ['space-1'],
  searchType: 'hybrid', // "semantic" | "fulltext" | "hybrid"
  limit: 20,
});

// Response includes semantic similarity and full-text relevance scores
```

## 📊 Error Handling

### Standard Error Responses

```typescript
// tRPC Error Format
type TRPCError = {
  code: 'UNAUTHORIZED' | 'FORBIDDEN' | 'NOT_FOUND' | 'BAD_REQUEST' | 'INTERNAL_SERVER_ERROR';
  message: string;
  data?: {
    code: string;
    httpStatus: number;
    path: string;
  };
};
```

### Common Error Codes

- `UNAUTHORIZED`: Invalid or missing authentication
- `FORBIDDEN`: Insufficient permissions for resource
- `NOT_FOUND`: Resource does not exist
- `BAD_REQUEST`: Invalid request parameters
- `INTERNAL_SERVER_ERROR`: Server-side error

## 🔒 Security Considerations

### Access Control

- All endpoints validate user sessions
- Space-based permissions enforced at API level
- Document access filtered by user permissions
- File uploads validated for type and size

### Rate Limiting

- AI chat endpoints: 10 requests per minute per user
- File upload: 5 uploads per minute per user
- Search endpoints: 30 requests per minute per user

### Data Validation

- All inputs validated with Zod schemas
- File uploads scanned for malicious content
- SQL injection prevention with parameterized queries
- XSS protection with input sanitization

---

_This API documentation covers the main endpoints and data structures. For detailed implementation examples, see the source code in `src/server/api/routers/` and `src/app/api/`._
