# Upstash Workflow Integration

This document describes the Upstash Workflow integration for handling document processing operations asynchronously, solving Vercel's execution time limits.

## Overview

The document processing workflow breaks down heavy operations into separate, manageable steps:

1. **Document Preparation** - Validate and prepare document for processing
2. **OCR Extraction** - Extract text using AWS Textract (for PDFs) or standard methods
3. **Embedding Generation** - Generate embeddings using Azure OpenAI
4. **Document Summarization** - Create AI-powered summaries using Google Gemini
5. **Finalization** - Store all data and mark document as completed

## Environment Setup

Add these environment variables to your `.env` file:

```env
QSTASH_URL=your_QSTASH_URL
QSTASH_TOKEN=your_QSTASH_TOKEN
```

## How It Works

### Upload Process (Non-blocking)

1. User uploads file(s) via the UI
2. Files are uploaded to Cloudflare R2
3. Document records are created in the database with `status: 'uploaded'`
4. Upstash workflows are triggered for each document
5. User receives immediate confirmation - no waiting for processing

### Background Processing

Each document goes through a 5-step workflow:

```
Document Upload → OCR/Text Extraction → Embedding Generation → AI Summarization → Completion
     ↓                    ↓                      ↓                    ↓              ↓
  'uploaded'         'processing'          'processing'         'processing'   'completed'
```

### Status Tracking

- Database stores workflow job IDs and processing status
- Frontend polls for status updates
- Users can monitor progress in real-time
- Failed documents can be retried with one click

## API Endpoints

### Document Processing

- `POST /api/materials/upload` - Upload and trigger workflow
- `GET /api/materials/status/{documentId}` - Get document processing status
- `GET /api/materials/status` - Get all user documents status
- `POST /api/materials/retry/{documentId}` - Retry failed document

### Workflows

- `POST /api/workflows/document-processing` - Upstash workflow endpoint

### Webhooks

- `POST /api/webhooks/workflow-completion` - Workflow completion notifications

## Components

### Frontend Components

- `DocumentStatusBadge` - Shows processing status with icons and tooltips
- `ProcessingStatusDashboard` - Overview of all document processing
- Enhanced upload dialog with workflow feedback

### Hooks

- `useDocumentStatus` - Monitor single document processing
- `useDocumentsStatus` - Monitor all documents overview

## Database Schema Updates

New fields added to `documents` table:

- `workflowJobId` - Tracks Upstash workflow execution
- `processingError` - Stores error details for failed processing

## Benefits

1. **No Timeouts** - Bypasses Vercel's execution time limits
2. **Better UX** - Users get immediate feedback, no waiting
3. **Scalability** - Each step runs independently with proper resource allocation
4. **Reliability** - Failed steps can be retried without reprocessing everything
5. **Monitoring** - Real-time status tracking and error reporting

## Error Handling

- Each workflow step has try-catch error handling
- Failed documents are marked with detailed error messages
- Users can retry failed processing with one click
- Workflow continues even if non-critical steps (like summarization) fail

## Development Notes

- Workflow steps are idempotent where possible
- Debug logging enabled by default during development
- All heavy operations (OCR, embeddings, AI) are isolated in separate steps
- Database transactions ensure data consistency

## Deployment

1. Set up Upstash Workflow account and get credentials
2. Add environment variables to deployment platform
3. Deploy the application - workflows will be automatically registered
4. Test with a sample document upload

## Monitoring

- Check Upstash Workflow dashboard for workflow execution logs
- Monitor database for document status distribution
- Use the processing status dashboard in the UI for user-facing monitoring
