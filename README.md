# Knowledge Sphere

A sophisticated AI-powered knowledge management and retrieval system built with Next.js, featuring advanced document processing, intelligent search, and collaborative spaces.

## 🌟 Key Features

- **🤖 AI-Powered Chat**: Advanced RAG system with LangGraph for intelligent document analysis
- **📁 Collaborative Spaces**: Shared workspaces with role-based access control
- **📄 Smart Document Processing**: Multi-format support with automatic text extraction and chunking
- **🔍 Advanced Search**: Semantic and full-text search with AI-powered keyword expansion
- **🔐 Secure Authentication**: Complete user management with email verification
- **☁️ Cloud Storage**: Secure file storage with Cloudflare R2 integration

> 📖 **For detailed feature specifications, see [Features Documentation](./docs/features.md)**

## 🏗️ Architecture

**Modern Full-Stack Application** built with Next.js 14, TypeScript, and PostgreSQL, featuring:

- **AI Integration**: LangGraph + LangChain for intelligent document analysis
- **Vector Database**: PostgreSQL with pgvector for semantic search
- **Type-Safe APIs**: tRPC for end-to-end type safety
- **Modern UI**: Shadcn/UI components with Tailwind CSS

> 🔧 **For complete technology details, see [Technology Stack](./docs/tech.md)**

## 🚀 Quick Start

```bash
# Clone and install
git clone <repository-url>
cd knowledge-sphere
pnpm install

# Set up environment (copy .env.example to .env.local and configure)
cp .env.example .env.local

# Set up database
pnpm run db:migrate

# Start development server
pnpm run dev
```

> 🛠️ **For detailed setup instructions, see [Development Guide](./docs/development.md)**
> 🚀 **For production deployment, see [Deployment Guide](./docs/deployment.md)**

## 📚 Documentation

- **[Features Documentation](./docs/features.md)** - Comprehensive feature specifications and implementation status
- **[Technology Stack](./docs/tech.md)** - Complete technology overview and architecture details
- **[API Documentation](./docs/api.md)** - API endpoints and integration guide
- **[Database Guide](./docs/db.md)** - Database management, schema, and migrations
- **[Development Guide](./docs/development.md)** - Development setup and workflow
- **[Deployment Guide](./docs/deployment.md)** - Production deployment instructions
- **[AI System Architecture](./docs/langgraph_enhance.md)** - LangGraph AI system details
- **[Development Status](./docs/todo.md)** - Current development status and roadmap

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/) and [React](https://reactjs.org/)
- AI capabilities powered by [LangChain](https://langchain.com/) and [LangGraph](https://langchain-ai.github.io/langgraph/)
- UI components from [Shadcn/UI](https://ui.shadcn.com/)
- Database management with [Drizzle ORM](https://orm.drizzle.team/)
