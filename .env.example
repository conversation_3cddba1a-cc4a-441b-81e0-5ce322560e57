# NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_APP_URL=
NEXT_PUBLIC_SERVER_URL= # same as app, but APP_URL often got override by Next.js for no reason in my case
USE_MOCKS=false
# AI testing mode - set to true to use mock AI responses and save tokens during development
USE_AI_TESTING=false

# Database for Docker (local)
DATABASE_DB_NAME=
DATABASE_USER=
DATABASE_PASSWORD=
DATABASE_HOST=localhost
DATABASE_PORT=5432

# Database URL for connection to the database
DATABASE_URL=

# Azure OpenAI
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_RESOURCE_NAME=
AZURE_OPENAI_API_KEY=

# Gemini 
GOOGLE_GENERATIVE_AI_API_KEY=

# Cloudflare R2
CLOUDFLARE_ACCOUNT_ID=
CLOUDFLARE_ACCESS_KEY_ID=
CLOUDFLARE_SECRET_ACCESS_KEY=
CLOUDFLARE_BUCKET_NAME=
NEXT_PUBLIC_MATERIAL_ACCESS_URL=

# AWS Textract
AWS_REGION=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# AWS S3
AWS_S3_REGION=
AWS_S3_BUCKET=

# AWS SNS
AWS_SNS_TOPIC_ARN=
AWS_ROLE_ARN=

# Upstash
QSTASH_URL=https://qstash.upstash.io
QSTASH_TOKEN=

# Email
RESEND_API_KEY=
FROM_EMAIL=