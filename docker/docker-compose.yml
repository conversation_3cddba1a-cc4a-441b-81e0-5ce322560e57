name: knowledge-sphere

services:
  postgres:
    image: pgvector/pgvector:pg16
    container_name: knowledge-sphere-db
    env_file:
      - ../.env
      # specify the env if needed: docker-compose -f docker/docker-compose.yml --env-file .env up -d
    environment:
      POSTGRES_DB: ${DATABASE_DB_NAME}
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql16/data
    ports:
      - '${DATABASE_PORT:-5432}:5432'
    restart: always

volumes:
  postgres_data:
