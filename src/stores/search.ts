import { FinderMode } from '@/types/search';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type SearchConfig = {
  spaceIds: string[];
  documentIds: string[];
  maxDocuments: number;
  finderMode: FinderMode;
  filterType: 'space' | 'document';
};

type SearchStore = {
  config: SearchConfig;
  setConfig: (config: Partial<SearchConfig>) => void;
  resetConfig: () => void;
};

const DEFAULT_CONFIG: SearchConfig = {
  spaceIds: [],
  documentIds: [],
  maxDocuments: 5,
  finderMode: 'fulltext',
  filterType: 'document',
};

export const useSearchStore = create<SearchStore>()(
  persist(
    (set) => ({
      config: DEFAULT_CONFIG,
      setConfig: (newConfig) =>
        set((state) => ({
          config: { ...state.config, ...newConfig },
        })),
      resetConfig: () => set({ config: DEFAULT_CONFIG }),
    }),
    {
      name: 'search-config',
    }
  )
);
