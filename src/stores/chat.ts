import { ChatConfig } from '@/types/chat';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type ChatStore = {
  config: ChatConfig;
  originalConfig: ChatConfig | null; // Track the original config from database
  currentTopicId: string | null; // Track which topic this config belongs to
  setConfig: (config: Partial<ChatConfig>) => void;
  setOriginalConfig: (config: ChatConfig | null, topicId: string) => void;
  resetConfig: () => void;
  hasConfigChanged: () => boolean;
  markConfigAsSaved: () => void; // Update original config to current config after saving
};

const DEFAULT_CONFIG: ChatConfig = {
  spaceIds: [],
  documentIds: [],
  searchMode: 'semantic',
};

export const useChatStore = create<ChatStore>()(
  persist(
    (set, get) => ({
      config: DEFAULT_CONFIG,
      originalConfig: null,
      currentTopicId: null,
      setConfig: (newConfig: Partial<ChatConfig>) =>
        set((state) => ({
          config: { ...DEFAULT_CONFIG, ...state.config, ...newConfig },
        })),
      setOriginalConfig: (config: ChatConfig | null, topicId: string) =>
        set((state) => {
          // If switching to a different topic, reset everything
          const isNewTopic = state.currentTopicId !== topicId;

          return {
            originalConfig: config,
            currentTopicId: topicId,
            // Always load the topic's config when switching topics or on first load
            config: isNewTopic ? (config ?? DEFAULT_CONFIG) : state.config,
          };
        }),
      resetConfig: () =>
        set((state) => ({
          config: state.originalConfig ?? DEFAULT_CONFIG,
        })),
      hasConfigChanged: () => {
        const state = get();
        if (!state.originalConfig) return false;

        // Deep compare the relevant fields
        const original = state.originalConfig;
        const current = state.config;

        return (
          JSON.stringify(original.spaceIds?.sort()) !== JSON.stringify(current.spaceIds?.sort()) ||
          JSON.stringify(original.documentIds?.sort()) !==
            JSON.stringify(current.documentIds?.sort()) ||
          original.searchMode !== current.searchMode
        );
      },
      markConfigAsSaved: () => {
        set((state) => ({
          originalConfig: { ...state.config },
        }));
      },
    }),
    {
      name: 'chat-config',
    }
  )
);
