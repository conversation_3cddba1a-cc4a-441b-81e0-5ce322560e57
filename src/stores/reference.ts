import type { RetrievedDocumentChunkDTO } from '@/types/chat';
import { create } from 'zustand';

type ReferenceStore = {
  selectedReference: RetrievedDocumentChunkDTO | null;
  setSelectedReference: (reference: RetrievedDocumentChunkDTO | null) => void;
};

export const useReferenceStore = create<ReferenceStore>((set) => ({
  selectedReference: null,
  setSelectedReference: (reference) => set({ selectedReference: reference }),
}));
