import { useState } from 'react';

/**
 * Custom hook to handle nested modal interactions (e.g., Popover inside Dialog).
 *
 * This hook solves the common issue where clicking outside a nested popover
 * (like MultiSelect inside a Dialog) causes the mouse cursor to become unresponsive
 * or the parent dialog to close unexpectedly.
 *
 * Referece: https://github.com/sersavan/shadcn-multi-select-component/issues/46
 *
 * @returns Object containing helper functions for nested modal management
 */
export const useNestedModals = () => {
  const [isNestedOpen, setIsNestedOpen] = useState(false);

  const toggleNestedModal = (state?: boolean) =>
    setIsNestedOpen((prev) => (state !== undefined ? state : !prev));

  const handleInteractOutside = (e: Event) => {
    if (isNestedOpen) {
      e.preventDefault();
      e.stopPropagation();
    }
  };

  return {
    isNestedOpen,
    toggleNestedModal,
    handleInteractOutside,
  };
};
