import type { TopicWithDocuments } from '@/database/repository/chat';
import { useChatStore } from '@/stores/chat';
import { DocumentMetadata, RetrievedDocumentChunkDTO } from '@/types/chat';
import { useMutation } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export type ChatInteraction = {
  content: string;
  senderType: 'user' | 'ai' | 'system';
  createdAt?: Date;
  retrievedDocuments?: RetrievedDocumentChunkDTO[];
  agentSteps?: AgentStep[];
  isStreamingContent?: boolean;
};

/**
 * Optimized agent step structure that stores only data ACTUALLY USED by the frontend
 * to minimize database storage and avoid redundant data duplication.
 *
 * Field usage by step (based on frontend display requirements):
 * - classify_query: reasoning (✓ displayed)
 * - check_history_relevance: reasoning, needsHistory (✓ displayed)
 * - create_research_plan: reasoning, retrievalStrategy (✓ grouped with retrieval)
 * - resolve_material_context: reasoning, retrievalStrategy (✓ grouped with retrieval)
 * - retrieve_context/retrieve_summary_context: reasoning, extractedKeywords, retrievedChunks, retrievedSummaries, retrievalStrategy (✓ all displayed)
 * - evaluate_results: reasoning (✓ displayed, chunks/summaries NOT shown)
 * - refine_query: reasoning, extractedKeywords (✓ displayed)
 * - generate_answer: reasoning (✓ step not displayed in frontend at all)
 */
export type AgentStep = {
  // Core step data (always present)
  step: string;
  message: string;
  retryCount: number;
  hasError: boolean;
  error?: string;

  // Frontend-only tracking (not stored in database)
  stepId?: string;

  // Step-specific data (only included when relevant to the step)
  reasoning?: string;
  extractedKeywords?: string[];
  retrievalStrategy?: 'chunks_only' | 'summaries_only' | 'both_chunks_and_summaries';

  // History relevance data (check_history_relevance step)
  needsHistory?: boolean;

  // Material selection data (resolve_material_context, retrieve_context)
  targetMaterials?: Array<{
    id: string;
    fileName: string;
    reason: string;
  }>;
  materialSelectionReasoning?: string;

  // Retrieval results (retrieve_context, evaluate_results, generate_answer)
  retrievedChunks?: Array<{
    id: string;
    chunkId: number;
    fileName: string;
    content: string; // Truncated for storage efficiency
    key: string; // S3 key for document viewer
    similarity?: number;
    fileType: string;
    metadata: DocumentMetadata;
  }>;
  retrievedSummaries?: Array<{
    id: string;
    fileName: string;
    summary: string; // Truncated for storage efficiency
    key: string; // S3 key for document viewer
    similarity?: number;
    fileType: string; // File type for consistency
  }>;
};

export type SendMessageOptions = {
  isInitialMessage?: boolean;
};

export function useChatEnhanced(initialTopic: TopicWithDocuments) {
  const { config, setOriginalConfig, markConfigAsSaved } = useChatStore();

  // Initialize the chat store with the topic's config on mount
  useEffect(() => {
    if (initialTopic.config) {
      setOriginalConfig(initialTopic.config, initialTopic.id);
    }
  }, [initialTopic.config, initialTopic.id, setOriginalConfig]);

  const [messages, setMessages] = useState<ChatInteraction[]>(
    initialTopic.interactions.map((interaction) => ({
      content: interaction.content,
      senderType: interaction.senderType,
      createdAt: interaction.createdAt,
      retrievedDocuments: interaction.retrievedDocuments as RetrievedDocumentChunkDTO[] | undefined,
      // Load existing agent steps from database if available
      agentSteps: interaction.agentSteps || undefined,
    }))
  );
  const [error, setError] = useState<Error | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);

  // Track current running step for real-time UI updates
  const [currentRunningStep, setCurrentRunningStep] = useState<{
    step: string;
    message: string;
  } | null>(null);
  const [isThinking, setIsThinking] = useState(false);

  const sendMessageMutation = useMutation({
    mutationFn: async ({
      content,
      isInitialMessage,
    }: {
      content: string;
      isInitialMessage?: boolean;
    }) => {
      try {
        setError(null);
        setIsStreaming(true);
        setCurrentRunningStep(null);

        // Immediately show loading state for better UX
        setIsThinking(true);

        // Add user message immediately for better UX
        if (!isInitialMessage && content) {
          setMessages((prev) => [...prev, { content, senderType: 'user', createdAt: new Date() }]);
        }

        const response = await fetch(`/api/chat/${initialTopic.id}/langgraph`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            content,
            isInitialMessage,
            config,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to send message');
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        let aiResponse = '';

        if (reader) {
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) {
                setIsStreaming(false);
                setIsThinking(false);
                setCurrentRunningStep(null);
                return aiResponse;
              }

              const chunk = decoder.decode(value);

              // All data now uses consistent "data: JSON" format

              // Handle Server-Sent Events (data: format) - now all data uses this format
              if (chunk.includes('data: ')) {
                const lines = chunk.split('\n');

                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    try {
                      const eventData = JSON.parse(line.replace('data: ', '').trim());

                      if (eventData.type === 'content') {
                        // Handle streaming content
                        setIsThinking(false);
                        setCurrentRunningStep(null);
                        aiResponse += eventData.content;

                        setMessages((prev) => {
                          const newMessages = [...prev];
                          const lastMessage = newMessages[newMessages.length - 1];

                          if (lastMessage?.senderType === 'ai') {
                            lastMessage.content = aiResponse;
                            lastMessage.isStreamingContent = true;
                          } else {
                            newMessages.push({
                              content: aiResponse,
                              senderType: 'ai',
                              createdAt: new Date(),
                              agentSteps: [],
                              isStreamingContent: true,
                            });
                          }
                          return newMessages;
                        });
                      } else if (eventData.type === 'completion') {
                        // Handle completion
                        setMessages((prev) => {
                          const newMessages = [...prev];
                          const lastMessage = newMessages[newMessages.length - 1];
                          if (lastMessage?.senderType === 'ai') {
                            lastMessage.isStreamingContent = false;
                            // References are now handled by agent progress badges
                          }
                          return newMessages;
                        });
                        setIsThinking(false);
                        setIsStreaming(false);
                        setCurrentRunningStep(null);
                        // Mark config as saved since the message was successfully processed
                        markConfigAsSaved();
                      } else if (eventData.type === 'step_start') {
                        console.log('🔄 Step Start:', eventData.step, 'ID:', eventData.stepId);
                        // Show current running step
                        setCurrentRunningStep({
                          step: eventData.step,
                          message: eventData.message,
                        });
                        setIsThinking(false); // Step started, not just thinking anymore

                        // Create AI message if it doesn't exist yet to hold the agent steps
                        setMessages((prev) => {
                          const newMessages = [...prev];
                          let lastMessage = newMessages[newMessages.length - 1];

                          if (!lastMessage || lastMessage.senderType !== 'ai') {
                            const newAiMessage: ChatInteraction = {
                              content: '',
                              senderType: 'ai',
                              createdAt: new Date(),
                              agentSteps: [],
                              isStreamingContent: false,
                            };
                            newMessages.push(newAiMessage);
                            lastMessage = newAiMessage;
                          }

                          if (lastMessage.senderType === 'ai') {
                            const existingSteps = lastMessage.agentSteps || [];

                            // Create initial step with stepId for matching with progress event
                            const newStep = {
                              step: eventData.step,
                              message: eventData.message,
                              retryCount: 0,
                              hasError: false,
                              stepId: eventData.stepId, // Store stepId for matching
                            };

                            lastMessage.agentSteps = [...existingSteps, newStep];
                          }
                          return newMessages;
                        });
                      } else if (eventData.type === 'progress') {
                        console.log(
                          '📊 Progress Update:',
                          eventData.step,
                          'ID:',
                          eventData.stepId,
                          'with rich data:',
                          {
                            keywords: eventData.extractedKeywords?.length || 0,
                            chunks: eventData.retrievedChunks?.length || 0,
                            reasoning: !!eventData.reasoning,
                          }
                        );
                        // Handle progress events - update existing step with rich details
                        setCurrentRunningStep(null); // Clear running step when progress comes

                        setMessages((prev) => {
                          const newMessages = [...prev];
                          let lastMessage = newMessages[newMessages.length - 1];

                          if (!lastMessage || lastMessage.senderType !== 'ai') {
                            const newAiMessage: ChatInteraction = {
                              content: '',
                              senderType: 'ai',
                              createdAt: new Date(),
                              agentSteps: [],
                              isStreamingContent: false,
                            };
                            newMessages.push(newAiMessage);
                            lastMessage = newAiMessage;
                          }

                          if (lastMessage.senderType === 'ai') {
                            const existingSteps = lastMessage.agentSteps || [];

                            const progressStep = {
                              step: eventData.step,
                              message: eventData.message,
                              retryCount: eventData.retryCount || 0,
                              hasError: eventData.hasError || false,
                              extractedKeywords: eventData.extractedKeywords,
                              reasoning: eventData.reasoning,
                              retrievedChunks: eventData.retrievedChunks,
                              retrievedSummaries: eventData.retrievedSummaries,
                              error: eventData.error,
                            };

                            // Smart step management: merge retrieval steps, update others by stepId
                            const isRetrievalStep = [
                              'retrieve_context',
                              'retrieve_summary_context',
                              'retrieve_content',
                            ].includes(eventData.step);

                            if (isRetrievalStep) {
                              // For retrieval steps, find any existing retrieval step and replace it with the latest data
                              const existingRetrievalIndex = existingSteps.findIndex((step) =>
                                [
                                  'retrieve_context',
                                  'retrieve_summary_context',
                                  'retrieve_content',
                                ].includes(step.step)
                              );

                              if (existingRetrievalIndex >= 0) {
                                // Replace existing retrieval step with the latest one (which should have better data)
                                existingSteps[existingRetrievalIndex] = {
                                  ...progressStep,
                                  stepId: eventData.stepId, // Use the latest stepId
                                };
                                lastMessage.agentSteps = [...existingSteps];
                              } else {
                                // No existing retrieval step, add this one
                                lastMessage.agentSteps = [
                                  ...existingSteps,
                                  { ...progressStep, stepId: eventData.stepId },
                                ];
                              }
                            } else {
                              // For non-retrieval steps, use the original stepId-based logic
                              const existingStepIndex = existingSteps.findIndex(
                                (step) => step.stepId === eventData.stepId
                              );

                              if (existingStepIndex >= 0) {
                                // Update existing step with progress data
                                existingSteps[existingStepIndex] = {
                                  ...existingSteps[existingStepIndex],
                                  ...progressStep,
                                  stepId: eventData.stepId, // Keep the stepId
                                };
                                lastMessage.agentSteps = [...existingSteps];
                              } else {
                                // Add new step if stepId not found
                                lastMessage.agentSteps = [
                                  ...existingSteps,
                                  { ...progressStep, stepId: eventData.stepId },
                                ];
                              }
                            }
                          }
                          return newMessages;
                        });
                      }
                    } catch (e) {
                      console.warn('Failed to parse event data:', e);
                    }
                  }
                }
                continue;
              }
            }
          } catch (e) {
            setIsStreaming(false);
            setIsThinking(false);
            setCurrentRunningStep(null);
            setError(e instanceof Error ? e : new Error('Failed to read response'));
            throw e;
          }
        }

        setIsStreaming(false);
        setIsThinking(false);
        setCurrentRunningStep(null);
        return aiResponse;
      } catch (e) {
        setIsStreaming(false);
        setIsThinking(false);
        setCurrentRunningStep(null);
        setError(e instanceof Error ? e : new Error('Failed to send message'));
        throw e;
      }
    },
  });

  const sendMessage = (content: string, options?: SendMessageOptions) => {
    return sendMessageMutation.mutate({
      content,
      isInitialMessage: options?.isInitialMessage,
    });
  };

  return {
    messages,
    error,
    isLoading: isStreaming,
    sendMessage,

    // Enhanced UI state
    isThinking,
    isAgentWorking: isStreaming || isThinking,
    currentRunningStep, // New: current step being executed
  };
}
