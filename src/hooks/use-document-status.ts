import { toast } from 'sonner';
import { useCallback, useEffect, useState } from 'react';

interface DocumentStatus {
  documentId: string;
  fileName: string;
  status: 'uploaded' | 'processing' | 'completed' | 'failed';
  workflowJobId?: string;
  processingError?: string;
  createdAt: string;
  updatedAt: string;
  workflowStatus?: {
    status: string;
    createdAt: string;
    updatedAt: string;
    steps: unknown[];
  };
}

interface UseDocumentStatusOptions {
  documentId: string;
  enabled?: boolean;
  pollingInterval?: number; // in milliseconds
  onStatusChange?: (status: DocumentStatus) => void;
  onCompleted?: (document: DocumentStatus) => void;
  onFailed?: (document: DocumentStatus) => void;
}

export function useDocumentStatus({
  documentId,
  enabled = true,
  pollingInterval = 5000, // 5 seconds
  onStatusChange,
  onCompleted,
  onFailed,
}: UseDocumentStatusOptions) {
  const [status, setStatus] = useState<DocumentStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);

  const fetchStatus = useCallback(async () => {
    if (!enabled || !documentId) return;

    try {
      setIsLoading(true);
      const response = await fetch(`/api/materials/status/${documentId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch status: ${response.statusText}`);
      }

      const newStatus: DocumentStatus = await response.json();

      // Check if status changed
      if (status && status.status !== newStatus.status) {
        onStatusChange?.(newStatus);

        // Trigger specific callbacks
        if (newStatus.status === 'completed') {
          onCompleted?.(newStatus);
          toast.success(`Document "${newStatus.fileName}" processed successfully!`);
        } else if (newStatus.status === 'failed') {
          onFailed?.(newStatus);
          toast.error(`Document "${newStatus.fileName}" processing failed`);
        }
      }

      setStatus(newStatus);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error fetching document status:', err);
    } finally {
      setIsLoading(false);
    }
  }, [documentId, enabled, status, onStatusChange, onCompleted, onFailed]);

  const retry = useCallback(async () => {
    if (!documentId) return;

    try {
      setIsRetrying(true);
      const response = await fetch(`/api/materials/retry/${documentId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        throw new Error(`Failed to retry: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        toast.success('Document processing restarted');
        // Immediately fetch new status
        await fetchStatus();
      } else {
        throw new Error(result.error || 'Retry failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Retry failed';
      toast.error(errorMessage);
      console.error('Error retrying document processing:', err);
    } finally {
      setIsRetrying(false);
    }
  }, [documentId, fetchStatus]);

  // Set up polling
  useEffect(() => {
    if (!enabled || !documentId) return;

    // Initial fetch
    fetchStatus();

    // Only poll if status is not final
    if (status?.status === 'completed' || status?.status === 'failed') {
      return;
    }

    const interval = setInterval(fetchStatus, pollingInterval);
    return () => clearInterval(interval);
  }, [fetchStatus, enabled, documentId, pollingInterval, status?.status]);

  return {
    status,
    isLoading,
    error,
    isRetrying,
    retry,
    refresh: fetchStatus,
  };
}

// Hook for managing multiple documents status
export function useDocumentsStatus() {
  const [documents, setDocuments] = useState<DocumentStatus[]>([]);
  const [counts, setCounts] = useState({
    uploaded: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllStatuses = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/materials/status');

      if (!response.ok) {
        throw new Error(`Failed to fetch documents: ${response.statusText}`);
      }

      const data = await response.json();
      setDocuments(data.documents);
      setCounts(data.counts);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error fetching documents status:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchAllStatuses();
  }, [fetchAllStatuses]);

  return {
    documents,
    counts,
    isLoading,
    error,
    refresh: fetchAllStatuses,
  };
}
