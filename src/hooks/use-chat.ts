import type { TopicWithDocuments } from '@/database/repository/chat';
import { useChatStore } from '@/stores/chat';
import { RetrievedDocumentChunkDTO } from '@/types/chat';
import { useMutation } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export type ChatInteraction = {
  content: string;
  senderType: 'user' | 'ai' | 'system';
  createdAt?: Date;
  retrievedDocuments?: RetrievedDocumentChunkDTO[];
};

export type SendMessageOptions = {
  isInitialMessage?: boolean;
};

export function useChat(initialTopic: TopicWithDocuments) {
  const { config, setOriginalConfig, markConfigAsSaved } = useChatStore();

  // Initialize the chat store with the topic's config on mount
  useEffect(() => {
    if (initialTopic.config) {
      setOriginalConfig(initialTopic.config, initialTopic.id);
    }
  }, [initialTopic.config, initialTopic.id, setOriginalConfig]);
  const [messages, setMessages] = useState<ChatInteraction[]>(
    initialTopic.interactions.map((interaction) => ({
      content: interaction.content,
      senderType: interaction.senderType,
      createdAt: interaction.createdAt,
      retrievedDocuments: interaction.retrievedDocuments as RetrievedDocumentChunkDTO[] | undefined,
    }))
  );
  const [error, setError] = useState<Error | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);

  const sendMessageMutation = useMutation({
    mutationFn: async ({
      content,
      isInitialMessage,
    }: {
      content: string;
      isInitialMessage?: boolean;
    }) => {
      try {
        setError(null);
        setIsStreaming(true);
        const response = await fetch(`/api/chat/${initialTopic.id}/message`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            content,
            isInitialMessage,
            config,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to send message');
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        let aiResponse = '';

        if (reader) {
          if (!isInitialMessage && content) {
            setMessages((prev) => [
              ...prev,
              { content, senderType: 'user', createdAt: new Date() },
            ]);
          }

          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) {
                setIsStreaming(false);
                return aiResponse;
              }

              const chunk = decoder.decode(value);

              // Check if the chunk is a completion message
              if (chunk.startsWith('\nEOM')) {
                try {
                  console.log('chunk', chunk);
                  // The replace method will replace only the first occurrence of '\nEOM'
                  const completionData = JSON.parse(chunk.replace('\nEOM', '').trim());
                  if (completionData.type === 'completion') {
                    setMessages((prev) => {
                      const newMessages = [...prev];
                      const lastMessage = newMessages[newMessages.length - 1];
                      if (lastMessage.senderType === 'ai') {
                        lastMessage.retrievedDocuments = completionData.references;
                      }
                      return newMessages;
                    });
                    // Mark config as saved since the message was successfully processed
                    markConfigAsSaved();
                  }
                  continue;
                } catch (e) {
                  console.error('Failed to parse completion message:', e);
                }
              }

              aiResponse += chunk;

              setMessages((prev) => {
                const newMessages = [...prev];
                if (newMessages[newMessages.length - 1].senderType === 'ai') {
                  newMessages[newMessages.length - 1].content = aiResponse;
                } else {
                  newMessages.push({
                    content: aiResponse,
                    senderType: 'ai',
                    createdAt: new Date(),
                  });
                }
                return newMessages;
              });
            }
          } catch (e) {
            setIsStreaming(false);
            setError(e instanceof Error ? e : new Error('Failed to read response'));
            throw e;
          }
        }
        setIsStreaming(false);
        return aiResponse;
      } catch (e) {
        setIsStreaming(false);
        setError(e instanceof Error ? e : new Error('Failed to send message'));
        throw e;
      }
    },
  });

  const sendMessage = (content: string, options?: SendMessageOptions) => {
    return sendMessageMutation.mutate({
      content,
      isInitialMessage: options?.isInitialMessage,
    });
  };

  return {
    messages,
    error,
    isLoading: isStreaming,
    sendMessage,
  };
}
