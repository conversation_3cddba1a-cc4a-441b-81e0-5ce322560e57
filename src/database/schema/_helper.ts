import { timestamp } from 'drizzle-orm/pg-core';

export const timestamptz = (name: string) => timestamp(name, { withTimezone: true });

export const createdAt = () => timestamptz('created_at').notNull().defaultNow();
export const updatedAt = () => timestamptz('updated_at').notNull().defaultNow();
export const deletedAt = () => timestamptz('deleted_at');

export function enumToPgEnum<T extends Record<string, string>>(
  myEnum: T
): [T[keyof T], ...T[keyof T][]] {
  return Object.values(myEnum).map((value: string) => `${value}`) as [T[keyof T], ...T[keyof T][]];
}

// https://github.com/drizzle-team/drizzle-orm/discussions/1914
