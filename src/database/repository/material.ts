import { db } from '@/database/drizzle';
import { documentChunks, documents, documentSpaces, ocrAnalysis } from '@/database/schema';
import { type TextractResult } from '@/lib/aws/textract';
import { deleteFromR2 } from '@/lib/cloudflare/r2';
import { DocumentType } from '@/types/material';
import { desc, eq } from 'drizzle-orm';

export const MaterialRepository = {
  getMaterialById,
  getMaterials,
  getMaterialsByUserId,
  getMaterialByKey,
  getMaterialWithChunksById,
  storeMaterial,
  storeChunks,
  storeChunksEnhanced,
  storeOcrAnalysis,
  getOcrAnalysis,
  updateMaterialStatus,
  updateMaterialFilename,
  updateWorkflowJobId,
  updateProcessingError,
  updateProcessingTiming,
  getDocumentsByStatus,
  deleteMaterial,
  // Note: S3 operations now reuse the standard s3Key field
  // Textract async methods
  storeTextractJobId,
  updateTextractJobStatus,
  getTextractJobInfo,
};

export async function getMaterialById(id: string) {
  return db.select().from(documents).where(eq(documents.id, id)).limit(1);
}

export async function getMaterials() {
  return db.select().from(documents).orderBy(desc(documents.createdAt));
}

export async function getMaterialsByUserId(userId: string) {
  return db
    .select()
    .from(documents)
    .where(eq(documents.uploadedBy, userId))
    .orderBy(desc(documents.createdAt));
}

export async function getMaterialByKey(key: string) {
  const material = await db.select().from(documents).where(eq(documents.s3Key, key)).limit(1);
  return material[0];
}

export async function getMaterialWithChunksById(id: string) {
  try {
    const result = await db
      .select({
        id: documents.id,
        fileName: documents.fileName,
        fileType: documents.fileType,
        s3Key: documents.s3Key,
        uploadedBy: documents.uploadedBy,
        createdAt: documents.createdAt,
        summary: documents.summary,
        chunks: {
          id: documentChunks.id,
          content: documentChunks.content,
          embedding: documentChunks.embedding,
          chunkOrder: documentChunks.chunkOrder,
          metadata: documentChunks.metadata,
        },
      })
      .from(documents)
      .leftJoin(documentChunks, eq(documents.id, documentChunks.documentId))
      .where(eq(documents.id, id));

    if (result.length === 0) return null;

    // Transform the flat results into nested structure
    const document = {
      ...result[0],
      chunks: result
        .filter(
          (row): row is typeof row & { chunks: NonNullable<(typeof row)['chunks']> } =>
            row.chunks !== null && row.chunks.id !== null
        )
        .map((row) => row.chunks),
    };

    return document;
  } catch {
    // console.error('Error getting material with chunks by id:', error);
    return null;
  }
}

type SaveMaterialParams = {
  fileName: string;
  fileType: DocumentType;
  s3Key: string;
  uploadedBy: string;
  chunks?: { content: string; pageNumber?: number; chunkOrder: number }[];
  embeddings?: number[][];
  summary?: string | null;
  summaryEmbedding?: number[] | null;
};

export async function storeMaterial({
  fileName,
  fileType,
  s3Key,
  uploadedBy,
  chunks,
  embeddings,
  summary,
  summaryEmbedding,
}: SaveMaterialParams) {
  // Insert the document with summary data
  const [document] = await db
    .insert(documents)
    .values({
      fileName,
      fileType,
      s3Key,
      uploadedBy,
      summary,
      summaryEmbedding,
    })
    .returning();

  if (chunks && embeddings) {
    // Insert the chunks
    await db.insert(documentChunks).values(
      chunks.map((chunk, index) => ({
        documentId: document.id,
        content: chunk.content,
        embedding: embeddings[index],
        pageNumber: chunk.pageNumber,
        chunkOrder: chunk.chunkOrder,
      }))
    );
  }

  return document;
}

type StoreChunksParams = {
  documentId: string;
  chunks: {
    content: string;
    embedding: number[];
    chunkOrder: number;
    metadata: Record<string, unknown>;
  }[];
};

type StoreChunksEnhancedParams = {
  documentId: string;
  chunks: {
    content: string;
    embedding: number[];
    chunkOrder: number;
    metadata: Record<string, unknown>;
    boundingBox?: unknown;
    polygonCoordinates?: unknown;
    layoutType?: string;
    confidenceScore?: number;
  }[];
};

export async function storeChunks({ documentId, chunks }: StoreChunksParams) {
  await db.insert(documentChunks).values(
    chunks.map((chunk) => ({
      documentId,
      content: chunk.content,
      embedding: chunk.embedding,
      chunkOrder: chunk.chunkOrder,
      metadata: chunk.metadata || {},
    }))
  );
}

export async function storeChunksEnhanced({ documentId, chunks }: StoreChunksEnhancedParams) {
  await db.insert(documentChunks).values(
    chunks.map((chunk) => ({
      documentId,
      content: chunk.content,
      embedding: chunk.embedding,
      chunkOrder: chunk.chunkOrder,
      pageNumber: (chunk.metadata?.pageIndex as number) || 0, // Extract from metadata
      metadata: chunk.metadata || {},
      boundingBox: chunk.boundingBox ? JSON.parse(JSON.stringify(chunk.boundingBox)) : null,
      polygonCoordinates: chunk.polygonCoordinates
        ? JSON.parse(JSON.stringify(chunk.polygonCoordinates))
        : null,
      layoutType: chunk.layoutType as
        | 'PARAGRAPH'
        | 'TITLE'
        | 'HEADER'
        | 'FOOTER'
        | 'SECTION_HEADER'
        | 'LINE'
        | 'LAYOUT_TEXT'
        | 'UNKNOWN'
        | null,
      confidenceScore: chunk.confidenceScore,
    }))
  );
}

export async function storeOcrAnalysis(
  documentId: string,
  ocrResult: TextractResult,
  processingTimeMs: number
) {
  const averageConfidence =
    ocrResult.extractedText.length > 0
      ? Math.round(
          (ocrResult.extractedText.reduce((sum, item) => sum + item.confidence, 0) /
            ocrResult.extractedText.length) *
            100
        )
      : 0;

  // Check if an existing OCR analysis record exists for this document
  const existingAnalysis = await db
    .select()
    .from(ocrAnalysis)
    .where(eq(ocrAnalysis.documentId, documentId))
    .limit(1);

  if (existingAnalysis.length > 0) {
    // Update the existing record with analysis data
    await db
      .update(ocrAnalysis)
      .set({
        analysisData: ocrResult as unknown as Record<string, unknown>,
        processingTime: processingTimeMs,
        totalBlocks: ocrResult.extractedText.length,
        averageConfidence,
        updatedAt: new Date(),
      })
      .where(eq(ocrAnalysis.id, existingAnalysis[0].id));
  } else {
    // Create new record if none exists
    await db.insert(ocrAnalysis).values({
      documentId,
      analysisData: ocrResult as unknown as Record<string, unknown>,
      processingTime: processingTimeMs,
      totalBlocks: ocrResult.extractedText.length,
      averageConfidence,
    });
  }
}

export async function getOcrAnalysis(documentId: string) {
  const result = await db
    .select()
    .from(ocrAnalysis)
    .where(eq(ocrAnalysis.documentId, documentId))
    .orderBy(desc(ocrAnalysis.updatedAt), desc(ocrAnalysis.createdAt))
    .limit(1);

  return result[0] || null;
}

export async function updateMaterialStatus(
  documentId: string,
  status: 'uploaded' | 'ocr_extracting' | 'embedding' | 'summarizing' | 'completed' | 'failed'
) {
  await db
    .update(documents)
    .set({
      status: status as
        | 'uploaded'
        | 'ocr_extracting'
        | 'embedding'
        | 'summarizing'
        | 'completed'
        | 'failed',
    })
    .where(eq(documents.id, documentId));
}

export async function updateMaterialFilename(id: string, fileName: string) {
  try {
    const [updatedDocument] = await db
      .update(documents)
      .set({
        fileName,
        updatedAt: new Date(),
      })
      .where(eq(documents.id, id))
      .returning();

    return updatedDocument;
  } catch (error) {
    console.error('Error updating material filename:', error);
    throw error;
  }
}

export async function updateWorkflowJobId(documentId: string, workflowJobId: string) {
  await db
    .update(documents)
    .set({
      workflowJobId,
      updatedAt: new Date(),
    })
    .where(eq(documents.id, documentId));
}

export async function updateProcessingError(documentId: string, error: string) {
  await db
    .update(documents)
    .set({
      processingError: error,
      status: 'failed',
      updatedAt: new Date(),
    })
    .where(eq(documents.id, documentId));
}

export async function updateProcessingTiming(
  documentId: string,
  stage: string,
  startTime?: Date,
  endTime?: Date
) {
  // Get current processing timings
  const [currentDoc] = await db
    .select({ processingTimings: documents.processingTimings })
    .from(documents)
    .where(eq(documents.id, documentId))
    .limit(1);

  const timings =
    (currentDoc?.processingTimings as Record<
      string,
      { startedAt: string; completedAt?: string }
    >) || {};

  if (startTime && !endTime) {
    // Starting a new stage
    timings[stage] = {
      startedAt: startTime.toISOString(),
    };
  } else if (endTime && timings[stage]) {
    // Completing an existing stage
    timings[stage] = {
      ...timings[stage],
      completedAt: endTime.toISOString(),
    };
  } else if (startTime && endTime) {
    // Setting both start and end time
    timings[stage] = {
      startedAt: startTime.toISOString(),
      completedAt: endTime.toISOString(),
    };
  }

  await db
    .update(documents)
    .set({
      processingTimings: timings,
      updatedAt: new Date(),
    })
    .where(eq(documents.id, documentId));
}

export async function getDocumentsByStatus(
  status: 'uploaded' | 'ocr_extracting' | 'embedding' | 'summarizing' | 'completed' | 'failed'
) {
  return db
    .select()
    .from(documents)
    .where(eq(documents.status, status))
    .orderBy(desc(documents.createdAt));
}

export async function deleteMaterial(id: string, fileKey?: string) {
  try {
    // Hard delete chunks and document_spaces, and soft delete document in a transaction
    const [deletedDocument] = await db.transaction(async (tx) => {
      // Hard delete all associated chunks
      await tx.delete(documentChunks).where(eq(documentChunks.documentId, id));

      // Hard delete all document_spaces connections
      await tx.delete(documentSpaces).where(eq(documentSpaces.documentId, id));

      // Soft delete the document
      const [document] = await tx
        .update(documents)
        .set({
          deletedAt: new Date(),
        })
        .where(eq(documents.id, id))
        .returning();

      return [document];
    });

    if (fileKey) {
      await deleteFromR2(fileKey);
    }

    return deletedDocument;
  } catch (error) {
    console.error('Error deleting material:', error);
    throw error;
  }
}

// Note: S3 operations now reuse the standard s3Key field - no separate functions needed

// Textract async processing methods

export async function storeTextractJobId(documentId: string, textractJobId: string) {
  // First, get or create an OCR analysis record
  const existingAnalysis = await db
    .select()
    .from(ocrAnalysis)
    .where(eq(ocrAnalysis.documentId, documentId))
    .limit(1);

  if (existingAnalysis.length > 0) {
    // Update existing record
    await db
      .update(ocrAnalysis)
      .set({
        textractJobId,
        jobStatus: 'IN_PROGRESS',
        startedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(ocrAnalysis.id, existingAnalysis[0].id));
  } else {
    // Create new record with minimal data
    await db.insert(ocrAnalysis).values({
      documentId,
      textractJobId,
      jobStatus: 'IN_PROGRESS',
      startedAt: new Date(),
      analysisData: {}, // Will be populated when job completes
    });
  }
}

export async function updateTextractJobStatus(
  textractJobId: string,
  status: 'IN_PROGRESS' | 'SUCCEEDED' | 'FAILED' | 'PARTIAL_SUCCESS',
  snsMessageId?: string,
  completedAt?: Date
) {
  const updateData: {
    jobStatus: 'IN_PROGRESS' | 'SUCCEEDED' | 'FAILED' | 'PARTIAL_SUCCESS';
    updatedAt: Date;
    snsMessageId?: string;
    completedAt?: Date;
  } = {
    jobStatus: status,
    updatedAt: new Date(),
  };

  if (snsMessageId) {
    updateData.snsMessageId = snsMessageId;
  }

  if (completedAt) {
    updateData.completedAt = completedAt;
  }

  await db.update(ocrAnalysis).set(updateData).where(eq(ocrAnalysis.textractJobId, textractJobId));
}

export async function getTextractJobInfo(textractJobId: string) {
  const result = await db
    .select()
    .from(ocrAnalysis)
    .where(eq(ocrAnalysis.textractJobId, textractJobId))
    .limit(1);

  return result[0] || null;
}
