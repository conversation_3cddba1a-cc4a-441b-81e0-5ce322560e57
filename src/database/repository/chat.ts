import { db } from '@/database/drizzle';
import { documentChunks, documents, RetrievedDocumentChunkStore, topics } from '@/database/schema';
import type { AgentStep } from '@/hooks/use-chat-enhanced';
import type { RetrievedDocumentChunkDTO } from '@/types/chat';
import { eq, inArray } from 'drizzle-orm';

export type EnhancedTopic = Awaited<ReturnType<typeof getTopicWithDocuments>>;

export type TopicWithDocuments = typeof topics.$inferSelect & {
  interactions: {
    id: string;
    content: string;
    senderType: 'user' | 'ai';
    createdAt: Date;
    retrievedDocuments?: RetrievedDocumentChunkDTO[];
    agentSteps?: AgentStep[];
  }[];
};

async function getTopicWithDocuments(topicId: string): Promise<TopicWithDocuments | null> {
  // First get the topic with its interactions
  const topic = await db.query.topics.findFirst({
    where: eq(topics.id, topicId),
    with: {
      interactions: {
        orderBy: (interactions, { asc }) => [asc(interactions.createdAt)],
        columns: {
          id: true,
          content: true,
          senderType: true,
          createdAt: true,
          retrievedDocuments: true,
          agentSteps: true,
        },
      },
    },
  });

  if (!topic) return null;

  // Get all unique chunk IDs from interactions
  const chunkIds = new Set<number>();
  topic.interactions.forEach((interaction) => {
    if (interaction.retrievedDocuments) {
      const documents = interaction.retrievedDocuments as RetrievedDocumentChunkStore[];
      documents.forEach((doc) => chunkIds.add(doc.chunkId));
    }
  });

  // If there are any chunk IDs, fetch their details
  const chunkDetails: Record<number, RetrievedDocumentChunkDTO> = {};
  if (chunkIds.size > 0) {
    const chunks = await db
      .select({
        id: documents.id,
        chunkId: documentChunks.id,
        content: documentChunks.content,
        fileName: documents.fileName,
        key: documents.s3Key,
        fileType: documents.fileType,
      })
      .from(documentChunks)
      .innerJoin(documents, eq(documentChunks.documentId, documents.id))
      .where(inArray(documentChunks.id, Array.from(chunkIds)));

    chunks.forEach((chunk) => {
      chunkDetails[chunk.chunkId] = {
        id: chunk.id,
        chunkId: chunk.chunkId,
        fileName: chunk.fileName || 'Unknown',
        content: chunk.content,
        key: chunk.key,
        similarity: 0,
        fileType: chunk.fileType,
        metadata: {},
      };
    });
  }

  // Enhance the interactions with full document information
  return {
    ...topic,
    interactions: topic.interactions.map((interaction) => {
      const baseInteraction = {
        ...interaction,
        agentSteps: interaction.agentSteps ? (interaction.agentSteps as AgentStep[]) : undefined,
      };

      if (!interaction.retrievedDocuments) {
        return {
          ...baseInteraction,
          retrievedDocuments: undefined,
        } as const;
      }

      const documents = interaction.retrievedDocuments as RetrievedDocumentChunkStore[];
      const enhancedDocuments = documents
        .map((doc) => {
          const chunkDetail = chunkDetails[doc.chunkId];
          if (!chunkDetail) return null;
          return {
            ...chunkDetail,
            similarity: doc.similarity,
            metadata: doc.metadata || {},
          };
        })
        .filter((doc): doc is NonNullable<typeof doc> => doc !== null);

      return {
        ...baseInteraction,
        retrievedDocuments: enhancedDocuments.length > 0 ? enhancedDocuments : undefined,
      } as const;
    }),
  };
}

export const ChatRepository = {
  getTopicWithDocuments,
} as const;
