import { randomBytes } from 'crypto';
import { db } from '@/database/drizzle';
import { users } from '@/database/schema';
import { hash } from '@node-rs/argon2';
import { eq } from 'drizzle-orm';

export const UserRepository = {
  createUser,
  isUserExists,
  getUserByEmail,
  createVerificationToken,
  verifyEmail,
  createPasswordResetToken,
  validateResetToken,
  resetPassword,
  getUserById,
};

async function createUser(name: string, email: string, password: string) {
  if (await isUserExists(email)) {
    throw new Error('User already exists');
  }

  const hashedPassword = await hash(password, {
    memoryCost: 19456,
    timeCost: 2,
    outputLen: 32,
    parallelism: 1,
  });

  const [user] = await db
    .insert(users)
    .values({
      name,
      email,
      password: hashedPassword,
    })
    .returning();

  return user;
}

async function isUserExists(email: string) {
  const user = await db.select().from(users).where(eq(users.email, email)).limit(1);
  return user.length > 0;
}

async function getUserByEmail(email: string) {
  const [user] = await db.select().from(users).where(eq(users.email, email)).limit(1);
  return user || null;
}

async function getUserById(id: string) {
  const [user] = await db.select().from(users).where(eq(users.id, id)).limit(1);
  return user || null;
}

async function createVerificationToken(userId: string): Promise<string> {
  const token = randomBytes(32).toString('hex');
  const expiryTime = new Date();
  expiryTime.setHours(expiryTime.getHours() + 24); // Token expires in 24 hours

  await db
    .update(users)
    .set({
      verificationToken: token,
      verificationTokenExpiry: expiryTime,
    })
    .where(eq(users.id, userId));

  return token;
}

async function verifyEmail(token: string): Promise<boolean> {
  const now = new Date();
  const [user] = await db.select().from(users).where(eq(users.verificationToken, token)).limit(1);

  if (!user || !user.verificationTokenExpiry || user.verificationTokenExpiry < now) {
    return false;
  }

  await db
    .update(users)
    .set({
      emailVerified: true,
      verificationToken: null,
      verificationTokenExpiry: null,
      updatedAt: now,
    })
    .where(eq(users.id, user.id));

  return true;
}

async function createPasswordResetToken(email: string): Promise<string | null> {
  const user = await getUserByEmail(email);
  if (!user) {
    return null;
  }

  const token = randomBytes(32).toString('hex');
  const expiryTime = new Date();
  expiryTime.setHours(expiryTime.getHours() + 1); // Token expires in 1 hour

  await db
    .update(users)
    .set({
      resetToken: token,
      resetTokenExpiry: expiryTime,
    })
    .where(eq(users.id, user.id));

  return token;
}

async function validateResetToken(token: string): Promise<boolean> {
  const now = new Date();
  const [user] = await db.select().from(users).where(eq(users.resetToken, token)).limit(1);

  // Token is valid if:
  // 1. User exists with this token
  // 2. Token has not expired
  return !!(user && user.resetTokenExpiry && user.resetTokenExpiry > now);
}

async function resetPassword(token: string, newPassword: string): Promise<boolean> {
  const now = new Date();
  const [user] = await db.select().from(users).where(eq(users.resetToken, token)).limit(1);

  if (!user || !user.resetTokenExpiry || user.resetTokenExpiry < now) {
    return false;
  }

  const hashedPassword = await hash(newPassword, {
    memoryCost: 19456,
    timeCost: 2,
    outputLen: 32,
    parallelism: 1,
  });

  await db
    .update(users)
    .set({
      password: hashedPassword,
      resetToken: null,
      resetTokenExpiry: null,
      updatedAt: now,
    })
    .where(eq(users.id, user.id));

  return true;
}
