import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Client } from 'pg';

dotenvExpand.expand(dotenv.config());

async function main() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Successfully connected to the database');

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const db = drizzle(client);
    console.log('Drizzle ORM initialized');

    // const result = await db.select().from(yourTable).execute();
    // console.log('Test query result:', result);
  } catch (error) {
    console.error('Error connecting to the database:', error);
  } finally {
    await client.end();
  }
}

main();
