import { env } from '@/lib/env.mjs';
import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as schema from './schema';

dotenvExpand.expand(dotenv.config());

const pool = new Pool({
  connectionString: env.DATABASE_URL,
  ssl: env.NEXT_PUBLIC_APP_ENV === 'production',
  max: 10, // maximum number of clients in the pool
  idleTimeoutMillis: 30000, // how long a client is allowed to remain idle before being closed
});

export const db = drizzle(pool, { schema });

// Optionally, you can add an error handler for the pool
pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});
