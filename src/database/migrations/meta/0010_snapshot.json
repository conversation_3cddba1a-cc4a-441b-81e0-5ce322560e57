{"id": "61697693-8ac3-49c9-92e0-be5c40322122", "prevId": "6a3613fc-36aa-40ea-94d3-4be52eef2113", "version": "7", "dialect": "postgresql", "tables": {"public.document_chunks": {"name": "document_chunks", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}, "chunk_order": {"name": "chunk_order", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"content_index": {"name": "content_index", "columns": [{"expression": "to_tsvector('simple', \"content\")", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gin", "with": {}}, "document_id_index": {"name": "document_id_index", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "embedding_index": {"name": "embedding_index", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}, "chunk_order_index": {"name": "chunk_order_index", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chunk_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"document_chunks_document_id_documents_id_fk": {"name": "document_chunks_document_id_documents_id_fk", "tableFrom": "document_chunks", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.document_spaces": {"name": "document_spaces", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "document_id": {"name": "document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"document_space_index": {"name": "document_space_index", "columns": [{"expression": "document_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"document_spaces_document_id_documents_id_fk": {"name": "document_spaces_document_id_documents_id_fk", "tableFrom": "document_spaces", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "document_spaces_space_id_spaces_id_fk": {"name": "document_spaces_space_id_spaces_id_fk", "tableFrom": "document_spaces", "tableTo": "spaces", "columnsFrom": ["space_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "document_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "s3_key": {"name": "s3_key", "type": "text", "primaryKey": false, "notNull": true}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"uploaded_by_index": {"name": "uploaded_by_index", "columns": [{"expression": "uploaded_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documents_uploaded_by_users_id_fk": {"name": "documents_uploaded_by_users_id_fk", "tableFrom": "documents", "tableTo": "users", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"documents_s3_key_unique": {"name": "documents_s3_key_unique", "nullsNotDistinct": false, "columns": ["s3_key"]}}}, "public.interactions": {"name": "interactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "topic_id": {"name": "topic_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "sender_type": {"name": "sender_type", "type": "senderType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "retrieved_documents": {"name": "retrieved_documents", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_topic_index": {"name": "user_topic_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "topic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "created_at_index": {"name": "created_at_index", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"interactions_topic_id_topics_id_fk": {"name": "interactions_topic_id_topics_id_fk", "tableFrom": "interactions", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "interactions_user_id_users_id_fk": {"name": "interactions_user_id_users_id_fk", "tableFrom": "interactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_users_id_fk": {"name": "session_user_id_users_id_fk", "tableFrom": "session", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.space_invitations": {"name": "space_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "use_count": {"name": "use_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"space_id_index": {"name": "space_id_index", "columns": [{"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "code_index": {"name": "code_index", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"space_invitations_space_id_spaces_id_fk": {"name": "space_invitations_space_id_spaces_id_fk", "tableFrom": "space_invitations", "tableTo": "spaces", "columnsFrom": ["space_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "space_invitations_created_by_users_id_fk": {"name": "space_invitations_created_by_users_id_fk", "tableFrom": "space_invitations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"space_invitations_code_unique": {"name": "space_invitations_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}}, "public.space_members": {"name": "space_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "space_id": {"name": "space_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "space_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'viewer'"}, "invitation_id": {"name": "invitation_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_space_index": {"name": "user_space_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "space_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"space_members_space_id_spaces_id_fk": {"name": "space_members_space_id_spaces_id_fk", "tableFrom": "space_members", "tableTo": "spaces", "columnsFrom": ["space_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "space_members_user_id_users_id_fk": {"name": "space_members_user_id_users_id_fk", "tableFrom": "space_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "space_members_invitation_id_space_invitations_id_fk": {"name": "space_members_invitation_id_space_invitations_id_fk", "tableFrom": "space_members", "tableTo": "space_invitations", "columnsFrom": ["invitation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.spaces": {"name": "spaces", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_shared": {"name": "is_shared", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"owner_id_index": {"name": "owner_id_index", "columns": [{"expression": "owner_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"spaces_owner_id_users_id_fk": {"name": "spaces_owner_id_users_id_fk", "tableFrom": "spaces", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.topics": {"name": "topics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_id_index": {"name": "user_id_index", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"topics_user_id_users_id_fk": {"name": "topics_user_id_users_id_fk", "tableFrom": "topics", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verification_token": {"name": "verification_token", "type": "text", "primaryKey": false, "notNull": false}, "verification_token_expiry": {"name": "verification_token_expiry", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "reset_token": {"name": "reset_token", "type": "text", "primaryKey": false, "notNull": false}, "reset_token_expiry": {"name": "reset_token_expiry", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"email_index": {"name": "email_index", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}}, "enums": {"public.document_type": {"name": "document_type", "schema": "public", "values": ["pdf", "docx", "txt", "other"]}, "public.senderType": {"name": "senderType", "schema": "public", "values": ["user", "ai"]}, "public.space_role": {"name": "space_role", "schema": "public", "values": ["owner", "editor", "viewer"]}}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}