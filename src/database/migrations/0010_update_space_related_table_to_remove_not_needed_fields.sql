ALTER TABLE "space_members" ADD COLUMN "invitation_id" integer;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "space_members" ADD CONSTRAINT "space_members_invitation_id_space_invitations_id_fk" FOREIGN KEY ("invitation_id") REFERENCES "public"."space_invitations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "space_invitations" DROP COLUMN IF EXISTS "expires_at";--> statement-breakpoint
ALTER TABLE "space_invitations" DROP COLUMN IF EXISTS "max_uses";