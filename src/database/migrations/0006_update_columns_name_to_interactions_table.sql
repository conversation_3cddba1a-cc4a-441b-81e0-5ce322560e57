DO $$ BEGIN
 CREATE TYPE "public"."senderType" AS ENUM('user', 'ai');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "interactions" ADD COLUMN "content" text NOT NULL;--> statement-breakpoint
ALTER TABLE "interactions" ADD COLUMN "sender_type" "senderType" NOT NULL;--> statement-breakpoint
ALTER TABLE "interactions" DROP COLUMN IF EXISTS "query";--> statement-breakpoint
ALTER TABLE "interactions" DROP COLUMN IF EXISTS "ai_response";--> statement-breakpoint
ALTER TABLE "interactions" DROP COLUMN IF EXISTS "is_ai_enabled";