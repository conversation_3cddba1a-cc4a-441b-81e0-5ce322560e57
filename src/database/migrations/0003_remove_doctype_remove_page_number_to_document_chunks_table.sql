DO $$ BEGIN
 CREATE TYPE "public"."document_type" AS ENUM('pdf', 'docx', 'txt', 'other');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
ALTER TABLE "document_chunks" DROP COLUMN IF EXISTS "page_number";--> statement-breakpoint
ALTER TABLE "documents" DROP COLUMN IF EXISTS "file_type";--> statement-breakpoint
ALTER TABLE "documents" ADD CONSTRAINT "documents_s3_key_unique" UNIQUE("s3_key");