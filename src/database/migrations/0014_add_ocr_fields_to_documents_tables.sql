DO $$ BEGIN
 CREATE TYPE "public"."document_status" AS ENUM('uploaded', 'processing', 'completed', 'failed');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."layout_type" AS ENUM('PARAGRAPH', 'TITLE', 'HEADER', 'FOOTER', 'SECTION_HEADER', 'LINE', 'LAYOUT_TEXT', 'UNKNOWN');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ocr_analysis" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"document_id" uuid NOT NULL,
	"textract_job_id" text,
	"analysis_data" jsonb NOT NULL,
	"processing_time" integer,
	"total_blocks" integer,
	"average_confidence" integer,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "document_chunks" ADD COLUMN "bounding_box" jsonb;--> statement-breakpoint
ALTER TABLE "document_chunks" ADD COLUMN "polygon_coordinates" jsonb;--> statement-breakpoint
ALTER TABLE "document_chunks" ADD COLUMN "layout_type" "layout_type";--> statement-breakpoint
ALTER TABLE "document_chunks" ADD COLUMN "confidence_score" integer;--> statement-breakpoint
ALTER TABLE "documents" ADD COLUMN "status" "document_status" DEFAULT 'uploaded' NOT NULL;--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ocr_analysis" ADD CONSTRAINT "ocr_analysis_document_id_documents_id_fk" FOREIGN KEY ("document_id") REFERENCES "public"."documents"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "ocr_document_id_index" ON "ocr_analysis" USING btree ("document_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "textract_job_id_index" ON "ocr_analysis" USING btree ("textract_job_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "layout_type_index" ON "document_chunks" USING btree ("layout_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "confidence_score_index" ON "document_chunks" USING btree ("confidence_score");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "status_index" ON "documents" USING btree ("status");