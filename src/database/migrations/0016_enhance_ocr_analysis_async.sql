ALTER TABLE "ocr_analysis" ADD COLUMN "job_status" text;--> statement-breakpoint
ALTER TABLE "ocr_analysis" ADD COLUMN "sns_message_id" text;--> statement-breakpoint
ALTER TABLE "ocr_analysis" ADD COLUMN "started_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "ocr_analysis" ADD COLUMN "completed_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "ocr_analysis" ADD COLUMN "page_count" integer;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_ocr_analysis_job_status" ON "ocr_analysis" USING btree ("job_status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_ocr_analysis_textract_job_id_active" ON "ocr_analysis" USING btree ("textract_job_id");