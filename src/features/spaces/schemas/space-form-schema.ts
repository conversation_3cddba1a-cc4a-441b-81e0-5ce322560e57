import { z } from 'zod';

export const spaceFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Space name is required')
    .max(50, 'Space name must be less than 50 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .nullish()
    .transform((val) => val || null),
  materialIds: z.array(z.string()).optional(),
});

export type SpaceFormValues = z.infer<typeof spaceFormSchema>;
