'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { MultiSelect } from '@/components/ui/multi-select';
import { Textarea } from '@/components/ui/textarea';
import { spaceFormSchema, type SpaceFormValues } from '@/features/spaces/schemas/space-form-schema';
import { useNestedModals } from '@/hooks/use-nested-modals';
import { clientApi } from '@/lib/trpc/client-api';
import { useUserStore } from '@/stores/user';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useState } from 'react';

type CreateSpaceDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function CreateSpaceDialog({ open, onOpenChange }: CreateSpaceDialogProps) {
  const [loading, setLoading] = useState(false);
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([]);
  const utils = clientApi.useUtils();
  const user = useUserStore((state) => state.user);
  const { handleInteractOutside } = useNestedModals();

  const form = useForm<SpaceFormValues>({
    resolver: zodResolver(spaceFormSchema),
    defaultValues: {
      name: '',
      description: '',
      materialIds: [],
    },
  });

  const { data: materials } = clientApi.material.list.useQuery(undefined, {
    enabled: !!user,
  });

  const createMutation = clientApi.space.create.useMutation({
    onSuccess: () => {
      toast.success('Space created successfully');
      utils.space.list.invalidate();
      onOpenChange(false);
      setSelectedMaterials([]);
      form.reset();
    },
    onError: (error) => {
      toast.error('Failed to create space');
      console.error(error);
    },
  });

  async function onSubmit(values: SpaceFormValues) {
    try {
      setLoading(true);
      await createMutation.mutateAsync({
        ...values,
        materialIds: selectedMaterials,
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="flex max-h-[90vh] flex-col gap-0 p-0"
        onInteractOutside={handleInteractOutside}
      >
        <DialogHeader className="flex-none p-6">
          <DialogTitle>Create New Space</DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Space name" disabled={loading} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Space description (optional)"
                        disabled={loading}
                        className="h-32"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormItem>
                <FormLabel>Materials</FormLabel>
                <FormControl>
                  <MultiSelect
                    options={(materials ?? []).map((material) => ({
                      label: material.fileName ?? 'Untitled',
                      value: material.id,
                    }))}
                    value={selectedMaterials}
                    onValueChange={setSelectedMaterials}
                    placeholder="Select materials..."
                    disabled={loading}
                    modalPopover={true}
                  />
                </FormControl>
              </FormItem>

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  Create Space
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
