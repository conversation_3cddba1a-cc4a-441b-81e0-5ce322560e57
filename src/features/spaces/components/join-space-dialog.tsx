'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { clientApi } from '@/lib/trpc/client-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, UsersRound } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useState } from 'react';

const joinSpaceSchema = z.object({
  code: z.string().min(1, 'Invitation code is required'),
});

type JoinSpaceFormValues = z.infer<typeof joinSpaceSchema>;

type JoinSpaceDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function JoinSpaceDialog({ open, onOpenChange }: JoinSpaceDialogProps) {
  const [loading, setLoading] = useState(false);
  const utils = clientApi.useUtils();

  const form = useForm<JoinSpaceFormValues>({
    resolver: zodResolver(joinSpaceSchema),
    defaultValues: {
      code: '',
    },
  });

  const joinMutation = clientApi.space.joinSpace.useMutation({
    onSuccess: (result) => {
      toast.success(result.message || 'Successfully joined space');
      utils.space.list.invalidate();
      utils.space.listAll.invalidate();
      onOpenChange(false);
      form.reset();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to join space');
    },
    onSettled: () => {
      setLoading(false);
    },
  });

  async function onSubmit(values: JoinSpaceFormValues) {
    setLoading(true);
    joinMutation.mutate(values);
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!loading) {
          onOpenChange(open);
          if (!open) {
            form.reset();
          }
        }
      }}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UsersRound className="h-5 w-5" />
            Join a Space
          </DialogTitle>
          <DialogDescription>Enter an invitation code to join a shared space</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Invitation Code</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter invitation code" disabled={loading} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Joining...
                  </>
                ) : (
                  'Join Space'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
