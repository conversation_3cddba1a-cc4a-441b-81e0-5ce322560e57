'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { MultiSelect } from '@/components/ui/multi-select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import type { Document, Space } from '@/database/schema';
import { spaceFormSchema, type SpaceFormValues } from '@/features/spaces/schemas/space-form-schema';
import { useNestedModals } from '@/hooks/use-nested-modals';
import { clientApi } from '@/lib/trpc/client-api';
import { formatDate } from '@/lib/utils';
import { useUserStore } from '@/stores/user';
import { zodResolver } from '@hookform/resolvers/zod';
import { ExternalLink, MoreHorizontal, Plus } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import Link from 'next/link';
import { useEffect, useState } from 'react';

type EditSpaceDialogProps = {
  space: Space;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
};

export function EditSpaceDialog({ space, open, onOpenChange, onSuccess }: EditSpaceDialogProps) {
  const [loading, setLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [availableDocuments, setAvailableDocuments] = useState<Document[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const currentUser = useUserStore((state) => state.user);
  const utils = clientApi.useUtils();
  const { handleInteractOutside } = useNestedModals();

  // Check if user is the owner of the space
  const isOwner = currentUser?.id === space.ownerId;
  // For now, only owners can edit - in the future this can be updated to include editors
  const canEdit = isOwner;

  const form = useForm<SpaceFormValues>({
    resolver: zodResolver(spaceFormSchema),
    defaultValues: {
      name: space.name,
      description: space.description ?? '',
    },
  });

  const { data: allDocuments } = clientApi.material.list.useQuery(undefined);
  const { data: spaceDocuments } = clientApi.space.getDocuments.useQuery(space.id, {
    enabled: open,
  });

  useEffect(() => {
    if (spaceDocuments) {
      setDocuments(spaceDocuments);
    }
  }, [spaceDocuments]);

  useEffect(() => {
    if (allDocuments && documents) {
      setAvailableDocuments(allDocuments.filter((doc) => !documents.some((d) => d.id === doc.id)));
    }
  }, [allDocuments, documents]);

  const updateMutation = clientApi.space.update.useMutation({
    onSuccess: () => {
      toast.success('Space updated successfully');
      utils.space.list.invalidate();
      onOpenChange(false);
      onSuccess?.();
    },
    onError: (error) => {
      toast.error('Failed to update space');
      console.error(error);
    },
  });

  const addDocumentsMutation = clientApi.space.addDocuments.useMutation({
    onSuccess: () => {
      toast.success('Documents added successfully');
      utils.space.getDocuments.invalidate(space.id);
      utils.space.list.invalidate();
      setSelectedDocuments([]);
    },
    onError: (error) => {
      toast.error('Failed to add documents');
      console.error(error);
    },
  });

  const removeDocumentMutation = clientApi.space.removeDocument.useMutation({
    onSuccess: () => {
      toast.success('Document removed successfully');
      utils.space.getDocuments.invalidate(space.id);
      utils.space.list.invalidate();
    },
    onError: (error) => {
      toast.error('Failed to remove document');
      console.error(error);
    },
  });

  async function onSubmit(values: SpaceFormValues) {
    try {
      setLoading(true);
      await updateMutation.mutateAsync({
        id: space.id,
        ...values,
      });
    } finally {
      setLoading(false);
    }
  }

  async function onAddDocuments() {
    if (selectedDocuments.length === 0) return;

    try {
      setLoading(true);
      await addDocumentsMutation.mutateAsync({
        documentIds: selectedDocuments,
        spaceId: space.id,
      });
    } finally {
      setLoading(false);
    }
  }

  async function onRemoveDocument(documentId: string) {
    try {
      setLoading(true);
      await removeDocumentMutation.mutateAsync({
        documentId,
        spaceId: space.id,
      });
    } finally {
      setLoading(false);
    }
  }

  const dialogTitle = canEdit ? 'Edit Space' : 'View Space';

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent
        className="flex max-h-[90vh] max-w-3xl flex-col gap-0 p-0"
        onInteractOutside={handleInteractOutside}
      >
        <DialogHeader className="flex-none p-6">
          <DialogTitle>{dialogTitle}</DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Space name"
                          disabled={loading || !canEdit}
                          readOnly={!canEdit}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Space description"
                          disabled={loading || !canEdit}
                          readOnly={!canEdit}
                          className="h-32"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {canEdit && (
                  <div className="flex justify-between gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => onOpenChange(false)}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={loading}>
                      Save Changes
                    </Button>
                  </div>
                )}

                {!canEdit && (
                  <div className="flex justify-end">
                    <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                      Close
                    </Button>
                  </div>
                )}
              </form>
            </Form>

            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <h3 className="text-lg font-medium">Documents</h3>
                {canEdit && (
                  <div className="flex items-start gap-2">
                    <MultiSelect
                      options={availableDocuments.map((doc) => ({
                        label: doc.fileName,
                        value: doc.id,
                      }))}
                      value={selectedDocuments}
                      onValueChange={setSelectedDocuments}
                      placeholder="Select documents..."
                      disabled={loading}
                      modalPopover={true}
                    />
                    <Button
                      type="button"
                      size="sm"
                      onClick={onAddDocuments}
                      disabled={loading || selectedDocuments.length === 0}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add
                    </Button>
                  </div>
                )}
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Added Date</TableHead>
                    {canEdit && <TableHead className="w-[70px]"></TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documents.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell>
                        <Link
                          href={`/materials/${doc.id}`}
                          className="flex w-fit items-center gap-2 font-medium text-foreground hover:underline"
                        >
                          {doc.fileName}
                          <ExternalLink className="h-3 w-3 text-gray-400" />
                        </Link>
                      </TableCell>
                      <TableCell>{formatDate(doc.createdAt)}</TableCell>
                      {canEdit && (
                        <TableCell>
                          <div className="flex justify-end">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => onRemoveDocument(doc.id)}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  Remove
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                  {documents.length === 0 && (
                    <TableRow>
                      <TableCell
                        colSpan={canEdit ? 3 : 2}
                        className="text-center text-muted-foreground"
                      >
                        No documents added yet
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
