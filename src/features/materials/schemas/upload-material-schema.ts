import { z } from 'zod';

// import { zfd } from 'zod-form-data';

export const uploadMaterialSchema = z.object({
  files: z
    .custom<File[]>(
      (value) => Array.isArray(value) && value.every((item) => item instanceof File),
      {
        message: 'Must be an array of Files',
      }
    )
    .refine((files) => files.length > 0, 'At least one file is required')
    .refine((files) => files.every((file) => file.size > 0), 'All files must have content')
    .refine(
      // (files) => files.every((file) => file.size <= 5 * 1024 * 1024), // 5MB
      (files) => files.every((file) => file.size <= 400 * 1024 * 1024), // 400MB
      'Each file size must be less than 400MB'
    ),
});

// export const uploadMaterialSchema = zfd.formData({
//   name: zfd.text(z.string().min(1, 'Name is required')),
//   files: zfd
//     .file()
//     .array()
//     .min(1, 'At least one file is required')
//     .refine((files) => files.every((file) => file.size > 0), 'All files must have content')
//     .refine(
//       (files) => files.every((file) => file.size <= 5000000),
//       'Each file size must be less than 5MB'
//     ),
// });

export type UploadMaterialInput = z.infer<typeof uploadMaterialSchema>;
