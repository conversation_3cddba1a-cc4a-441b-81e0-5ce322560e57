import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { MultiSelect } from '@/components/ui/multi-select';
import { uploadMaterialSchema } from '@/features/materials/schemas/upload-material-schema';
import { useNestedModals } from '@/hooks/use-nested-modals';
import { clientApi } from '@/lib/trpc/client-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useState } from 'react';
import { FileUploader } from './file-uploader';

type UploadMaterialDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function UploadMaterialDialog({ open, onOpenChange }: UploadMaterialDialogProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [selectedSpaces, setSelectedSpaces] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<number, number>>({});
  const utils = clientApi.useUtils();
  const { handleInteractOutside } = useNestedModals();

  const { data: spaces = [], isLoading: isLoadingSpaces } = clientApi.space.list.useQuery();

  const form = useForm<z.infer<typeof uploadMaterialSchema>>({
    resolver: zodResolver(uploadMaterialSchema),
    defaultValues: {
      files: [],
    },
  });

  const onSubmit = async (values: z.infer<typeof uploadMaterialSchema>) => {
    if (values.files.length === 0) {
      toast.error('Please select at least one file to upload');
      return;
    }

    setIsUploading(true);
    setUploadProgress({});

    try {
      // Step 1: Get presigned URLs for each file
      const fileInfo = values.files.map((file) => {
        const customFile = file as File & { customName?: string };
        return {
          name: customFile.customName || file.name, // Use custom name if available
          type: file.type,
          size: file.size,
        };
      });

      const presignedResponse = await fetch('/api/materials/presigned-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          files: fileInfo,
          spaceIds: selectedSpaces,
        }),
      });

      if (!presignedResponse.ok) {
        throw new Error('Failed to get upload URLs');
      }

      const { urls } = await presignedResponse.json();

      type PresignedUrlInfo = {
        url: string;
        key: string;
        fileIndex: number;
        originalName: string;
      };

      // Step 2: Upload files directly to R2 using presigned URLs
      const uploadPromises = urls.map(async (urlInfo: PresignedUrlInfo) => {
        const fileIndex = urlInfo.fileIndex;
        const file = values.files[fileIndex];

        try {
          const xhr = new XMLHttpRequest();

          // Setup progress tracking
          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const percentComplete = Math.round((event.loaded / event.total) * 100);
              setUploadProgress((prev) => ({
                ...prev,
                [fileIndex]: percentComplete,
              }));
            }
          });

          // Create a promise to track completion
          const uploadPromise = new Promise<{
            key: string;
            name: string;
            type: string;
            size: number;
          }>((resolve, reject) => {
            xhr.onload = () => {
              if (xhr.status >= 200 && xhr.status < 300) {
                const customFile = file as File & { customName?: string };
                resolve({
                  key: urlInfo.key,
                  name: customFile.customName || file.name, // Use custom name
                  type: file.type,
                  size: file.size,
                });
              } else {
                reject(new Error(`Upload failed with status ${xhr.status}`));
              }
            };
            xhr.onerror = () => reject(new Error('Upload failed'));
          });

          // Start the upload
          xhr.open('PUT', urlInfo.url, true);
          xhr.setRequestHeader('Content-Type', file.type);
          xhr.send(file);

          return uploadPromise;
        } catch (error) {
          console.error(`Error uploading file ${file.name}:`, error);
          throw error;
        }
      });

      // Wait for all uploads to complete
      const uploadedFiles = await Promise.all(uploadPromises);

      // Step 3: Notify the server about successful uploads
      const processResponse = await fetch('/api/materials/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          files: uploadedFiles,
          spaceIds: selectedSpaces,
        }),
      });

      if (!processResponse.ok) {
        throw new Error('Processing uploaded files failed');
      }

      const processResult = await processResponse.json();

      if (!processResult.success) {
        throw new Error(processResult.error || 'Processing failed');
      }

      // Show success message with workflow processing info
      if (processResult.failedUploads && processResult.failedUploads.length > 0) {
        toast.warning(
          `${processResult.uploadedFiles.length} files uploaded successfully and processing started, 
          ${processResult.failedUploads.length} failed. Check console for details.`
        );
        console.error('Failed uploads:', processResult.failedUploads);
      } else {
        toast.success(
          `${processResult.uploadedFiles.length} file(s) uploaded successfully! 
          Processing has started in the background. You'll receive notifications when complete.`,
          { duration: 5000 }
        );
      }

      // Log workflow information for debugging
      console.log('Workflow processing started:', {
        uploadedFiles: processResult.uploadedFiles,
        message: processResult.message,
      });

      utils.material.list.invalidate();
      onOpenChange(false);
      form.reset();
      setSelectedSpaces([]);
    } catch (error) {
      console.error('Upload error:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload materials');
    } finally {
      setIsUploading(false);
      setUploadProgress({});
    }
  };

  const cleanup = () => {
    form.reset();
    setSelectedSpaces([]);
    setIsUploading(false);
    setUploadProgress({});
  };

  const handleClose = () => {
    cleanup();
    onOpenChange(false);
  };

  // Calculate overall progress
  const calculateOverallProgress = () => {
    if (Object.keys(uploadProgress).length === 0) return 0;
    const totalProgress = Object.values(uploadProgress).reduce((sum, value) => sum + value, 0);
    return Math.round(totalProgress / Object.keys(uploadProgress).length);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl" onInteractOutside={handleInteractOutside}>
        <DialogHeader className="flex-none p-6">
          <DialogTitle>Upload Materials</DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="files"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Files</FormLabel>
                    <FormControl>
                      <FileUploader
                        value={field.value as File[]}
                        onValueChange={field.onChange}
                        maxFileCount={4}
                        maxSize={400 * 1024 * 1024} // 400 mb
                        disabled={isUploading}
                        progresses={uploadProgress}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  Spaces
                  {isLoadingSpaces && (
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  )}
                </FormLabel>
                <FormControl>
                  <MultiSelect
                    value={selectedSpaces}
                    onValueChange={setSelectedSpaces}
                    options={spaces.map((space) => ({
                      label: space.name,
                      value: space.id,
                    }))}
                    placeholder={isLoadingSpaces ? 'Loading spaces...' : 'Select spaces...'}
                    disabled={isUploading || isLoadingSpaces}
                    modalPopover={true}
                  />
                </FormControl>
              </FormItem>

              {isUploading && Object.keys(uploadProgress).length > 0 && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">
                    Overall Progress: {calculateOverallProgress()}%
                  </div>
                  {Object.entries(uploadProgress).map(([fileIndex, progress]) => (
                    <div key={fileIndex} className="space-y-1">
                      <div className="text-xs text-muted-foreground">
                        {form.getValues().files[Number(fileIndex)].name}: {progress}%
                      </div>
                      <div className="h-1.5 w-full overflow-hidden rounded-full bg-muted">
                        <div
                          className="h-full bg-primary transition-all"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isUploading}
                >
                  Cancel
                </Button>
                <Button onClick={form.handleSubmit(onSubmit)} disabled={isUploading}>
                  {isUploading ? 'Uploading...' : 'Upload'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
