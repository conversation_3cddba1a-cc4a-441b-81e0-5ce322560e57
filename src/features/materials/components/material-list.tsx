'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { MultiSelect } from '@/components/ui/multi-select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { DocumentWithSpaces } from '@/database/repository/space';
import { clientApi } from '@/lib/trpc/client-api';
import { formatDate } from '@/lib/utils';
import { useUserStore } from '@/stores/user';
import {
  ArrowUpDown,
  ExternalLink,
  Eye,
  FolderClosed,
  MoreHorizontal,
  Pencil,
  Trash2,
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { AddToSpaceDialog } from './add-to-space-dialog';
import { DocumentStatusBadge } from './document-status-badge';
import { EditFilenameDialog } from './edit-filename-dialog';

type MaterialItemProps = {
  material: DocumentWithSpaces;
};

function MaterialItem({ material }: MaterialItemProps) {
  const utils = clientApi.useUtils();
  const currentUser = useUserStore((state) => state.user);
  const [isAddToSpaceOpen, setIsAddToSpaceOpen] = useState(false);
  const [isEditFilenameOpen, setIsEditFilenameOpen] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  const deleteMutation = clientApi.material.delete.useMutation({
    onSuccess: () => {
      toast.success('Material deleted successfully');
      utils.material.list.invalidate();
    },
    onError: (error) => {
      toast.error('Failed to delete material');
      console.error(error);
    },
  });

  async function onDelete() {
    if (!confirm('Are you sure you want to delete this material?')) return;
    await deleteMutation.mutateAsync(material.id);
  }

  const handleRetry = useCallback(async () => {
    setIsRetrying(true);
    try {
      const response = await fetch(`/api/materials/retry/${material.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        throw new Error('Failed to retry processing');
      }

      const result = await response.json();
      if (result.success) {
        toast.success('Document processing restarted');
        utils.material.list.invalidate();
      } else {
        throw new Error(result.error || 'Retry failed');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to retry processing');
    } finally {
      setIsRetrying(false);
    }
  }, [material.id, utils.material.list]);

  // Determine if any of the spaces this material belongs to are shared
  const isInSharedSpace = material.documentSpaces.some((ds) => ds.space.isShared);

  // Determine if user is owner of any spaces this material belongs to
  const isOwnerOfAnySpace = material.documentSpaces.some(
    (ds) => ds.space.ownerId === currentUser?.id
  );

  // Determine sharing status text and badge variant
  const getSharingStatus = () => {
    if (!material.documentSpaces.length) return null;
    if (!isInSharedSpace) return 'Private';
    if (isOwnerOfAnySpace) return 'Shared';
    return 'Member Access';
  };

  const getBadgeVariant = () => {
    if (!material.documentSpaces.length) return 'outline';
    if (!isInSharedSpace) return 'secondary';
    if (isOwnerOfAnySpace) return 'default';
    return 'outline';
  };

  return (
    <>
      <TableRow>
        <TableCell>
          <Link
            href={`/materials/${material.id}`}
            className="flex w-fit items-center gap-2 font-medium text-foreground hover:underline"
          >
            {material.fileName}
            <ExternalLink className="h-3 w-3 text-gray-400" />
          </Link>
        </TableCell>
        <TableCell>
          {material.documentSpaces.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {material.documentSpaces.map((ds) => (
                <Badge key={ds.id} variant="outline">
                  {ds.space.name}
                </Badge>
              ))}
            </div>
          ) : (
            <span className="text-muted-foreground"></span>
          )}
        </TableCell>
        <TableCell>
          {getSharingStatus() ? (
            <Badge variant={getBadgeVariant()}>{getSharingStatus()}</Badge>
          ) : (
            <div className="w-full text-center text-muted-foreground"></div>
          )}
        </TableCell>
        <TableCell>
          <DocumentStatusBadge
            status={material.status}
            processingError={material.processingError}
            onRetry={material.status === 'failed' ? handleRetry : undefined}
            isRetrying={isRetrying}
            processingTimings={material.processingTimings}
          />
        </TableCell>
        <TableCell>{formatDate(material.createdAt)}</TableCell>
        <TableCell>
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/materials/${material.id}`}>
                    <Eye />
                    View Material
                  </Link>
                </DropdownMenuItem>
                {material.uploadedBy === currentUser?.id && (
                  <DropdownMenuItem onClick={() => setIsEditFilenameOpen(true)}>
                    <Pencil />
                    Edit Filename
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={() => setIsAddToSpaceOpen(true)}>
                  <FolderClosed />
                  Manage Space
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {material.uploadedBy === currentUser?.id && (
                  <DropdownMenuItem onClick={onDelete} className="text-red-600 focus:text-red-600">
                    <Trash2 />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableCell>
      </TableRow>

      <AddToSpaceDialog
        open={isAddToSpaceOpen}
        onOpenChange={setIsAddToSpaceOpen}
        material={material}
      />

      <EditFilenameDialog
        open={isEditFilenameOpen}
        onOpenChange={setIsEditFilenameOpen}
        materialId={material.id}
        currentFileName={material.fileName}
      />
    </>
  );
}

type MaterialListProps = {
  materials: DocumentWithSpaces[];
};

export function MaterialList({ materials }: MaterialListProps) {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedSpaces, setSelectedSpaces] = useState<string[]>([]);
  const utils = clientApi.useUtils();

  const filteredAndSortedMaterials = useMemo(() => {
    return materials
      .filter((material) => material.fileName.toLowerCase().includes(searchTerm.toLowerCase()))
      .sort((a, b) => {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      });
  }, [materials, searchTerm, sortOrder]);

  // Check if any materials are still processing
  const hasProcessingDocuments = useMemo(() => {
    return materials.some(
      (material) =>
        material.status === 'uploaded' ||
        material.status === 'ocr_extracting' ||
        material.status === 'embedding' ||
        material.status === 'summarizing'
    );
  }, [materials]);

  // Auto-refresh materials list when there are processing documents
  useEffect(() => {
    if (!hasProcessingDocuments) return;

    const interval = setInterval(() => {
      utils.material.list.invalidate();
    }, 5000); // 5 seconds

    return () => clearInterval(interval);
  }, [hasProcessingDocuments, utils.material.list]);

  // Get unique spaces from all materials using space ID
  const allSpaces = Array.from(
    new Map(
      materials
        .flatMap((material) => material.documentSpaces.map((ds) => ds.space))
        .map((space) => [space.id, space])
    ).values()
  ).sort((a, b) => a.name.localeCompare(b.name));

  // Filter materials based on selected spaces
  const filteredMaterials = filteredAndSortedMaterials.filter((material) =>
    selectedSpaces.length === 0
      ? true
      : material.documentSpaces.some((ds) => selectedSpaces.includes(ds.space.id))
  );

  if (materials.length === 0) {
    return <div className="text-center text-muted-foreground">No materials uploaded yet</div>;
  }

  return (
    <div className="space-y-4">
      {hasProcessingDocuments && (
        <div className="rounded-md border border-blue-200 bg-blue-50 p-3">
          <div className="flex items-center gap-2 text-sm text-blue-800">
            <div className="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
            Auto-refreshing every 5 seconds -{' '}
            {
              materials.filter(
                (m) =>
                  m.status === 'uploaded' ||
                  m.status === 'ocr_extracting' ||
                  m.status === 'embedding' ||
                  m.status === 'summarizing'
              ).length
            }{' '}
            document(s) still processing
          </div>
        </div>
      )}
      <div className="flex items-center gap-4">
        <div className="flex flex-1 items-center gap-2">
          <Input
            type="text"
            placeholder="Search by filename"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-xs"
          />
          <Button
            variant="outline"
            size="icon"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <MultiSelect
            options={allSpaces.map((space) => ({
              label: space.name,
              value: space.id,
            }))}
            value={selectedSpaces}
            onValueChange={setSelectedSpaces}
            placeholder="All spaces"
            modalPopover={true}
          />
        </div>
      </div>

      {filteredMaterials.length === 0 ? (
        <div className="text-center text-muted-foreground">No materials found</div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Spaces</TableHead>
              <TableHead>Share</TableHead>
              <TableHead>Process Status</TableHead>
              <TableHead>Uploaded At</TableHead>
              <TableHead className="w-[70px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMaterials.map((material) => (
              <MaterialItem key={material.id} material={material} />
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
