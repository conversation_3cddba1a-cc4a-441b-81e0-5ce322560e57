'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useNestedModals } from '@/hooks/use-nested-modals';
import { clientApi } from '@/lib/trpc/client-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useEffect } from 'react';

const editFilenameSchema = z.object({
  fileName: z.string().min(1, 'Filename cannot be empty'),
});

type EditFilenameDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  materialId: string;
  currentFileName: string;
};

export function EditFilenameDialog({
  open,
  onOpenChange,
  materialId,
  currentFileName,
}: EditFilenameDialogProps) {
  const utils = clientApi.useUtils();
  const { handleInteractOutside } = useNestedModals();

  // Get file extension
  const getFileNameAndExt = (filename: string) => {
    const lastDotIndex = filename.lastIndexOf('.');
    if (lastDotIndex > 0) {
      return {
        name: filename.substring(0, lastDotIndex),
        ext: filename.substring(lastDotIndex),
      };
    }
    return { name: filename, ext: '' };
  };

  const { name: currentName, ext } = getFileNameAndExt(currentFileName);

  const form = useForm<z.infer<typeof editFilenameSchema>>({
    resolver: zodResolver(editFilenameSchema),
    defaultValues: {
      fileName: currentName,
    },
  });

  // Reset form values when dialog opens or currentFileName changes
  useEffect(() => {
    if (open) {
      const { name } = getFileNameAndExt(currentFileName);
      form.reset({
        fileName: name,
      });
    }
  }, [open, currentFileName, form]);

  const updateFilenameMutation = clientApi.material.updateFilename.useMutation({
    onSuccess: () => {
      toast.success('Filename updated successfully');
      utils.material.list.invalidate();
      onOpenChange(false);
      form.reset();
    },
    onError: (error) => {
      toast.error('Failed to update filename');
      console.error(error);
    },
  });

  const onSubmit = async (values: z.infer<typeof editFilenameSchema>) => {
    const newName = values.fileName.trim();
    if (newName === '') {
      return;
    }

    // Ensure we keep the original extension
    const finalName = newName + ext;

    await updateFilenameMutation.mutateAsync({
      id: materialId,
      fileName: finalName,
    });
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent onInteractOutside={handleInteractOutside}>
        <DialogHeader>
          <DialogTitle>Edit Filename</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fileName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Filename</FormLabel>
                  <FormControl>
                    <div className="space-y-1">
                      <div className="relative">
                        <Input
                          {...field}
                          className="pr-16"
                          onKeyDown={(e) => {
                            if (e.key === 'Escape') {
                              handleCancel();
                            }
                          }}
                        />
                        <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                          {ext}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Original filename: {currentFileName}
                      </p>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={updateFilenameMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateFilenameMutation.isPending}>
                {updateFilenameMutation.isPending ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
