'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  Database,
  Eye,
  FileText,
  Loader2,
  RefreshCw,
  Upload,
} from 'lucide-react';
import { useEffect, useState } from 'react';

type DocumentStatus =
  | 'uploaded'
  | 'processing'
  | 'ocr_extracting'
  | 'embedding'
  | 'summarizing'
  | 'completed'
  | 'failed';

interface DocumentStatusBadgeProps {
  status: DocumentStatus;
  processingError?: string | null;
  onRetry?: () => void;
  isRetrying?: boolean;
  className?: string;
  // JSON timing field for scalable processing stages
  processingTimings?: {
    [stage: string]: {
      startedAt: string;
      completedAt?: string;
    };
  } | null;
}

const statusConfig = {
  uploaded: {
    icon: Upload,
    label: 'Uploaded',
    color: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    description: 'File uploaded, waiting for processing to start',
  },
  processing: {
    icon: Clock,
    label: 'Processing',
    color: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    description: 'Document is being processed (legacy status)',
  },
  ocr_extracting: {
    icon: Eye,
    label: 'OCR Extracting',
    color: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
    description: 'Extracting text and layout information from document',
  },
  embedding: {
    icon: Database,
    label: 'Embedding',
    color: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
    description: 'Creating vector embeddings for semantic search',
  },
  summarizing: {
    icon: FileText,
    label: 'Summarizing',
    color: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',
    description: 'Generating document summary and analysis',
  },
  completed: {
    icon: CheckCircle,
    label: 'Completed',
    color: 'bg-green-100 text-green-800 hover:bg-green-200',
    description: 'Document processed successfully and ready for use',
  },
  failed: {
    icon: AlertCircle,
    label: 'Failed',
    color: 'bg-red-100 text-red-800 hover:bg-red-200',
    description: 'Processing failed - click retry button to try again',
  },
} as const;

export function DocumentStatusBadge({
  status,
  processingError,
  onRetry,
  isRetrying = false,
  className,
  processingTimings,
}: DocumentStatusBadgeProps) {
  const config = statusConfig[status] || statusConfig['uploaded']; // Fallback to 'uploaded' if status not found
  const Icon = config.icon;

  // State to force re-renders for live timing updates
  const [, setTick] = useState(0);

  // Update timer every second for active processing stages
  useEffect(() => {
    const isActiveProcessing = [
      'processing',
      'ocr_extracting',
      'embedding',
      'summarizing',
    ].includes(status);

    if (!isActiveProcessing || !processingTimings) return;

    // Check if current stage is actually running
    const stageKey = status === 'processing' ? 'processing' : status;
    const currentStage = processingTimings[stageKey];
    const isCurrentlyRunning = currentStage?.startedAt && !currentStage.completedAt;

    if (!isCurrentlyRunning) return;

    const interval = setInterval(() => {
      setTick((tick) => tick + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [status, processingTimings]);

  // Helper function to calculate elapsed time
  const getElapsedTime = (startTime: string, endTime?: string): string => {
    const start = new Date(startTime).getTime();
    const end = endTime ? new Date(endTime).getTime() : Date.now();
    const diff = end - start;

    if (diff < 1000) return '< 1s';
    if (diff < 60000) return `${Math.round(diff / 1000)}s`;
    if (diff < 3600000) return `${Math.round(diff / 60000)}m`;
    return `${Math.round(diff / 3600000)}h`;
  };

  // Get timing info from JSON structure
  const getTimingInfo = () => {
    const timings: { label: string; duration: string }[] = [];

    if (!processingTimings) return timings;

    // Define stage labels for display
    const stageLabels: { [key: string]: string } = {
      ocr_extracting: 'OCR Extraction',
      embedding: 'Embedding',
      summarizing: 'Summarizing',
    };

    // Process each stage in order
    Object.entries(processingTimings).forEach(([stage, timing]) => {
      if (timing.startedAt) {
        const label = stageLabels[stage] || stage.charAt(0).toUpperCase() + stage.slice(1);
        const duration = getElapsedTime(timing.startedAt, timing.completedAt);
        const isRunning = !timing.completedAt;

        timings.push({
          label,
          duration: isRunning ? `${duration} (running)` : duration,
        });
      }
    });

    return timings;
  };

  const timings = getTimingInfo();

  // Get current running time for active processing stages
  const getCurrentRunningTime = () => {
    if (
      !processingTimings ||
      !['processing', 'ocr_extracting', 'embedding', 'summarizing'].includes(status)
    ) {
      return null;
    }

    // Map status to stage key
    const stageKey = status === 'processing' ? 'processing' : status;
    const currentStage = processingTimings[stageKey];

    if (currentStage?.startedAt && !currentStage.completedAt) {
      return getElapsedTime(currentStage.startedAt);
    }

    return null;
  };

  const currentRunningTime = getCurrentRunningTime();

  const badge = (
    <Badge
      variant="secondary"
      className={cn(
        'flex items-center gap-1 px-2 py-1 text-xs font-medium',
        config.color,
        className
      )}
    >
      {['processing', 'ocr_extracting', 'embedding', 'summarizing'].includes(status) ? (
        <Loader2 className="h-3 w-3 animate-spin" />
      ) : (
        <Icon className="h-3 w-3" />
      )}
      <span>{config.label}</span>
      {currentRunningTime && (
        <span className="ml-1 font-mono text-xs opacity-75">({currentRunningTime})</span>
      )}
    </Badge>
  );

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2">
        <Tooltip>
          <TooltipTrigger asChild>{badge}</TooltipTrigger>
          <TooltipContent side="bottom" className="max-w-xs">
            <div className="space-y-1">
              <p className="font-medium">{config.label}</p>
              <p className="text-xs text-muted-foreground">{config.description}</p>

              {timings.length > 0 && (
                <div className="mt-2 space-y-1">
                  <p className="text-xs font-medium text-foreground">Processing Time:</p>
                  {timings.map((timing, index) => (
                    <div key={index} className="flex justify-between text-xs">
                      <span className="text-muted-foreground">{timing.label}:</span>
                      <span className="font-mono">{timing.duration}</span>
                    </div>
                  ))}
                </div>
              )}

              {processingError && status === 'failed' && (
                <p className="mt-2 text-xs text-red-600">Error: {processingError}</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>

        {status === 'failed' && onRetry && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                onClick={onRetry}
                disabled={isRetrying}
                className="h-6 w-6 p-0 hover:bg-red-50"
              >
                {isRetrying ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <RefreshCw className="h-3 w-3" />
                )}
                <span className="sr-only">Retry processing</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Retry processing</p>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}
