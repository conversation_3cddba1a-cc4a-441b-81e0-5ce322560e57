'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { useDocumentsStatus } from '@/hooks/use-document-status';
import { formatDistanceToNow } from 'date-fns';
import { AlertCircle, CheckCircle, Clock, FileText, RefreshCw, Upload } from 'lucide-react';
import { DocumentStatusBadge } from './document-status-badge';

export function ProcessingStatusDashboard() {
  const { documents, counts, isLoading, error, refresh } = useDocumentsStatus();

  if (error) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <AlertCircle className="mb-4 h-12 w-12 text-red-500" />
          <p className="mb-4 text-center text-muted-foreground">
            Failed to load document status: {error}
          </p>
          <Button onClick={refresh} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const processingProgress =
    counts.total > 0 ? Math.round((counts.completed / counts.total) * 100) : 0;

  const hasProcessingDocuments = counts.processing > 0 || counts.uploaded > 0;

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-4">
            <Upload className="mr-3 h-8 w-8 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Uploaded</p>
              <p className="text-2xl font-bold">{counts.uploaded}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-4">
            <Clock className="mr-3 h-8 w-8 text-yellow-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Processing</p>
              <p className="text-2xl font-bold">{counts.processing}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-4">
            <CheckCircle className="mr-3 h-8 w-8 text-green-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Completed</p>
              <p className="text-2xl font-bold">{counts.completed}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center p-4">
            <AlertCircle className="mr-3 h-8 w-8 text-red-500" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Failed</p>
              <p className="text-2xl font-bold">{counts.failed}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      {counts.total > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Overall Progress</CardTitle>
            <CardDescription>
              {counts.completed} of {counts.total} documents processed successfully
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={processingProgress} className="w-full" />
            <div className="mt-2 flex justify-between text-sm text-muted-foreground">
              <span>{processingProgress}% complete</span>
              <span>{counts.total - counts.completed} remaining</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document List */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>Document Processing Status</CardTitle>
            <CardDescription>
              Monitor the processing status of your uploaded documents
            </CardDescription>
          </div>
          <Button onClick={refresh} variant="outline" size="sm" disabled={isLoading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          {documents.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8">
              <FileText className="mb-4 h-12 w-12 text-muted-foreground" />
              <p className="text-center text-muted-foreground">
                No documents found. Upload some files to get started!
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {documents.map((doc, index) => (
                <div key={doc.documentId}>
                  <div className="flex items-center justify-between rounded-lg border p-3">
                    <div className="flex min-w-0 flex-1 items-center space-x-3">
                      <FileText className="h-5 w-5 flex-shrink-0 text-muted-foreground" />
                      <div className="min-w-0 flex-1">
                        <p className="truncate text-sm font-medium">{doc.fileName}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(doc.createdAt), { addSuffix: true })}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-shrink-0 items-center space-x-2">
                      <DocumentStatusBadge
                        status={doc.status}
                        processingError={doc.processingError}
                        onRetry={
                          doc.status === 'failed'
                            ? () => {
                                // Retry logic would be handled by the hook
                                refresh();
                              }
                            : undefined
                        }
                      />
                    </div>
                  </div>
                  {index < documents.length - 1 && <Separator className="my-2" />}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Auto-refresh notice */}
      {hasProcessingDocuments && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="flex items-center p-4">
            <Clock className="mr-3 h-5 w-5 text-yellow-600" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium">Auto-refreshing status</p>
              <p className="text-yellow-700">
                Processing documents will update automatically. Large documents may take several
                minutes to complete.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
