import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { MultiSelect } from '@/components/ui/multi-select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import type { DocumentWithSpaces, SpaceWithCount } from '@/database/repository/space';
import { useNestedModals } from '@/hooks/use-nested-modals';
import { clientApi } from '@/lib/trpc/client-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { FileText, Loader2, Trash2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useState } from 'react';

// Form schema for adding to spaces
const addToSpaceSchema = z.object({
  spaceIds: z.array(z.string()),
});

type AddToSpaceFormValues = z.infer<typeof addToSpaceSchema>;

type AddToSpaceDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  material: DocumentWithSpaces;
};

interface SpaceInfo {
  id: string;
  name: string;
}

export function AddToSpaceDialog({ open, onOpenChange, material }: AddToSpaceDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRemoving, setIsRemoving] = useState<Record<string, boolean>>({});
  const utils = clientApi.useUtils();
  const { handleInteractOutside } = useNestedModals();

  // Get all spaces for the dropdown
  const { data: allSpaces = [], isLoading: isLoadingSpaces } = clientApi.space.list.useQuery(
    undefined,
    {
      enabled: open,
    }
  );

  // Get current spaces this material belongs to
  const currentSpaces: SpaceInfo[] = material.documentSpaces.map((ds) => ({
    id: ds.space.id,
    name: ds.space.name,
  }));

  // Filter out spaces that material is already in
  const availableSpaces = allSpaces.filter(
    (space: SpaceWithCount) => !currentSpaces.some((cs: SpaceInfo) => cs.id === space.id)
  );

  const addDocumentsMutation = clientApi.space.addDocuments.useMutation({
    onSuccess: () => {
      utils.material.list.invalidate();
    },
    onError: (error) => {
      console.error('Error adding material to space:', error);
      toast.error('Failed to add material to space');
    },
  });

  const removeDocumentMutation = clientApi.space.removeDocument.useMutation({
    onSuccess: () => {
      utils.material.list.invalidate();
    },
    onError: (error) => {
      console.error('Error removing material from space:', error);
      toast.error('Failed to remove material from space');
    },
  });

  // Setup form with validation
  const form = useForm<AddToSpaceFormValues>({
    resolver: zodResolver(addToSpaceSchema),
    defaultValues: {
      spaceIds: [],
    },
  });

  // Handle form submission to add to spaces
  const onSubmit = async (values: AddToSpaceFormValues) => {
    if (values.spaceIds.length === 0) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Process each space one at a time
      for (const spaceId of values.spaceIds) {
        await addDocumentsMutation.mutateAsync({
          documentIds: [material.id],
          spaceId,
        });
      }

      toast.success(`Added "${material.fileName}" to ${values.spaceIds.length} space(s)`);

      // Reset form but keep dialog open for further management
      form.reset();
    } catch {
      // Error is already handled in the mutation onError callback
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle removing material from a space
  const handleRemoveFromSpace = async (spaceId: string, spaceName: string) => {
    setIsRemoving((prev) => ({ ...prev, [spaceId]: true }));

    try {
      await removeDocumentMutation.mutateAsync({
        documentId: material.id,
        spaceId,
      });

      toast.success(`Removed "${material.fileName}" from "${spaceName}"`);
    } catch {
      // Error is already handled in the mutation onError callback
    } finally {
      setIsRemoving((prev) => ({ ...prev, [spaceId]: false }));
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl" onInteractOutside={handleInteractOutside}>
        <DialogHeader>
          <DialogTitle className="text-xl">Manage Spaces</DialogTitle>
        </DialogHeader>

        <div className="mb-4 flex items-center space-x-3 rounded-md border bg-muted/40 p-3">
          <FileText className="h-10 w-10 flex-shrink-0 text-primary" />
          <div className="flex-1 space-y-1">
            <p className="text-sm font-medium leading-none">{material.fileName}</p>
            <p className="text-xs text-muted-foreground">
              {"You can add this material to spaces or remove it from spaces it's currently in"}
            </p>
          </div>
        </div>

        <Separator className="my-2" />

        <ScrollArea className="max-h-[60vh]">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* Current Spaces */}
            <Card className="border-muted bg-card/50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Current Spaces</CardTitle>
                <CardDescription>
                  Spaces this material belongs to ({currentSpaces.length})
                </CardDescription>
              </CardHeader>
              <CardContent>
                {currentSpaces.length === 0 ? (
                  <div className="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
                    <p className="text-sm text-muted-foreground">
                      This material is not in any spaces yet
                    </p>
                  </div>
                ) : (
                  <ul className="space-y-2">
                    {currentSpaces.map((space) => (
                      <li
                        key={space.id}
                        className="flex items-center justify-between rounded-md border border-muted bg-background p-3 transition-colors hover:bg-accent/10"
                      >
                        <span className="font-medium">{space.name}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveFromSpace(space.id, space.name)}
                          disabled={isRemoving[space.id]}
                          className="h-8 px-2 text-destructive hover:bg-destructive/10 hover:text-destructive"
                        >
                          {isRemoving[space.id] ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                          <span className="ml-1.5">Remove</span>
                        </Button>
                      </li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>

            {/* Add to Spaces Form */}
            <Card className="border-muted bg-card/50">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium">Add to Spaces</CardTitle>
                <CardDescription>
                  {availableSpaces.length === 0
                    ? 'No more spaces available'
                    : 'Select spaces to add this material to'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {availableSpaces.length === 0 ? (
                  <div className="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
                    <p className="text-sm text-muted-foreground">
                      This material is already in all available spaces
                    </p>
                  </div>
                ) : (
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                      <FormField
                        control={form.control}
                        name="spaceIds"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Add to Spaces</FormLabel>
                            <FormControl>
                              <MultiSelect
                                options={availableSpaces.map((space: SpaceWithCount) => ({
                                  label: space.name,
                                  value: space.id,
                                }))}
                                value={field.value}
                                onValueChange={field.onChange}
                                placeholder={
                                  isLoadingSpaces
                                    ? 'Loading spaces...'
                                    : availableSpaces.length === 0
                                      ? 'All spaces already added'
                                      : 'Select spaces to add...'
                                }
                                disabled={
                                  isSubmitting || isLoadingSpaces || availableSpaces.length === 0
                                }
                                modalPopover={true}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="submit"
                        disabled={isSubmitting || form.watch('spaceIds').length === 0}
                        className="w-full"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Adding...
                          </>
                        ) : (
                          'Add to Selected Spaces'
                        )}
                      </Button>
                    </form>
                  </Form>
                )}
              </CardContent>
            </Card>
          </div>
        </ScrollArea>

        <div className="flex justify-end pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting || Object.values(isRemoving).some((val) => val)}
          >
            Done
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
