import { db } from '@/database/drizzle';
import { SpaceRepository } from '@/database/repository/space';
import { spaceInvitations, spaces } from '@/database/schema/index';
import { TRPCError } from '@trpc/server';
import { and, eq, isNull } from 'drizzle-orm';
import { z } from 'zod';
import { protectedProcedure, router } from '../';

const createSpaceSchema = z.object({
  name: z.string(),
  description: z.string().nullable(),
  materialIds: z.array(z.string()).optional(),
});

const updateSpaceSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
});

const documentSpaceSchema = z.object({
  documentId: z.string(),
  spaceId: z.string(),
});

// Schema for toggling space sharing
const toggleSharingSchema = z.object({
  id: z.string(),
  isShared: z.boolean(),
});

// Schema for managing space members
const memberSchema = z.object({
  spaceId: z.string(),
  userId: z.string(),
  role: z.enum(['owner', 'editor', 'viewer']),
});

// Schema for creating invitations
const createInvitationSchema = z.object({
  spaceId: z.string(),
});

// Schema for joining a space
const joinSpaceSchema = z.object({
  code: z.string(),
});

// Schema for validating invitation token
const invitationTokenSchema = z.object({
  token: z.string(),
});

// Schema for accepting invitation
const acceptInvitationSchema = z.object({
  token: z.string(),
});

export const spaceRouter = router({
  list: protectedProcedure.query(async ({ ctx }) => {
    return SpaceRepository.getSpacesByUserId(ctx.user.id);
  }),

  create: protectedProcedure.input(createSpaceSchema).mutation(async ({ ctx, input }) => {
    const space = await SpaceRepository.createSpace(input.name, input.description, ctx.user.id);

    if (input.materialIds && input.materialIds.length > 0) {
      await SpaceRepository.addDocumentsToSpace(input.materialIds, [space.id]);
    }

    return space;
  }),

  update: protectedProcedure.input(updateSpaceSchema).mutation(async ({ ctx, input }) => {
    // Check ownership
    const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
    const space = spaces.find((s) => s.id === input.id);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to update this space',
      });
    }

    return SpaceRepository.updateSpace(input.id, input.name, input.description);
  }),

  delete: protectedProcedure.input(z.string()).mutation(async ({ ctx, input }) => {
    // Check ownership
    const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
    const space = spaces.find((s) => s.id === input);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to delete this space',
      });
    }

    return SpaceRepository.deleteSpace(input);
  }),

  addDocuments: protectedProcedure
    .input(z.object({ documentIds: z.array(z.string()), spaceId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check ownership
      const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
      const space = spaces.find((s) => s.id === input.spaceId);

      if (!space) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to add documents to this space',
        });
      }

      return SpaceRepository.addDocumentsToSpace(input.documentIds, [input.spaceId]);
    }),

  removeDocument: protectedProcedure.input(documentSpaceSchema).mutation(async ({ ctx, input }) => {
    // Check ownership
    const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
    const space = spaces.find((s) => s.id === input.spaceId);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to remove documents from this space',
      });
    }

    return SpaceRepository.removeDocumentFromSpace(input.documentId, input.spaceId);
  }),

  getDocuments: protectedProcedure.input(z.string()).query(async ({ ctx, input }) => {
    // Check access to this space (either as owner or member)
    const allSpaces = await SpaceRepository.getAllSpacesForUser(ctx.user.id);
    const space = allSpaces.find((s) => s.id === input);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have access to this space',
      });
    }

    // Additional sharing check - if user is not the owner, space must be shared
    if (space.ownerId !== ctx.user.id && !space.isShared) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'This space is no longer shared',
      });
    }

    return SpaceRepository.getSpaceDocuments(input);
  }),

  // New procedures for space sharing functionality
  toggleSharing: protectedProcedure.input(toggleSharingSchema).mutation(async ({ ctx, input }) => {
    // Check ownership (only owner can toggle sharing)
    const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
    const space = spaces.find((s) => s.id === input.id);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to change sharing settings for this space',
      });
    }

    return SpaceRepository.toggleSharing(input.id, input.isShared);
  }),

  // Get all available spaces (owned + shared with user)
  listAll: protectedProcedure.query(async ({ ctx }) => {
    // First get all spaces (owned + member)
    const allSpaces = await SpaceRepository.getAllSpacesForUser(ctx.user.id);

    // Filter spaces to respect sharing status: only return spaces that are either owned by user or are currently shared
    return allSpaces.filter(
      (space) =>
        // Include if:
        // 1. User is the owner (regardless of sharing status)
        // 2. Space has sharing enabled (for spaces where user is a member)
        space.ownerId === ctx.user.id || space.isShared
    );
  }),

  // Add member to space
  addMember: protectedProcedure.input(memberSchema).mutation(async ({ ctx, input }) => {
    // Check ownership (only owner can add members)
    const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
    const space = spaces.find((s) => s.id === input.spaceId);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to add members to this space',
      });
    }

    return SpaceRepository.addMember(input.spaceId, input.userId, input.role);
  }),

  // Remove member from space
  removeMember: protectedProcedure
    .input(z.object({ spaceId: z.string(), userId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check ownership (only owner can remove members)
      const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
      const space = spaces.find((s) => s.id === input.spaceId);

      if (!space) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to remove members from this space',
        });
      }

      return SpaceRepository.removeMember(input.spaceId, input.userId);
    }),

  // Get members of a space
  getMembers: protectedProcedure.input(z.string()).query(async ({ ctx, input }) => {
    // Check access to this space (either as owner or member)
    const allSpaces = await SpaceRepository.getAllSpacesForUser(ctx.user.id);
    const space = allSpaces.find((s) => s.id === input);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have access to this space',
      });
    }

    // Additional sharing check - if user is not the owner, space must be shared
    if (space.ownerId !== ctx.user.id && !space.isShared) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'This space is no longer shared',
      });
    }

    return SpaceRepository.getMembers(input);
  }),

  // Create an invitation
  createInvitation: protectedProcedure
    .input(createInvitationSchema)
    .mutation(async ({ ctx, input }) => {
      // Check ownership (only owner can create invitations)
      const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
      const space = spaces.find((s) => s.id === input.spaceId);

      if (!space) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You do not have permission to create invitations for this space',
        });
      }

      // Check if space is shared
      if (!space.isShared) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'This space is not currently shared',
        });
      }

      return SpaceRepository.createInvitation(input.spaceId, ctx.user.id);
    }),

  // Get invitations for a space
  getInvitations: protectedProcedure.input(z.string()).query(async ({ ctx, input }) => {
    // Check ownership (only owner can view invitations)
    const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
    const space = spaces.find((s) => s.id === input);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to view invitations for this space',
      });
    }

    return SpaceRepository.getInvitations(input);
  }),

  // Delete an invitation
  deleteInvitation: protectedProcedure.input(z.number()).mutation(async ({ ctx, input }) => {
    // Get invitation info to check ownership
    const allInvitations = await db
      .select({
        id: spaceInvitations.id,
        spaceId: spaceInvitations.spaceId,
      })
      .from(spaceInvitations)
      .where(eq(spaceInvitations.id, input))
      .limit(1);

    if (allInvitations.length === 0) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Invitation not found',
      });
    }

    const invitation = allInvitations[0];

    // Check ownership of the space
    const spaces = await SpaceRepository.getSpacesByUserId(ctx.user.id);
    const space = spaces.find((s) => s.id === invitation.spaceId);

    if (!space) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to delete invitations for this space',
      });
    }

    return SpaceRepository.deleteInvitation(input);
  }),

  // Join a space with invitation code
  joinSpace: protectedProcedure.input(joinSpaceSchema).mutation(async ({ ctx, input }) => {
    return SpaceRepository.joinSpaceWithCode(input.code, ctx.user.id);
  }),

  // Validate an invitation token before joining
  validateInvitationToken: protectedProcedure
    .input(invitationTokenSchema)
    .query(async ({ input }) => {
      // Find the invitation
      const [invitation] = await db
        .select({
          code: spaceInvitations.code,
          spaceId: spaceInvitations.spaceId,
          useCount: spaceInvitations.useCount,
        })
        .from(spaceInvitations)
        .where(eq(spaceInvitations.code, input.token))
        .limit(1);

      if (!invitation) {
        return { valid: false, spaceName: '' };
      }

      // Check if space exists and is shared
      const [space] = await db
        .select()
        .from(spaces)
        .where(and(eq(spaces.id, invitation.spaceId), isNull(spaces.deletedAt)))
        .limit(1);

      if (!space || !space.isShared) {
        return { valid: false, spaceName: '' };
      }

      return { valid: true, spaceName: space.name };
    }),

  // Accept an invitation and join a space
  acceptInvitation: protectedProcedure
    .input(acceptInvitationSchema)
    .mutation(async ({ ctx, input }) => {
      const result = await SpaceRepository.joinSpaceWithCode(input.token, ctx.user.id);

      if (!result.success) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: result.message,
        });
      }

      return result;
    }),
});
