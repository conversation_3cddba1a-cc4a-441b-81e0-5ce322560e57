import { UserRepository } from '@/database/repository/user';
import { getUser, lucia, verifyPassword } from '@/lib/auth';
import { EmailService } from '@/lib/email';
import { publicProcedure, router } from '@/server/trpc';
import { SignInSchema, SignUpSchema } from '@/types/user';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';
import { cookies } from 'next/headers';

export const authRouter = router({
  getUser: publicProcedure.query(async () => {
    const { user, session } = await getUser();

    return { user, session };
  }),
  signIn: publicProcedure.input(SignInSchema).mutation(async ({ input, ctx }) => {
    if (ctx.user) {
      throw new TRPCError({ code: 'UNAUTHORIZED', message: 'User already signed in' });
    }

    const { email, password } = input;
    try {
      const user = await UserRepository.getUserByEmail(email);

      if (!user) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'User not found' });
      }

      const validPassword = await verifyPassword(user.password, password);
      if (!validPassword) {
        throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Invalid credentials' });
      }

      const session = await lucia.createSession(user.id, {});
      const sessionCookie = lucia.createSessionCookie(session.id);

      cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

      return { success: true };
    } catch (error: unknown) {
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error('[signIn] INTERNAL_SERVER_ERROR', error);
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'An error occurred' });
    }
  }),
  signUp: publicProcedure.input(SignUpSchema).mutation(async ({ input, ctx }) => {
    if (ctx.user) {
      throw new TRPCError({ code: 'UNAUTHORIZED', message: 'User already signed in' });
    }

    const { name, email, password } = input;

    try {
      const user = await UserRepository.createUser(name, email, password);

      const session = await lucia.createSession(user.id, {});
      const sessionCookie = lucia.createSessionCookie(session.id);

      cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);

      // Generate verification token
      const verificationToken = await UserRepository.createVerificationToken(user.id);

      // Send verification email
      await EmailService.sendVerificationEmail(user.email, user.name, verificationToken);

      return { success: true };
    } catch (error: unknown) {
      if (error instanceof Error && error.message === 'User already exists') {
        throw new TRPCError({ code: 'CONFLICT', message: 'User with this email already exists' });
      }

      if (error instanceof TRPCError) {
        throw error;
      }

      console.error('[signUp] INTERNAL_SERVER_ERROR', error);
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'An error occurred' });
    }
  }),
  verifyEmail: publicProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      try {
        const verified = await UserRepository.verifyEmail(input.token);

        if (!verified) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid or expired verification token',
          });
        }

        return {
          success: true,
          message: 'Email verified successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('[verifyEmail] INTERNAL_SERVER_ERROR', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An error occurred during email verification',
        });
      }
    }),
  resendVerification: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      try {
        // Find the user
        const user = await UserRepository.getUserByEmail(input.email);

        if (!user) {
          // For security, we don't want to reveal if an email exists or not
          return {
            success: true,
            message: 'If your email is registered, a verification link has been sent.',
          };
        }

        // Check if already verified
        if (user.emailVerified) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'This email is already verified. Please sign in.',
          });
        }

        // Generate a new verification token
        const verificationToken = await UserRepository.createVerificationToken(user.id);

        // Send verification email
        await EmailService.sendVerificationEmail(user.email, user.name, verificationToken);

        return {
          success: true,
          message: 'Verification email sent successfully. Please check your inbox.',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('[resendVerification] INTERNAL_SERVER_ERROR', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An error occurred while sending the verification email',
        });
      }
    }),
  forgotPassword: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      try {
        const token = await UserRepository.createPasswordResetToken(input.email);

        // Even if the email doesn't exist, we return success to prevent email enumeration
        if (token) {
          await EmailService.sendPasswordResetEmail(input.email, token);
        }

        return {
          success: true,
          message: 'If an account with that email exists, a password reset link has been sent',
        };
      } catch (error) {
        console.error('[forgotPassword] INTERNAL_SERVER_ERROR', error);

        // Still return success even on error to prevent email enumeration
        return {
          success: true,
          message: 'If an account with that email exists, a password reset link has been sent',
        };
      }
    }),
  resetPassword: publicProcedure
    .input(z.object({ token: z.string(), password: z.string().min(8) }))
    .mutation(async ({ input }) => {
      try {
        const success = await UserRepository.resetPassword(input.token, input.password);

        if (!success) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid or expired reset token',
          });
        }

        return {
          success: true,
          message: 'Password has been reset successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('[resetPassword] INTERNAL_SERVER_ERROR', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An error occurred while resetting your password',
        });
      }
    }),
  validateResetToken: publicProcedure
    .input(z.object({ token: z.string() }))
    .query(async ({ input }) => {
      try {
        const isValid = await UserRepository.validateResetToken(input.token);

        return {
          valid: isValid,
        };
      } catch (error) {
        console.error('[validateResetToken] INTERNAL_SERVER_ERROR', error);
        return { valid: false };
      }
    }),
});
