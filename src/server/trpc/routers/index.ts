import { router } from '@/server/trpc';
import { authRouter } from '@/server/trpc/routers/auth';
import { chatRouter } from '@/server/trpc/routers/chat';
import { materialRouter } from '@/server/trpc/routers/material';
import { spaceRouter } from '@/server/trpc/routers/space';

const appRouter = router({
  auth: authRouter,
  space: spaceRouter,
  material: materialRouter,
  chat: chatRouter,
});

type AppRouter = typeof appRouter;

export { appRouter };
export type { AppRouter };
