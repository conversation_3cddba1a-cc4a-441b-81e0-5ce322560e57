import { db } from '@/database/drizzle';
import { interactions, topics } from '@/database/schema/index';
import { TRPCError } from '@trpc/server';
import { eq } from 'drizzle-orm';
import { z } from 'zod';
import { protectedProcedure, router } from '../';

export const chatRouter = router({
  delete: protectedProcedure.input(z.string()).mutation(async ({ ctx, input: topicId }) => {
    // Check ownership - make sure the topic belongs to the user
    const topic = await db.query.topics.findFirst({
      where: eq(topics.id, topicId),
      columns: {
        id: true,
        userId: true,
      },
    });

    if (!topic) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Topic not found',
      });
    }

    if (topic.userId !== ctx.user.id) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You do not have permission to delete this topic',
      });
    }

    // Delete all interactions associated with this topic first
    await db.delete(interactions).where(eq(interactions.topicId, topicId));

    // Then delete the topic
    await db.delete(topics).where(eq(topics.id, topicId));

    return { success: true };
  }),
});
