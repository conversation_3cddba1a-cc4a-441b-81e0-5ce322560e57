import { ForgotPasswordForm } from '@/components/auth/forgot-password-form';
import { SITE_PATHS } from '@/configs/site';
import { getUser } from '@/lib/auth';
import Link from 'next/link';
import { redirect } from 'next/navigation';

export default async function ForgotPasswordPage() {
  const { user } = await getUser();

  if (user) {
    redirect(SITE_PATHS.HOME);
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="mb-8 flex flex-col items-center justify-center text-center">
        <h1 className="mb-2 text-3xl font-bold">Forgot Password</h1>
        <p className="text-muted-foreground">Enter your email to reset your password</p>
      </div>
      <div className="w-full max-w-md">
        <ForgotPasswordForm />
        <div className="mt-6 text-center">
          <p className="text-sm text-muted-foreground">
            Remember your password?{' '}
            <Link
              href={SITE_PATHS.AUTH.SIGN_IN}
              className="font-medium underline underline-offset-4"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
