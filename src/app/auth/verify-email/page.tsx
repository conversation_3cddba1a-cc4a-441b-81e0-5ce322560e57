import { logOut } from '@/actions/auth';
import { EmailVerificationSuccess } from '@/components/auth/email-verification-success';
import { ResendVerificationEmail } from '@/components/auth/resend-verification-email';
import { Button } from '@/components/ui/button';
import { SITE_PATHS } from '@/configs/site';
import { UserRepository } from '@/database/repository/user';
import { getUser } from '@/lib/auth';
import { AlertTriangle, Loader2, Mail } from 'lucide-react';
import Link from 'next/link';
import { redirect } from 'next/navigation';

export default async function VerifyEmailPage({
  searchParams,
}: {
  searchParams: { email?: string; token?: string; error?: string };
}) {
  const { user } = await getUser();
  console.log('user', user);

  // If the user is authenticated and their email is already verified, redirect to home
  if (user && user.emailVerified) {
    return redirect(SITE_PATHS.HOME);
  }

  if (!user) {
    return redirect(SITE_PATHS.AUTH.SIGN_IN);
  }

  const email = user.email;
  const token = searchParams.token;
  const errorMessage = searchParams.error;
  console.log('email', email);
  console.log('token', token);

  // If there's a token, verify it automatically
  if (token && !errorMessage) {
    try {
      const verified = await UserRepository.verifyEmail(token);

      if (verified) {
        // If verification successful, render the client-side success component
        // This will update the user store on the client side
        return <EmailVerificationSuccess email={email} />;
      } else {
        // If verification failed, show error
        redirect(`${SITE_PATHS.AUTH.VERIFY_EMAIL}?error=invalid_token}`);
      }
    } catch {
      // If verification fails, redirect to the verification page with an error
      redirect(`/auth/verify-email?error=invalid_token`);
    }

    // While verifying, show loading state
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="mb-8 flex flex-col items-center justify-center text-center">
          <h1 className="mb-2 text-3xl font-bold">Verifying Email</h1>
          <p className="text-muted-foreground">Please wait while we verify your email address</p>
        </div>
        <div className="flex flex-col items-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="mt-4">Verifying...</p>
        </div>
      </div>
    );
  }

  // If there's an error, show error state
  if (errorMessage) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="mb-8 flex flex-col items-center justify-center text-center">
          <h1 className="mb-2 text-3xl font-bold">Verification Failed</h1>
          <p className="text-muted-foreground">We couldn&apos;t verify your email address</p>
        </div>
        <div className="w-full max-w-md">
          <div className="rounded-lg border border-red-200 p-6 shadow-sm">
            <div className="flex flex-col items-center py-4">
              <div className="mb-4 rounded-full bg-red-100 p-3">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="mb-2 text-xl font-medium">Verification Error</h3>
              <p className="mb-4 text-center text-muted-foreground">
                {errorMessage === 'invalid_token'
                  ? 'The verification link has expired or is invalid.'
                  : 'There was a problem verifying your email address.'}
              </p>
              <div className="mt-2">
                <ResendVerificationEmail email={email} />
              </div>
              <div className="mt-6">
                <Link
                  href={SITE_PATHS.AUTH.SIGN_IN}
                  className="text-sm text-blue-600 hover:underline"
                >
                  Return to Sign In
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  console.log('show page');
  // If there's no token, show an instruction page
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="mb-8 flex flex-col items-center justify-center text-center">
        <div className="mb-4 rounded-full bg-blue-100 p-4">
          <Mail className="h-12 w-12 text-blue-600" />
        </div>
        <h1 className="mb-2 text-3xl font-bold">Verify Your Email</h1>
        <p className="max-w-md text-muted-foreground">
          {!user ? (
            <>
              We&apos;ve sent a verification link to{' '}
              {email ? <strong>{email}</strong> : 'your email address'}. Please check your inbox and
              click the link to verify your account.
            </>
          ) : (
            <>
              Your email address <strong>{email}</strong> is not verified yet. Please check your
              inbox for a verification link or request a new one below.
            </>
          )}
        </p>
      </div>

      <ResendVerificationEmail email={email} />

      <div className="mt-6 text-center">
        <p className="text-sm text-muted-foreground">
          {!user ? (
            <>
              Already have an account?{' '}
              <Link
                href={SITE_PATHS.AUTH.SIGN_IN}
                className="font-medium underline underline-offset-4"
              >
                Sign in
              </Link>
            </>
          ) : (
            <div className="flex items-center gap-1">
              Want to use a different account?{' '}
              <form action={logOut} className="inline">
                <Button type="submit" variant="link" className="flex items-center gap-2">
                  Sign out
                </Button>
              </form>
            </div>
          )}
        </p>
      </div>
    </div>
  );
}
