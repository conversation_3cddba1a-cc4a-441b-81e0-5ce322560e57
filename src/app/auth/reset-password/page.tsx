import { ResetPasswordForm } from '@/components/auth/reset-password-form';
import { SITE_PATHS } from '@/configs/site';
import { getUser } from '@/lib/auth';
import { serverApi } from '@/lib/trpc/server-api';
import { AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { redirect } from 'next/navigation';

export default async function ResetPasswordPage({
  searchParams,
}: {
  searchParams: { token?: string };
}) {
  const { user } = await getUser();

  if (user) {
    redirect(SITE_PATHS.HOME);
  }

  const token = searchParams.token;

  if (!token) {
    redirect(SITE_PATHS.AUTH.FORGOT_PASSWORD);
  }

  // Validate the token before showing the reset password form using tRPC
  const validation = await serverApi.auth.validateResetToken.query({ token });

  // If token is invalid, show error message
  if (!validation.valid) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="mb-8 flex flex-col items-center justify-center text-center">
          <h1 className="mb-2 text-3xl font-bold">Invalid or Expired Link</h1>
          <p className="text-muted-foreground">The password reset link is invalid or has expired</p>
        </div>
        <div className="w-full max-w-md">
          <div className="rounded-lg border border-red-200 p-6 shadow-sm">
            <div className="flex flex-col items-center py-4">
              <div className="mb-4 rounded-full bg-red-100 p-3">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="mb-2 text-xl font-medium">Reset Link Error</h3>
              <p className="mb-4 text-center text-muted-foreground">
                This password reset link is no longer valid. It may have expired or already been
                used.
              </p>
              <div className="mt-4">
                <Link
                  href={SITE_PATHS.AUTH.FORGOT_PASSWORD}
                  className="text-blue-600 hover:underline"
                >
                  Request a new password reset link
                </Link>
              </div>
            </div>
          </div>
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              Remember your password?{' '}
              <Link
                href={SITE_PATHS.AUTH.SIGN_IN}
                className="font-medium underline underline-offset-4"
              >
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="mb-8 flex flex-col items-center justify-center text-center">
        <h1 className="mb-2 text-3xl font-bold">Reset Password</h1>
        <p className="text-muted-foreground">Enter your new password</p>
      </div>
      <div className="w-full max-w-md">
        <ResetPasswordForm token={token} />
        <div className="mt-6 text-center">
          <p className="text-sm text-muted-foreground">
            Remember your password?{' '}
            <Link
              href={SITE_PATHS.AUTH.SIGN_IN}
              className="font-medium underline underline-offset-4"
            >
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
