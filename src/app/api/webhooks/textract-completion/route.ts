import { MaterialRepository } from '@/database/repository/material';
import { env } from '@/lib/env.mjs';
import {
  handleTextractFailureEvent,
  triggerTextractCompletionEvent,
} from '@/lib/workflows/event-manager';
import { Client } from '@upstash/workflow';
import { NextRequest, NextResponse } from 'next/server';

// AWS SNS signature verification imports - removed for now
// import { createHash, createHmac } from 'crypto';

interface SNSMessage {
  Type: 'Notification' | 'SubscriptionConfirmation' | 'UnsubscribeConfirmation';
  MessageId: string;
  TopicArn: string;
  Subject?: string;
  Message: string;
  Timestamp: string;
  SignatureVersion: string;
  Signature: string;
  SigningCertURL: string;
  UnsubscribeURL?: string;
  SubscribeURL?: string;
  Token?: string;
}

interface TextractJobResult {
  JobId: string;
  Status: 'SUCCEEDED' | 'FAILED' | 'PARTIAL_SUCCESS' | 'IN_PROGRESS';
  API: 'StartDocumentAnalysis';
  JobTag?: string;
  Timestamp: number;
  DocumentLocation: {
    S3ObjectName: string;
    S3Bucket: string;
  };
}

/**
 * Webhook endpoint for AWS SNS notifications about Textract job completion
 * This endpoint receives notifications when async Textract jobs complete
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[TextractWebhook] Received SNS notification');

    // Parse the SNS message
    const body = await request.text();
    const snsMessage: SNSMessage = JSON.parse(body);

    console.log(`[TextractWebhook] SNS Message Type: ${snsMessage.Type}`);

    // Handle different SNS message types
    switch (snsMessage.Type) {
      case 'SubscriptionConfirmation':
        return handleSubscriptionConfirmation(snsMessage);

      case 'Notification':
        return handleTextractNotification(snsMessage);

      case 'UnsubscribeConfirmation':
        console.log('[TextractWebhook] Received unsubscribe confirmation');
        return NextResponse.json({ message: 'Unsubscribe confirmed' });

      default:
        console.warn(`[TextractWebhook] Unknown SNS message type: ${snsMessage.Type}`);
        return NextResponse.json({ error: 'Unknown message type' }, { status: 400 });
    }
  } catch (error) {
    console.error('[TextractWebhook] Error processing SNS notification:', error);
    return NextResponse.json({ error: 'Failed to process notification' }, { status: 500 });
  }
}

/**
 * Handle SNS subscription confirmation
 */
async function handleSubscriptionConfirmation(snsMessage: SNSMessage) {
  try {
    console.log('[TextractWebhook] Handling subscription confirmation');

    if (!snsMessage.SubscribeURL) {
      throw new Error('Missing SubscribeURL in confirmation message');
    }

    // In production, you should verify the SNS signature before confirming
    // For now, we'll just log the confirmation URL
    console.log(`[TextractWebhook] Subscription URL: ${snsMessage.SubscribeURL}`);
    console.log('[TextractWebhook] Please visit the URL to confirm the subscription');

    // Optionally, you can automatically confirm by making a GET request to SubscribeURL
    // const confirmResponse = await fetch(snsMessage.SubscribeURL);
    // console.log(`[TextractWebhook] Confirmation response: ${confirmResponse.status}`);

    return NextResponse.json({
      message: 'Subscription confirmation received',
      subscribeURL: snsMessage.SubscribeURL,
    });
  } catch (error) {
    console.error('[TextractWebhook] Failed to handle subscription confirmation:', error);
    return NextResponse.json(
      { error: 'Failed to process subscription confirmation' },
      { status: 500 }
    );
  }
}

/**
 * Handle Textract job completion notification
 */
async function handleTextractNotification(snsMessage: SNSMessage) {
  try {
    console.log('[TextractWebhook] Processing Textract completion notification');

    // Parse the Textract job result from the message
    const jobResult: TextractJobResult = JSON.parse(snsMessage.Message);

    console.log(
      `[TextractWebhook] Job ${jobResult.JobId} completed with status: ${jobResult.Status}`
    );

    // Update job status in database
    await MaterialRepository.updateTextractJobStatus(
      jobResult.JobId,
      jobResult.Status,
      snsMessage.MessageId,
      new Date()
    );

    // Get job info to find the document
    const jobInfo = await MaterialRepository.getTextractJobInfo(jobResult.JobId);

    if (!jobInfo) {
      console.warn(`[TextractWebhook] No job info found for JobId: ${jobResult.JobId}`);
      return NextResponse.json({
        message: 'Job status updated but no job info found',
        jobId: jobResult.JobId,
      });
    }

    // Trigger Upstash workflow event
    await triggerWorkflowEvent(jobResult.JobId, jobInfo.documentId, jobResult.Status);

    console.log(`[TextractWebhook] Successfully processed notification for job ${jobResult.JobId}`);

    return NextResponse.json({
      message: 'Notification processed successfully',
      jobId: jobResult.JobId,
      status: jobResult.Status,
    });
  } catch (error) {
    console.error('[TextractWebhook] Failed to process Textract notification:', error);
    return NextResponse.json({ error: 'Failed to process Textract notification' }, { status: 500 });
  }
}

/**
 * Trigger Upstash workflow event for job completion
 */
async function triggerWorkflowEvent(jobId: string, documentId: string, status: string) {
  try {
    console.log(`[TextractWebhook] Triggering workflow event for job ${jobId}`);

    // Convert string status to typed status
    const typedStatus = status as 'SUCCEEDED' | 'FAILED' | 'PARTIAL_SUCCESS';

    // Use Upstash Client to notify waiting workflows
    if (env.QSTASH_URL && env.QSTASH_TOKEN) {
      const client = new Client({
        baseUrl: env.QSTASH_URL,
        token: env.QSTASH_TOKEN,
      });

      const eventId = `textract-completion-${jobId}`;
      const eventData = {
        jobId,
        documentId,
        status: typedStatus,
        timestamp: new Date().toISOString(),
      };

      await client.notify({
        eventId,
        eventData,
      });

      console.log(`[TextractWebhook] Successfully notified Upstash workflow for event ${eventId}`);
    } else {
      console.warn(
        `[TextractWebhook] QSTASH_TOKEN not available, falling back to legacy event system`
      );

      // Fallback to legacy event manager for backwards compatibility
      if (typedStatus === 'FAILED') {
        await handleTextractFailureEvent(jobId, documentId, 'Textract job failed');
      } else {
        await triggerTextractCompletionEvent(jobId, documentId, typedStatus);
      }
    }

    console.log(`[TextractWebhook] Successfully triggered workflow event for job ${jobId}`);
    return true;
  } catch (error) {
    console.error(`[TextractWebhook] Failed to trigger workflow event for job ${jobId}:`, error);
    throw error;
  }
}

/**
 * Verify SNS signature (security best practice)
 * This should be used in production to verify that notifications are actually from AWS
 */
// SNS signature verification - implementation placeholder for production security
// async function verifySNSSignature(snsMessage: SNSMessage): Promise<boolean> {
//   try {
//     // Production implementation should:
//     // 1. Download and verify the signing certificate from SigningCertURL
//     // 2. Construct the string to sign according to SNS specification
//     // 3. Verify the signature using the certificate's public key
//
//     console.log('[TextractWebhook] SNS signature verification skipped');
//     return true;
//   } catch (error) {
//     console.error('[TextractWebhook] SNS signature verification failed:', error);
//     return false;
//   }
// }

/**
 * Health check endpoint
 */
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    service: 'textract-webhook',
    timestamp: new Date().toISOString(),
  });
}
