import { db } from '@/database/drizzle';
import { Topic } from '@/database/schema';
import { getUser } from '@/lib/auth';
import { isToday, isYesterday, startOfDay, subDays } from 'date-fns';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export type UserTopic = Omit<Topic, 'userId' | 'updatedAt'>;

export type GroupedChats = {
  today: UserTopic[];
  yesterday: UserTopic[];
  previous7Days: UserTopic[];
  older: UserTopic[];
};

export async function GET() {
  try {
    const { user } = await getUser();

    if (!user) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const chats = await db.query.topics.findMany({
      where: (topics, { eq }) => eq(topics.userId, user.id),
      orderBy: (topics, { desc }) => [desc(topics.updatedAt)],
      columns: {
        id: true,
        name: true,
        createdAt: true,
        updatedAt: true,
        config: true,
      },
    });

    // Group chats by date periods
    const now = new Date();
    const yesterdayStart = startOfDay(subDays(now, 1));
    const previous7DaysStart = startOfDay(subDays(now, 7));

    const groupedChats = chats.reduce<GroupedChats>(
      (acc, chat) => {
        const chatDate = new Date(chat.updatedAt);

        if (isToday(chatDate)) {
          acc.today.push(chat);
        } else if (isYesterday(chatDate)) {
          acc.yesterday.push(chat);
        } else if (chatDate >= previous7DaysStart && chatDate < yesterdayStart) {
          acc.previous7Days.push(chat);
        } else {
          acc.older.push(chat);
        }

        return acc;
      },
      { today: [], yesterday: [], previous7Days: [], older: [] }
    );

    return NextResponse.json(groupedChats);
  } catch (error) {
    console.error('[CHATS_GET]', error);
    return new NextResponse('Internal Error', { status: 500 });
  }
}
