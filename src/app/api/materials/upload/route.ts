import { MaterialRepository } from '@/database/repository/material';
import { getUser } from '@/lib/auth';
import { deleteFromR2 } from '@/lib/cloudflare/r2';
import { env } from '@/lib/env.mjs';
import { getDocumentType } from '@/lib/materials/utils';
import { Client } from '@upstash/workflow';
import { NextRequest, NextResponse } from 'next/server';
import type { DocumentProcessingPayload } from '@/app/api/workflows/document-processing/route';

export async function POST(request: NextRequest) {
  let files: { key: string; name: string; type: string; size: number }[] = [];

  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const workflowUrl = `${env.NEXT_PUBLIC_SERVER_URL}/api/workflows/document-processing`;

    const body = await request.json();
    files = body.files as { key: string; name: string; type: string; size: number }[];
    const spaceIds = body.spaceIds as string[];

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files uploaded' }, { status: 400 });
    }

    // Check if Upstash Workflow is configured
    const useWorkflow = env.QSTASH_URL && env.QSTASH_TOKEN;

    if (!useWorkflow) {
      return NextResponse.json(
        {
          error:
            'Upstash Workflow not configured. Please add QSTASH_URL and QSTASH_TOKEN to your environment variables.',
        },
        { status: 500 }
      );
    }

    // Initialize Upstash Workflow client
    const workflowClient = new Client({
      baseUrl: env.QSTASH_URL,
      token: env.QSTASH_TOKEN,
    });

    // Process all files: create database records and trigger workflows
    const results = await Promise.all(
      files.map(async (file) => {
        const fileKey = file.key;
        let material: Awaited<ReturnType<typeof MaterialRepository.storeMaterial>> | null = null;

        try {
          // Create document record first
          material = await MaterialRepository.storeMaterial({
            fileName: file.name,
            fileType: getDocumentType(file.type),
            s3Key: fileKey,
            uploadedBy: user.id,
            summary: null, // Will be updated by workflow
            summaryEmbedding: null, // Will be updated by workflow
          });

          // Set initial status to uploaded
          await MaterialRepository.updateMaterialStatus(material.id, 'uploaded');

          // Prepare workflow payload
          const workflowPayload: DocumentProcessingPayload = {
            documentId: material.id,
            fileKey,
            fileName: file.name,
            fileType: file.type,
            uploadedBy: user.id,
            spaceIds: spaceIds || [],
            debug: true, // Enable debug logging
          };

          // Trigger workflow
          const workflowRun = await workflowClient.trigger({
            url: workflowUrl,
            body: workflowPayload,
            retries: 1,
            useFailureFunction: true,
          });

          // Store workflow job ID
          await MaterialRepository.updateWorkflowJobId(material.id, workflowRun.workflowRunId);

          return {
            success: true,
            fileName: file.name,
            materialId: material.id,
            workflowId: workflowRun.workflowRunId,
            status: 'processing',
          };
        } catch (error) {
          console.error(`Error initiating processing for file ${file.name}:`, error);

          // Clean up uploaded file from R2
          try {
            await deleteFromR2(fileKey);
          } catch (cleanupError) {
            console.error(`Error cleaning up file ${fileKey} from R2:`, cleanupError);
            // Don't throw here - we want to continue with the original error
          }

          // If material was created, mark as failed
          if (material?.id) {
            try {
              await MaterialRepository.updateProcessingError(
                material.id,
                error instanceof Error ? error.message : 'Unknown error during upload'
              );
            } catch (cleanupError) {
              console.error(`Error during cleanup for file ${file.name}:`, cleanupError);
            }
          }

          return {
            success: false,
            fileName: file.name,
            error: error instanceof Error ? error.message : 'Error initiating file processing',
          };
        }
      })
    );

    const successfulUploads = results.filter((result) => result.success);
    const failedUploads = results.filter((result) => !result.success);

    if (successfulUploads.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'All uploads failed to initialize',
          failedUploads,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `${successfulUploads.length} file(s) uploaded and processing started`,
      uploadedFiles: successfulUploads.map((result) => ({
        fileName: result.fileName,
        materialId: result.materialId,
        workflowId: result.workflowId,
        status: result.status,
      })),
      failedUploads: failedUploads.length > 0 ? failedUploads : undefined,
    });
  } catch (error) {
    console.error('Error processing upload:', error);

    // Clean up any uploaded files from R2 if we have file keys
    if (files && files.length > 0) {
      // Clean up all files in parallel
      const cleanupPromises = files.map(async (file) => {
        try {
          await deleteFromR2(file.key);
        } catch (cleanupError) {
          console.error(`Error cleaning up file ${file.key} from R2:`, cleanupError);
          // Continue with other cleanups even if one fails
        }
      });

      // Wait for all cleanup operations to complete
      await Promise.allSettled(cleanupPromises);
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
