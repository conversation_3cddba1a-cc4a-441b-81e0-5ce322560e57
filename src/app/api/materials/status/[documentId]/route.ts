import { MaterialRepository } from '@/database/repository/material';
import { getUser } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest, { params }: { params: { documentId: string } }) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId } = params;

    // Get document from database
    const documents = await MaterialRepository.getMaterialById(documentId);
    const document = documents[0];

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if user owns the document
    if (document.uploadedBy !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get workflow status if workflow job ID exists
    let workflowStatus = null;
    if (document.workflowJobId) {
      // For now, we'll use the database status as the source of truth
      // In the future, you can implement workflow status polling if needed
      workflowStatus = {
        status:
          document.status === 'completed'
            ? 'COMPLETED'
            : document.status === 'failed'
              ? 'FAILED'
              : 'PENDING',
        workflowRunId: document.workflowJobId,
      };
    }

    return NextResponse.json({
      documentId: document.id,
      fileName: document.fileName,
      status: document.status,
      workflowJobId: document.workflowJobId,
      processingError: document.processingError,
      processingTimings: document.processingTimings,
      createdAt: document.createdAt,
      updatedAt: document.updatedAt,
      workflowStatus: workflowStatus
        ? {
            status: workflowStatus.status,
            workflowRunId: workflowStatus.workflowRunId,
          }
        : null,
    });
  } catch (error) {
    console.error('Error checking document status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
