import { MaterialRepository } from '@/database/repository/material';
import { getUser } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as
      | 'uploaded'
      | 'ocr_extracting'
      | 'embedding'
      | 'summarizing'
      | 'completed'
      | 'failed'
      | null;

    // Get user's documents
    let documents;
    if (status) {
      // Filter by specific status
      const allDocuments = await MaterialRepository.getDocumentsByStatus(status);
      documents = allDocuments.filter((doc) => doc.uploadedBy === user.id);
    } else {
      // Get all user's documents
      documents = await MaterialRepository.getMaterialsByUserId(user.id);
    }

    // Transform response to include processing status info
    const documentsWithStatus = documents.map((doc) => ({
      id: doc.id,
      fileName: doc.fileName,
      fileType: doc.fileType,
      status: doc.status,
      workflowJobId: doc.workflowJobId,
      processingError: doc.processingError,
      processingTimings: doc.processingTimings,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      hasError: !!doc.processingError,
      canRetry: doc.status === 'failed',
    }));

    // Count documents by status
    const statusCounts = {
      uploaded: documentsWithStatus.filter((doc) => doc.status === 'uploaded').length,
      ocr_extracting: documentsWithStatus.filter((doc) => doc.status === 'ocr_extracting').length,
      embedding: documentsWithStatus.filter((doc) => doc.status === 'embedding').length,
      summarizing: documentsWithStatus.filter((doc) => doc.status === 'summarizing').length,
      completed: documentsWithStatus.filter((doc) => doc.status === 'completed').length,
      failed: documentsWithStatus.filter((doc) => doc.status === 'failed').length,
      total: documentsWithStatus.length,
    };

    return NextResponse.json({
      documents: documentsWithStatus,
      counts: statusCounts,
    });
  } catch (error) {
    console.error('Error fetching documents status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
