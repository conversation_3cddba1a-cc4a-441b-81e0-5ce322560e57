import { getUser } from '@/lib/auth';
import { getPresignedUrl } from '@/lib/cloudflare/r2';
import { v4 as uuidv4 } from 'uuid';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const files = body.files as { name: string; type: string; size: number }[];
    const spaceIds = body.spaceIds as string[];

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files provided' }, { status: 400 });
    }

    const presignedUrls = await Promise.all(
      files.map(async (file, index) => {
        const uuid = uuidv4();
        const key = `${uuid}`;
        const url = await getPresignedUrl(key, file.type);

        return {
          url,
          key,
          fileIndex: index,
          originalName: file.name,
        };
      })
    );

    return NextResponse.json({
      urls: presignedUrls,
      spaceIds,
    });
  } catch (error) {
    console.error('Error generating presigned URLs:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
