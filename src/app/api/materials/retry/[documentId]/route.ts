import { MaterialRepository } from '@/database/repository/material';
import { getUser } from '@/lib/auth';
import { env } from '@/lib/env.mjs';
import { getMimeTypeFromDocumentType } from '@/lib/materials/utils';
import { Client } from '@upstash/workflow';
import { NextRequest, NextResponse } from 'next/server';
import type { DocumentProcessingPayload } from '@/app/api/workflows/document-processing/route';

export async function POST(request: NextRequest, { params }: { params: { documentId: string } }) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId } = params;

    // Get document from database
    const documents = await MaterialRepository.getMaterialById(documentId);
    const document = documents[0];

    if (!document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if user owns the document
    if (document.uploadedBy !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if document can be retried (should be in failed status)
    if (document.status !== 'failed') {
      return NextResponse.json(
        { error: `Cannot retry document with status: ${document.status}` },
        { status: 400 }
      );
    }

    // Check if Upstash Workflow is configured
    if (!env.QSTASH_URL || !env.QSTASH_TOKEN) {
      return NextResponse.json({ error: 'Upstash Workflow not configured' }, { status: 500 });
    }

    // Get request body for additional parameters
    const body = await request.json().catch(() => ({}));
    const spaceIds = (body.spaceIds as string[]) || [];

    try {
      // Initialize Upstash Workflow client
      const workflowClient = new Client({
        baseUrl: env.QSTASH_URL,
        token: env.QSTASH_TOKEN,
      });

      // Reset document status and clear previous error
      await MaterialRepository.updateMaterialStatus(documentId, 'uploaded');

      // Clear previous processing error
      await MaterialRepository.updateProcessingError(documentId, '');

      // Prepare workflow payload
      const workflowPayload: DocumentProcessingPayload = {
        documentId: document.id,
        fileKey: document.s3Key,
        fileName: document.fileName,
        fileType: getMimeTypeFromDocumentType(document.fileType),
        uploadedBy: document.uploadedBy,
        spaceIds,
        debug: true,
      };

      // Trigger new workflow
      const workflowRun = await workflowClient.trigger({
        url: `${env.NEXT_PUBLIC_SERVER_URL}/api/workflows/document-processing`,
        body: workflowPayload,
        retries: 1,
        useFailureFunction: true,
      });

      // Update workflow job ID
      await MaterialRepository.updateWorkflowJobId(documentId, workflowRun.workflowRunId);

      return NextResponse.json({
        success: true,
        message: 'Document processing restarted',
        documentId: document.id,
        workflowId: workflowRun.workflowRunId,
        status: 'processing',
      });
    } catch (error) {
      console.error(`Error retrying processing for document ${documentId}:`, error);

      // Mark as failed again with new error
      await MaterialRepository.updateProcessingError(
        documentId,
        error instanceof Error ? error.message : 'Unknown error during retry'
      );

      return NextResponse.json({ error: 'Failed to restart document processing' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in retry endpoint:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
