import documentProcessingWorkflow, {
  handleDocumentProcessingFailure,
} from '@/lib/workflows/document-processing';
import type { DocumentProcessingPayload } from '@/lib/workflows/document-processing';
import { WorkflowContext } from '@upstash/workflow';
import { serve } from '@upstash/workflow/nextjs';

// Create the workflow endpoint
export const { POST } = serve(
  async (context: WorkflowContext) => {
    return await documentProcessingWorkflow(context);
  },
  {
    failureFunction: async ({ context, failStatus, failResponse, failHeaders }) => {
      await handleDocumentProcessingFailure(
        context as WorkflowContext,
        failStatus,
        failResponse,
        failHeaders
      );
    },
    retries: 1,
    // verbose: true,
  }
);

// Export types for use in other files
export type { DocumentProcessingPayload };
