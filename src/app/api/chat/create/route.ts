import { db } from '@/database/drizzle';
import { interactions, topics } from '@/database/schema';
import { getUser } from '@/lib/auth';
import { ChatConfig } from '@/types/chat';

export async function POST(req: Request) {
  const session = await getUser();
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { initialQuery, config } = await req.json();
  if (!initialQuery || typeof initialQuery !== 'string') {
    return new Response('Invalid request: initialQuery is required', { status: 400 });
  }

  try {
    const result = await db.transaction(async (tx) => {
      // Create a new topic with the initial query as the name and store the config
      const [topic] = await tx
        .insert(topics)
        .values({
          name: initialQuery.slice(0, 255), // Limit the name length
          userId: session.user?.id ?? '',
          config: config as ChatConfig, // Store the chat configuration
        })
        .returning();

      // Create the initial interaction
      await tx.insert(interactions).values({
        topicId: topic.id,
        userId: session.user?.id ?? '',
        content: initialQuery,
        senderType: 'user',
      });

      return topic;
    });

    return new Response(JSON.stringify({ chatId: result.id }), {
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Failed to create chat topic:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
