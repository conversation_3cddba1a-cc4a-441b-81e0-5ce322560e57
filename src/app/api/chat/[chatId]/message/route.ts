import { db } from '@/database/drizzle';
import { interactions, topics, type RetrievedDocumentChunkStore } from '@/database/schema';
import { ChatInteraction } from '@/hooks/use-chat';
import { getUser } from '@/lib/auth';
import { generateChatResponse } from '@/lib/azure/openai';
import { searchMaterials } from '@/lib/materials/search';
import { ChatConfig, DocumentMetadata, RetrievedDocumentChunkDTO } from '@/types/chat';
import { eq } from 'drizzle-orm';

export async function POST(req: Request, { params }: { params: { chatId: string } }) {
  const session = await getUser();
  if (!session) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { content, isInitialMessage, config } = await req.json();

  // First, get the existing topic to retrieve its stored config
  const existingTopic = await db.query.topics.findFirst({
    where: eq(topics.id, params.chatId),
    columns: {
      id: true,
      config: true,
      userId: true,
    },
  });

  if (!existingTopic) {
    return new Response('Chat not found', { status: 404 });
  }

  // Ensure user owns the topic
  if (existingTopic.userId !== session.user?.id) {
    return new Response('Unauthorized', { status: 403 });
  }

  // Merge the stored config with any provided config, giving priority to provided config
  const storedConfig = existingTopic.config as ChatConfig;
  const providedConfig = config as ChatConfig;

  // Default values
  const defaultConfig: ChatConfig = {
    searchMode: 'semantic' as const,
    spaceIds: [],
    documentIds: [],
  };

  const chatConfig: ChatConfig = {
    ...defaultConfig,
    ...storedConfig,
    // Allow all fields from provided config to override stored config
    ...(providedConfig && providedConfig),
  };

  // Update topic's config only if it has changed (to save bandwidth)
  if (config) {
    const configChanged =
      JSON.stringify(storedConfig?.spaceIds?.sort()) !==
        JSON.stringify(chatConfig.spaceIds?.sort()) ||
      JSON.stringify(storedConfig?.documentIds?.sort()) !==
        JSON.stringify(chatConfig.documentIds?.sort()) ||
      storedConfig?.searchMode !== chatConfig.searchMode;

    if (configChanged) {
      console.log('💾 Updating chat config in database - config has changed');
      await db.update(topics).set({ config: chatConfig }).where(eq(topics.id, params.chatId));
    } else {
      console.log('🔄 Skipping config update - no changes detected');
    }
  }

  // Only save user message if it's not an initial message
  if (!isInitialMessage && content) {
    await db.insert(interactions).values({
      topicId: params.chatId,
      userId: session.user?.id ?? '',
      content,
      senderType: 'user',
    });
  }

  // Get chat history
  const messages = await db.query.interactions.findMany({
    where: eq(interactions.topicId, params.chatId),
    orderBy: (interactions, { asc }) => [asc(interactions.createdAt)],
  });

  // Search for relevant documents using the user's query and chat config
  const searchResults = await searchMaterials({
    query: content,
    mode: 'embedding',
    limit: 10, // Use a default limit since maxDocuments is no longer configurable
    spaceIds: chatConfig.spaceIds ? chatConfig.spaceIds : undefined,
    documentIds: chatConfig.documentIds ? chatConfig.documentIds : undefined,
  });

  // Transform search results into RetrievedDocumentChunkStore and RetrievedDocumentChunkDTO formats
  const retrievedChunks: RetrievedDocumentChunkDTO[] = [];
  const retrievedChunksStore: RetrievedDocumentChunkStore[] = [];

  searchResults.forEach((result) => {
    retrievedChunks.push({
      id: result.id,
      chunkId: result.chunkId,
      fileName: result.fileName,
      content: result.content,
      key: result.key,
      similarity: result.similarity,
      metadata: result.metadata as DocumentMetadata,
      fileType: result.fileType,
    });

    retrievedChunksStore.push({
      id: result.id,
      chunkId: result.chunkId,
      similarity: result.similarity,
      metadata: result.metadata as DocumentMetadata,
    });
  });

  // Create a context string from the search results
  const context =
    searchResults.length > 0
      ? `Here are some relevant documents that might help answer the question:\n\n${searchResults
          .map((result, index) => `[Document ${index + 1}] ${result.content}`)
          .join('\n\n')}`
      : '';

  // Create a TransformStream for streaming the response
  const stream = new TransformStream();
  const writer = stream.writable.getWriter();
  const encoder = new TextEncoder();

  let fullResponse = '';

  // Start streaming in the background
  (async () => {
    try {
      // Save initial AI message with empty content and retrieved documents
      const [aiMessage] = await db
        .insert(interactions)
        .values({
          topicId: params.chatId,
          userId: session.user?.id ?? '',
          content: '',
          senderType: 'ai',
          retrievedDocuments: retrievedChunksStore,
        })
        .returning();

      // Add system message with context if available
      const systemMessages = context
        ? [
            {
              content: `${context}\n\nPlease use the above documents to help answer the user's questions. If the documents don't contain relevant information, Please say that you don't know.`,
              senderType: 'system' as const,
            },
          ]
        : [];

      const allMessages = [...systemMessages, ...messages] as ChatInteraction[];

      const result = await generateChatResponse(allMessages, {
        temperature: 0.7,
        max_tokens: 500,
      });

      for await (const chunk of result) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          fullResponse += content;
          await writer.write(encoder.encode(content));
        }
      }

      // Update the AI message with complete response
      await db
        .update(interactions)
        .set({ content: fullResponse })
        .where(eq(interactions.id, aiMessage.id));

      // Signal that streaming is complete and send references
      const completionMessage = JSON.stringify({
        type: 'completion',
        references: retrievedChunks,
      });
      await writer.write(encoder.encode(`\nEOM${completionMessage}`)); // EOM is End of Message
    } catch (error) {
      console.error('Streaming error:', error);
      // Write error message to stream
      const errorMsg = 'An error occurred while generating the response';
      await writer.write(encoder.encode(errorMsg));
    } finally {
      await writer.close();
    }
  })();

  return new Response(stream.readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
    },
  });
}
