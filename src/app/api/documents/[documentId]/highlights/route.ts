import { db } from '@/database/drizzle';
import { documentChunks, documents } from '@/database/schema';
import { getUser } from '@/lib/auth';
import { convertChunksToHighlights } from '@/lib/materials/ocr-utils';
import { and, eq, isNotNull } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: { documentId: string } }) {
  try {
    const { user } = await getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { documentId } = params;

    // Verify user has access to this document
    const document = await db.select().from(documents).where(eq(documents.id, documentId)).limit(1);

    if (!document[0]) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Check if user has access to this document (owner or shared space)
    if (document[0].uploadedBy !== user.id) {
      // TODO: Add space permission check here
      // For now, we'll just allow access
    }

    const searchParams = request.nextUrl.searchParams;
    const minConfidence = parseFloat(searchParams.get('minConfidence') || '0');
    const pageNumber = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null;

    // Build WHERE conditions
    const whereConditions = [
      eq(documentChunks.documentId, documentId),
      isNotNull(documentChunks.boundingBox),
      isNotNull(documentChunks.layoutType),
    ];

    // Add page filter if specified
    if (pageNumber !== null) {
      whereConditions.push(eq(documentChunks.pageNumber, pageNumber));
    }

    // Get highlights from document chunks ONLY (optimal performance)
    const chunks = await db
      .select()
      .from(documentChunks)
      .where(and(...whereConditions))
      .orderBy(documentChunks.pageNumber, documentChunks.chunkOrder);

    if (chunks.length === 0) {
      return NextResponse.json({
        highlights: [],
        message:
          pageNumber !== null
            ? `No OCR data found for page ${pageNumber}`
            : 'No OCR data found for this document',
      });
    }

    const highlights = convertChunksToHighlights(chunks);
    const filteredHighlights = highlights.filter((h) => h.confidence >= minConfidence);

    // Get total page count for pagination metadata (only if page filtering is used)
    const totalPages =
      pageNumber === null
        ? null
        : await db
            .selectDistinct({ pageNumber: documentChunks.pageNumber })
            .from(documentChunks)
            .where(
              and(
                eq(documentChunks.documentId, documentId),
                isNotNull(documentChunks.boundingBox),
                isNotNull(documentChunks.layoutType)
              )
            );

    return NextResponse.json({
      highlights: filteredHighlights,
      source: 'chunks',
      page: pageNumber,
      totalChunks: chunks.length,
      chunksWithOcr: chunks.filter((c) => c.boundingBox && c.layoutType).length,
      // Pagination metadata
      ...(totalPages && {
        totalPages: totalPages.length,
        availablePages: totalPages.map((p) => p.pageNumber).sort((a, b) => a - b),
      }),
    });
  } catch (error) {
    console.error('Error fetching document highlights:', error);
    return NextResponse.json({ error: 'Failed to fetch document highlights' }, { status: 500 });
  }
}
