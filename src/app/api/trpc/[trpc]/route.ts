import { env } from '@/lib/env.mjs';
import { createTRPCContext } from '@/lib/trpc/context';
import { appRouter } from '@/server/trpc/routers';
import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { NextRequest } from 'next/server';

const createContext = async (req: NextRequest) => {
  return createTRPCContext({ headers: req.headers });
};

const handler = (req: NextRequest) =>
  fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: () => createContext(req),
    onError:
      env.NODE_ENV === 'development'
        ? ({ path, error, input }) => {
            console.error(`❌ tRPC failed on ${path ?? '<no-path>'}: ${error}`);
            console.log('input:', input);
          }
        : undefined,
  });

export { handler as GET, handler as POST };
