import { AppSidebar } from '@/components/app-sidebar';
import { MainNav } from '@/components/main-nav';
import { SidebarProvider } from '@/components/ui/sidebar';
import { getUser } from '@/lib/auth';
import { UserProvider } from '@/providers/user-provider';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Knowledge Sphere',
  description: 'Manage your knowledge with ease',
};

export default async function MainLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { user } = await getUser();

  return (
    <UserProvider user={user}>
      <SidebarProvider>
        {user && <AppSidebar user={user} />}
        <div className="flex flex-1 flex-col">
          <MainNav className="sticky top-0 z-20 bg-background" user={user} />
          <div className="flex-1">{children}</div>
        </div>
      </SidebarProvider>
    </UserProvider>
  );
}
