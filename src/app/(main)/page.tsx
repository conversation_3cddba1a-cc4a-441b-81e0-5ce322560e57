import { SITE_PATHS } from '@/configs/site';
import { getUser } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default async function Home() {
  const { user } = await getUser();
  if (!user) {
    redirect(SITE_PATHS.AUTH.SIGN_IN);
  }

  if (!user.emailVerified) {
    redirect(SITE_PATHS.AUTH.VERIFY_EMAIL);
  }

  return (
    <div className="grid h-full grid-rows-[20px_1fr_20px] items-center justify-items-center gap-16 p-8 pb-20 font-[family-name:var(--font-geist-sans)] sm:p-20">
      <main className="row-start-2 flex flex-col items-center gap-8">
        <h1 className="text-4xl font-bold">Welcome to the Knowledge Sphere</h1>
        <p className="text-lg">
          The Knowledge Sphere is a platform for storing and searching through your documents.
        </p>
      </main>
      {/* <footer className="row-start-3 flex items-center justify-center">
        <p className="text-sm text-gray-500">
          &copy; {new Date().getFullYear()} Your Company Name. All rights reserved.
        </p>
      </footer> */}
    </div>
  );
}
