import { ChatUIEnhanced } from '@/components/chat/chat-ui-enhanced';
import { SITE_PATHS } from '@/configs/site';
import { ChatRepository } from '@/database/repository/chat';
import { getUser } from '@/lib/auth';
import { env } from '@/lib/env.mjs';
import { redirect } from 'next/navigation';

interface EnhancedChatPageProps {
  params: {
    chatId: string;
  };
}

export default async function EnhancedChatPage({ params }: EnhancedChatPageProps) {
  const { user } = await getUser();
  if (!user) {
    redirect(SITE_PATHS.AUTH.SIGN_IN);
  }

  const topic = await ChatRepository.getTopicWithDocuments(params.chatId);

  if (!topic) {
    redirect('/'); // Redirect to home if topic not found
  }

  // Ensure user owns the topic
  if (topic.userId !== user.id) {
    redirect('/'); // Redirect if user doesn't own the topic
  }

  return (
    <div className="h-full">
      <ChatUIEnhanced
        initialTopic={topic}
        materialAccessUrl={env.NEXT_PUBLIC_MATERIAL_ACCESS_URL}
      />
    </div>
  );
}
