// import { Chat<PERSON> } from '@/components/chat/chat-ui';
// import { ChatRepository } from '@/database/repository/chat';
import { getUser } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default async function ChatPage({ params }: { params: { chatId: string } }) {
  const session = await getUser();
  if (!session) {
    redirect('/login');
  }

  redirect(`/chat/${params.chatId}/enhanced`);

  // const topic = await ChatRepository.getTopicWithDocuments(params.chatId);

  // if (!topic) {
  //   console.error('Topic not found, redirect to search page');
  //   redirect('/search');
  // }

  // return (
  //   <div className="flex h-full flex-col">
  //     <ChatUI initialTopic={topic} />
  //   </div>
  // );
}
