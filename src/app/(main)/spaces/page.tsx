import SpacePage from '@/components/space/space-page';
import { SITE_PATHS } from '@/configs/site';
import { getUser } from '@/lib/auth';
import { redirect } from 'next/navigation';

export default async function Spaces() {
  const { user } = await getUser();
  if (!user) {
    redirect(SITE_PATHS.AUTH.SIGN_IN);
  }

  if (!user.emailVerified) {
    redirect(SITE_PATHS.AUTH.VERIFY_EMAIL);
  }

  return <SpacePage />;
}
