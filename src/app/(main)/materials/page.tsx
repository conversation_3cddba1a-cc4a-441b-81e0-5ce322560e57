'use client';

import { Button } from '@/components/ui/button';
import { SITE_PATHS } from '@/configs/site';
import { MaterialList } from '@/features/materials/components/material-list';
import { UploadMaterialDialog } from '@/features/materials/components/upload-material-dialog';
import { clientApi } from '@/lib/trpc/client-api';
import { useUserStore } from '@/stores/user';
import { Plus } from 'lucide-react';
import { redirect } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function Materials() {
  const user = useUserStore((state) => state.user);
  const loading = useUserStore((state) => state.loading);
  const [isUploading, setIsUploading] = useState(false);

  const { data: materials, isLoading } = clientApi.material.list.useQuery(undefined, {
    enabled: !!user,
  });

  useEffect(() => {
    if (!loading && !user) {
      redirect(SITE_PATHS.AUTH.SIGN_IN);
    }
  }, [user, loading]);

  if (loading || isLoading) {
    return (
      <div className="container mx-auto px-8 pt-8">
        <h1 className="mb-4 text-2xl font-bold">Materials</h1>
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-8 pt-8">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Materials</h1>
        <Button onClick={() => setIsUploading(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Upload Material
        </Button>
      </div>

      <UploadMaterialDialog open={isUploading} onOpenChange={setIsUploading} />

      <div>
        <MaterialList materials={materials ?? []} />
      </div>
    </div>
  );
}
