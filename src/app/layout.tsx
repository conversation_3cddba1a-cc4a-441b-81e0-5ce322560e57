import { Toaster } from '@/components/ui/sonner';
import type { Metadata } from 'next';
import localFont from 'next/font/local';
import './globals.css';
import { TRPCProvider } from '@/lib/trpc/provider';
import { ClientProvider } from '@/providers/client-provider';
import { PDFWorkerProvider } from '@/providers/pdf-worker-provider';
import { cookies } from 'next/headers';

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
  weight: '100 900',
});
const geistMono = localFont({
  src: './fonts/GeistMonoVF.woff',
  variable: '--font-geist-mono',
  weight: '100 900',
});

export const metadata: Metadata = {
  title: 'Knowledge Sphere',
  description: 'Manage your knowledge with ease',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <ClientProvider>
          <TRPCProvider cookies={cookies().toString()}>
            <PDFWorkerProvider>{children}</PDFWorkerProvider>
          </TRPCProvider>
        </ClientProvider>
        <Toaster position="top-right" richColors />
      </body>
    </html>
  );
}
