'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { clientApi } from '@/lib/trpc/client-api';
import { formatDate } from '@/lib/utils';
import { useUserStore } from '@/stores/user';
import { skipToken } from '@tanstack/react-query';
import { Co<PERSON>, Loader2, Share2, Trash2, User<PERSON>inus } from 'lucide-react';
import { toast } from 'sonner';
import { useEffect, useState } from 'react';

type SpaceInvitation = {
  id: number;
  spaceId: string;
  code: string;
  createdBy: string;
  useCount: number;
  createdAt: Date;
};

type SpaceMember = {
  id: number;
  spaceId: string;
  userId: string;
  role: 'owner' | 'editor' | 'viewer';
  invitationId: number | null;
  createdAt: Date;
  updatedAt: Date;
  userName: string;
  userEmail: string;
};

type ShareSpaceDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  spaceId: string;
  spaceName: string;
  isShared: boolean;
};

export function ShareSpaceDialog({
  isOpen,
  onClose,
  spaceId,
  spaceName,
  isShared,
}: ShareSpaceDialogProps) {
  const [sharingEnabled, setSharingEnabled] = useState(isShared);
  const [invitations, setInvitations] = useState<SpaceInvitation[]>([]);
  const [activeTab, setActiveTab] = useState('share');
  const [removingMemberId, setRemovingMemberId] = useState<string | null>(null);
  const utils = clientApi.useUtils();
  const currentUser = useUserStore((state) => state.user);

  // Query to get invitations
  const { data: invitationsData, refetch: refetchInvitations } =
    clientApi.space.getInvitations.useQuery(spaceId, {
      enabled: isOpen && sharingEnabled,
    });

  // Query to get members
  const { data: members = [] as SpaceMember[], isLoading: isLoadingMembers } =
    clientApi.space.getMembers.useQuery(
      activeTab === 'members' && isOpen && sharingEnabled ? spaceId : skipToken
    );

  // Mutation to toggle sharing
  const toggleSharing = clientApi.space.toggleSharing.useMutation({
    onSuccess: () => {
      setSharingEnabled(!sharingEnabled);
      toast.success(`Sharing ${!sharingEnabled ? 'enabled' : 'disabled'} for ${spaceName}`);
      // Invalidate space lists to refresh data
      utils.space.list.invalidate();
      utils.space.listAll.invalidate();

      if (!sharingEnabled) {
        // If enabling sharing, fetch invitations
        refetchInvitations();
      }
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to update sharing settings');
    },
  });

  // Mutation to create invitation
  const createInvitation = clientApi.space.createInvitation.useMutation({
    onSuccess: () => {
      toast.success('Invitation code created');
      refetchInvitations();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to create invitation code');
    },
  });

  // Mutation to delete invitation
  const deleteInvitation = clientApi.space.deleteInvitation.useMutation({
    onSuccess: () => {
      toast.success('Invitation deleted');
      refetchInvitations();
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to delete invitation');
    },
  });

  // Mutation to remove a member
  const removeMember = clientApi.space.removeMember.useMutation({
    onSuccess: () => {
      toast.success('Member removed from space');
      utils.space.getMembers.invalidate(spaceId);
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to remove member');
    },
    onSettled: () => {
      setRemovingMemberId(null);
    },
  });

  // Update invitations when data changes
  useEffect(() => {
    if (invitationsData) {
      setInvitations(invitationsData);
    }
  }, [invitationsData]);

  // Handle toggling sharing
  const handleToggleSharing = () => {
    toggleSharing.mutate({ id: spaceId, isShared: !sharingEnabled });
  };

  // Handle creating a new invitation
  const handleCreateInvitation = () => {
    createInvitation.mutate({
      spaceId,
    });
  };

  // Handle deleting an invitation
  const handleDeleteInvitation = (id: number) => {
    deleteInvitation.mutate(id);
  };

  // Handle copying an invitation code to clipboard
  const handleCopyInvitation = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Invitation code copied to clipboard');
  };

  // Handle removing a member
  const handleRemoveMember = (userId: string) => {
    if (userId === currentUser?.id) {
      toast.error("You can't remove yourself from the space");
      return;
    }

    if (confirm('Are you sure you want to remove this member from the space?')) {
      setRemovingMemberId(userId);
      removeMember.mutate({
        spaceId,
        userId,
      });
    }
  };

  // Get badge variant for role display
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'editor':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md md:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share Space: {spaceName}
          </DialogTitle>
          <DialogDescription>
            {sharingEnabled
              ? 'This space is currently shared. You can manage invitations and access settings here.'
              : 'Enable sharing to allow other users to access this space via invitation links.'}
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center space-x-2 py-4">
          <Switch
            id="sharing-toggle"
            checked={sharingEnabled}
            onCheckedChange={handleToggleSharing}
            disabled={toggleSharing.isPending}
          />
          <Label htmlFor="sharing-toggle">
            {sharingEnabled ? 'Sharing Enabled' : 'Sharing Disabled'}
          </Label>
        </div>

        {sharingEnabled && (
          <Tabs defaultValue="share" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="share">Share Links</TabsTrigger>
              <TabsTrigger value="members">Members</TabsTrigger>
            </TabsList>

            <TabsContent value="share" className="space-y-4 py-4">
              <Card>
                <CardHeader>
                  <CardTitle>Create New Invitation Code</CardTitle>
                  <CardDescription>
                    Generate a code that can be used to join this space
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button
                    onClick={handleCreateInvitation}
                    disabled={createInvitation.isPending}
                    className="w-full"
                  >
                    {createInvitation.isPending ? 'Creating...' : 'Create Invitation Code'}
                  </Button>
                </CardFooter>
              </Card>

              <h3 className="text-lg font-medium">Active Invitation Codes</h3>
              {invitations.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No active invitation codes. Create one to share this space.
                </p>
              ) : (
                <div className="space-y-4">
                  {invitations.map((invitation) => (
                    <Card key={invitation.id}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">Invitation Code</CardTitle>
                          <div className="flex items-center gap-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleCopyInvitation(invitation.code)}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Copy Invitation Code</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleDeleteInvitation(invitation.id)}
                                    disabled={deleteInvitation.isPending}
                                  >
                                    <Trash2 className="h-4 w-4 text-destructive" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Delete Invitation</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="font-mono text-sm">{invitation.code}</div>
                        <div className="mt-2">
                          <Badge variant="outline">Uses: {invitation.useCount}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="members" className="space-y-4 py-4">
              <Card>
                <CardHeader>
                  <CardTitle>Space Members</CardTitle>
                  <CardDescription>View and manage users with access to this space</CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingMembers ? (
                    <div className="flex justify-center py-4">
                      <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
                    </div>
                  ) : members.length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      No members have joined this space yet.
                    </p>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>User</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Joined</TableHead>
                          <TableHead className="w-[70px]"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {members.map((member) => (
                          <TableRow key={member.id}>
                            <TableCell className="">
                              <span className="font-medium">{member.userName}</span>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {member.userEmail}
                            </TableCell>
                            <TableCell>
                              <Badge variant={getRoleBadgeVariant(member.role)}>
                                {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatDate(member.createdAt)}</TableCell>
                            <TableCell>
                              {member.role !== 'owner' && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => handleRemoveMember(member.userId)}
                                        disabled={removingMemberId === member.userId}
                                        className="h-8 w-8 text-destructive hover:bg-destructive/10"
                                      >
                                        {removingMemberId === member.userId ? (
                                          <Loader2 className="h-4 w-4 animate-spin" />
                                        ) : (
                                          <UserMinus className="h-4 w-4" />
                                        )}
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Remove Member</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
