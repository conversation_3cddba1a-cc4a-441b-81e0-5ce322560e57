'use client';

import { But<PERSON> } from '@/components/ui/button';
import { SITE_PATHS } from '@/configs/site';
import { CreateSpaceDialog } from '@/features/spaces/components/create-space-form';
import { JoinSpaceDialog } from '@/features/spaces/components/join-space-dialog';
import { SpaceList } from '@/features/spaces/components/space-list';
import { clientApi } from '@/lib/trpc/client-api';
import { useUserStore } from '@/stores/user';
import { Plus, UsersRound } from 'lucide-react';
import { redirect } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function SpacePage() {
  const user = useUserStore((state) => state.user);
  const loading = useUserStore((state) => state.loading);
  const [isCreating, setIsCreating] = useState(false);
  const [isJoining, setIsJoining] = useState(false);

  // const { data: spaces, isLoading } = clientApi.space.list.useQuery(undefined, {
  const { data: spaces, isLoading } = clientApi.space.listAll.useQuery(undefined, {
    enabled: !!user,
  });

  useEffect(() => {
    if (!loading && !user) {
      redirect(SITE_PATHS.AUTH.SIGN_IN);
    }
  }, [user, loading]);

  if (loading || isLoading) {
    return (
      <div className="container mx-auto px-8 pt-8">
        <h1 className="mb-4 text-2xl font-bold">Spaces</h1>
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-8 pt-8">
      <div className="mb-8 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Spaces</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsJoining(true)} variant="outline">
            <UsersRound className="mr-2 h-4 w-4" />
            Join Space
          </Button>
          <Button onClick={() => setIsCreating(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Space
          </Button>
        </div>
      </div>

      <CreateSpaceDialog open={isCreating} onOpenChange={setIsCreating} />
      <JoinSpaceDialog open={isJoining} onOpenChange={setIsJoining} />

      <div>
        <SpaceList spaces={spaces ?? []} />
      </div>
    </div>
  );
}
