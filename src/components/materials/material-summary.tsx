'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { BookOpen, ChevronDown, ChevronRight } from 'lucide-react';
import { useState } from 'react';

interface MaterialSummaryProps {
  summary: string | null;
  className?: string;
}

export function MaterialSummary({ summary, className }: MaterialSummaryProps) {
  const [isExpanded, setIsExpanded] = useState(true); // Start expanded to show all content

  // Don't render if no summary
  if (!summary) {
    return null;
  }

  // Parse the structured summary if it contains sections
  const parseSummary = (summaryText: string) => {
    const sections = summaryText.split('\n\n');
    const parsed = {
      executiveSummary: '',
      overallSummary: '',
      chapters: [] as Array<{ title: string; content: string }>,
      keyTopics: [] as string[],
      documentType: '',
      targetAudience: '',
      mainPurpose: '',
    };

    let currentSection = '';

    for (const section of sections) {
      const trimmed = section.trim();
      if (!trimmed) continue;

      if (trimmed.startsWith('EXECUTIVE SUMMARY:')) {
        parsed.executiveSummary = trimmed.replace('EXECUTIVE SUMMARY:', '').trim();
        currentSection = 'executive';
      } else if (trimmed.startsWith('OVERALL SUMMARY:')) {
        parsed.overallSummary = trimmed.replace('OVERALL SUMMARY:', '').trim();
        currentSection = 'overall';
      } else if (trimmed.startsWith('CHAPTER SUMMARIES:')) {
        currentSection = 'chapters';
      } else if (trimmed.startsWith('KEY TOPICS:')) {
        const topicsText = trimmed.replace('KEY TOPICS:', '').trim();
        parsed.keyTopics = topicsText
          .split(',')
          .map((t) => t.trim())
          .filter(Boolean);
        currentSection = 'topics';
      } else if (trimmed.startsWith('DOCUMENT TYPE:')) {
        parsed.documentType = trimmed.replace('DOCUMENT TYPE:', '').trim();
        currentSection = 'type';
      } else if (trimmed.startsWith('TARGET AUDIENCE:')) {
        parsed.targetAudience = trimmed.replace('TARGET AUDIENCE:', '').trim();
        currentSection = 'audience';
      } else if (trimmed.startsWith('MAIN PURPOSE:')) {
        parsed.mainPurpose = trimmed.replace('MAIN PURPOSE:', '').trim();
        currentSection = 'purpose';
      } else if (currentSection === 'chapters' && trimmed.includes(':')) {
        const [title, ...contentParts] = trimmed.split(':');
        if (title && contentParts.length > 0) {
          parsed.chapters.push({
            title: title.trim(),
            content: contentParts.join(':').trim(),
          });
        }
      }
    }

    return parsed;
  };

  const parsedSummary = parseSummary(summary);
  const hasStructuredContent =
    parsedSummary.executiveSummary ||
    parsedSummary.overallSummary ||
    parsedSummary.chapters.length > 0;

  return (
    <Card className={cn('border-blue-200 bg-blue-50/50', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-base">
            <BookOpen className="h-4 w-4 text-blue-600" />
            Document Summary
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-8 px-2 text-xs hover:bg-blue-100"
          >
            {isExpanded ? (
              <>
                <ChevronDown className="mr-1 h-3 w-3" />
                Collapse
              </>
            ) : (
              <>
                <ChevronRight className="mr-1 h-3 w-3" />
                Expand
              </>
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {hasStructuredContent ? (
          <div className="space-y-3">
            {/* Executive Summary - Always visible */}
            {parsedSummary.executiveSummary && (
              <div className="rounded-md bg-white p-3 shadow-sm">
                <h4 className="mb-2 text-sm font-medium text-blue-800">Quick Overview</h4>
                <p className="text-sm leading-relaxed text-gray-700">
                  {parsedSummary.executiveSummary}
                </p>
              </div>
            )}

            {/* Expandable detailed content */}
            {isExpanded && (
              <div className="space-y-3">
                {/* Overall Summary */}
                {parsedSummary.overallSummary && (
                  <div className="rounded-md bg-white p-3 shadow-sm">
                    <h4 className="mb-2 text-sm font-medium text-blue-800">Detailed Summary</h4>
                    <p className="text-sm leading-relaxed text-gray-700">
                      {parsedSummary.overallSummary}
                    </p>
                  </div>
                )}

                {/* Chapter Summaries */}
                {parsedSummary.chapters.length > 0 && (
                  <div className="rounded-md bg-white p-3 shadow-sm">
                    <h4 className="mb-2 text-sm font-medium text-blue-800">Chapter Summaries</h4>
                    <div className="space-y-2">
                      {parsedSummary.chapters.map((chapter, index) => (
                        <div key={index} className="border-l-2 border-blue-200 pl-3">
                          <h5 className="text-xs font-medium text-blue-700">{chapter.title}</h5>
                          <p className="text-xs leading-relaxed text-gray-600">{chapter.content}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Key Topics */}
                {parsedSummary.keyTopics.length > 0 && (
                  <div className="rounded-md bg-white p-3 shadow-sm">
                    <h4 className="mb-2 text-sm font-medium text-blue-800">Key Topics</h4>
                    <div className="flex flex-wrap gap-1">
                      {parsedSummary.keyTopics.map((topic, index) => (
                        <span
                          key={index}
                          className="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-700"
                        >
                          {topic}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Document Metadata */}
                {(parsedSummary.documentType ||
                  parsedSummary.targetAudience ||
                  parsedSummary.mainPurpose) && (
                  <div className="rounded-md bg-white p-3 shadow-sm">
                    <h4 className="mb-2 text-sm font-medium text-blue-800">Document Details</h4>
                    <div className="space-y-1 text-xs text-gray-600">
                      {parsedSummary.documentType && (
                        <div>
                          <span className="font-medium">Type:</span> {parsedSummary.documentType}
                        </div>
                      )}
                      {parsedSummary.targetAudience && (
                        <div>
                          <span className="font-medium">Audience:</span>{' '}
                          {parsedSummary.targetAudience}
                        </div>
                      )}
                      {parsedSummary.mainPurpose && (
                        <div>
                          <span className="font-medium">Purpose:</span> {parsedSummary.mainPurpose}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          // Fallback for unstructured summaries
          <div className="rounded-md bg-white p-3 shadow-sm">
            <p className="whitespace-pre-wrap text-sm leading-relaxed text-gray-700">
              {isExpanded
                ? summary
                : summary.length > 200
                  ? `${summary.substring(0, 200)}...`
                  : summary}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
