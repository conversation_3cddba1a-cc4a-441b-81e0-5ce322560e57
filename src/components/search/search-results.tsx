import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { DocumentMetadata } from '@/types/chat';
import { DocumentType, MaterialSearchResult } from '@/types/material';
import {
  ChevronLeft,
  ChevronRight,
  FileSpreadsheetIcon,
  FileTextIcon,
  FileTypeIcon,
  MapPin,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

type SearchResultsProps = {
  results: MaterialSearchResult[];
  totalCount?: number;
  actualSearchQuery: string;
  onDocumentSelect?: (document: MaterialSearchResult | null) => void;
  className?: string;
};

function cleanContent(content: string): string {
  return content
    .replace(/\n+/g, ' ') // Replace multiple newlines with single space
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim();
}

function highlightText(text: string, query: string): JSX.Element {
  if (!query.trim()) return <>{text}</>;

  const parts = text.split(new RegExp(`(${query})`, 'gi'));
  return (
    <>
      {parts.map((part, i) =>
        part.toLowerCase() === query.toLowerCase() ? (
          <span key={i} className="bg-yellow-100 font-medium">
            {part}
          </span>
        ) : (
          part
        )
      )}
    </>
  );
}

function getFileIcon(fileType: DocumentType) {
  switch (fileType) {
    case DocumentType.PDF:
      return <FileSpreadsheetIcon className="h-5 w-5 text-red-500" />;
    case DocumentType.DOCX:
      return <FileTextIcon className="h-5 w-5 text-blue-500" />;
    default:
      return <FileTypeIcon className="h-5 w-5 text-muted-foreground" />;
  }
}

export function SearchResults({
  results,
  totalCount,
  actualSearchQuery,
  onDocumentSelect,
  className,
}: SearchResultsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '20', 10);
  const totalPages = totalCount ? Math.ceil(totalCount / pageSize) : 1;

  // Calculate the range of results being shown
  const startResult = (currentPage - 1) * pageSize + 1;
  const endResult = Math.min(currentPage * pageSize, totalCount || results.length);
  const totalResults = totalCount || results.length;

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());
    router.push(`/search?${params.toString()}`);
  };

  if (results.length === 0) {
    return (
      <div className="mt-8 text-center text-muted-foreground">
        No results found for &quot;{actualSearchQuery}&quot;
      </div>
    );
  }

  return (
    <div className={cn('h-full', className)}>
      <div className="ml-2 flex items-center justify-between pr-4">
        <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
          <span>
            Showing {startResult}-{endResult} of {totalResults} matching chunks
          </span>
          {actualSearchQuery && (
            <span className="">
              for &quot;<span className="font-bold">{actualSearchQuery}</span>&quot;
            </span>
          )}
        </div>

        {/* Top Pagination Controls */}
        {totalCount && totalCount > pageSize && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="mt-2 space-y-4 pb-4 pr-4">
        {results.map((result) => {
          const metadata = result.metadata as DocumentMetadata;
          const cleanedContent = cleanContent(result.content);

          return (
            <Card key={`${result.id}-${result.chunkId}`} className="p-4">
              <div className="flex items-start gap-3">
                <div className="mt-1 shrink-0">{getFileIcon(result.fileType)}</div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center justify-between gap-2">
                    <h3 className="flex-1 truncate font-medium">{result.fileName}</h3>
                    <div className="flex shrink-0 items-center gap-2">
                      {metadata?.pageNumber && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 gap-1 rounded-full border border-gray-200 bg-muted/70 px-2 hover:bg-gray-200"
                          onClick={() => onDocumentSelect?.(result)}
                        >
                          <span className="text-xs">Page {metadata.pageNumber}</span>
                          <MapPin className="h-3 w-3" />
                        </Button>
                      )}
                      {result.similarity && (
                        <Badge variant="outline" className="shrink-0">
                          {(result.similarity * 100).toFixed(1)}% match
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="mt-1 max-h-24 overflow-y-auto text-sm text-muted-foreground">
                    {highlightText(cleanedContent, actualSearchQuery)}
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Bottom Pagination Controls */}
      {totalCount && totalCount > pageSize && (
        <div className="mt-6 flex items-center justify-between border-t pb-6 pr-4 pt-4">
          <div className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages} ({totalCount} total results)
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
