'use client';

import { cn } from '@/lib/utils';
import { useMemo } from 'react';

type HighlightedContentProps = {
  content: string;
  highlight?: string;
  className?: string;
};

export function HighlightedContent({ content, highlight, className }: HighlightedContentProps) {
  const highlightedContent = useMemo(() => {
    if (!highlight || highlight.trim() === '') {
      return content;
    }

    const regex = new RegExp(`(${highlight})`, 'gi');
    const parts = content.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 dark:bg-yellow-900">
          {part}
        </span>
      ) : (
        part
      )
    );
  }, [content, highlight]);

  return (
    <div className={cn('whitespace-pre-wrap text-sm text-foreground', className)}>
      {highlightedContent}
    </div>
  );
}
