'use client';

import { DocumentViewer } from '@/components/documents/document-viewer';
import { SearchResults } from '@/components/search/search-results';
import { SearchSettings } from '@/components/search/search-settings';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { useSearchStore } from '@/stores/search';
import { DocumentMetadata } from '@/types/chat';
import type { MaterialSearchResult } from '@/types/material';
import { FinderMode } from '@/types/search';
import { Search, Settings2, X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FormEvent, useEffect, useState } from 'react';

type SearchResultsWithCount = {
  results: MaterialSearchResult[];
  totalCount: number;
};

type SearchPageProps = {
  initialQuery: string;
  finderMode: FinderMode;
  searchResults: SearchResultsWithCount | null;
  materialAccessUrl: string;
};

export default function SearchPage({
  initialQuery,
  finderMode = 'fulltext',
  searchResults,
  materialAccessUrl,
}: SearchPageProps) {
  const [query, setQuery] = useState(initialQuery);
  const [currentFinderMode, setCurrentFinderMode] = useState<FinderMode>(finderMode);
  const [selectedDocument, setSelectedDocument] = useState<MaterialSearchResult | null>(null);
  const [showSettings, setShowSettings] = useState(!searchResults);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { config: searchConfig, setConfig: setSearchConfig } = useSearchStore();

  // Get the actual search query from URL params
  const actualSearchQuery = searchParams.get('q') || '';

  // Initialize space IDs and document IDs from URL on mount
  useEffect(() => {
    const spaceIdsParam = searchParams.get('spaceIds');
    const documentIdsParam = searchParams.get('documentIds');

    if (spaceIdsParam) {
      const spaceIds = spaceIdsParam.split(',');
      setSearchConfig({ spaceIds, filterType: 'space' });
    }

    if (documentIdsParam) {
      const documentIds = documentIdsParam.split(',');
      setSearchConfig({ documentIds, filterType: 'document' });
    }
  }, [searchParams, setSearchConfig]);

  const handleSearch = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!query.trim()) {
      return;
    }

    // Build the URL with all parameters
    const params = new URLSearchParams({
      q: query,
      mode: 'finder',
      finderMode: currentFinderMode,
      pageSize: '20', // Default to 20 results per page
    });

    // Handle space and document filter parameters
    // We need to distinguish between "no filter applied" vs "filter applied but empty"
    // Based on the current filterType, we know which filter the user is actively using

    if (searchConfig.filterType === 'space') {
      // User is using space filter - send spaceIds parameter (even if empty)
      if ((searchConfig.spaceIds?.length ?? 0) > 0) {
        params.set('spaceIds', searchConfig.spaceIds!.join(','));
      } else {
        // User selected space filter but no spaces - send empty to indicate "no results wanted"
        params.set('spaceIds', '');
      }
    } else if (searchConfig.filterType === 'document') {
      // User is using document filter - send documentIds parameter (even if empty)
      if ((searchConfig.documentIds?.length ?? 0) > 0) {
        params.set('documentIds', searchConfig.documentIds!.join(','));
      } else {
        // User selected document filter but no documents - send empty to indicate "no results wanted"
        params.set('documentIds', '');
      }
    }
    // If filterType is neither 'space' nor 'document', don't send any filter parameters
    // This will default to searching all accessible content

    const url = `/search?${params.toString()}`;
    router.push(url);
    setShowSettings(false); // Hide the settings panel when search is performed
  };

  const hasSearchResults = searchResults !== null;

  return (
    <div className="mx-auto flex h-[calc(100vh-4.1rem)] max-w-screen-2xl">
      <div
        className={cn(
          'flex w-full min-w-0 flex-1 flex-col overflow-x-hidden p-4 pb-0',
          selectedDocument || (!hasSearchResults && showSettings) ? 'pr-0' : undefined
        )}
      >
        <div className="flex gap-1">
          <form onSubmit={handleSearch} className="flex flex-1 gap-2 pr-4">
            <Input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search through your documents..."
              className="flex-grow"
            />
            <Button type="submit" variant="default" disabled={!query.trim()}>
              <Search className="mr-2 h-4 w-4" />
              Search Content
            </Button>
            {hasSearchResults ? (
              <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="icon" className="shrink-0">
                    <Settings2 className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="end" className="w-80 border-none p-0 shadow-none">
                  <SearchSettings
                    finderMode={currentFinderMode}
                    onFinderModeChange={setCurrentFinderMode}
                  />
                </PopoverContent>
              </Popover>
            ) : (
              <Button
                type="button"
                variant="outline"
                size="icon"
                className="shrink-0"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings2 className="h-4 w-4" />
              </Button>
            )}
          </form>
        </div>

        <div className="mt-4 flex-1 overflow-y-auto">
          {searchResults && (
            <SearchResults
              results={searchResults.results}
              totalCount={searchResults.totalCount}
              actualSearchQuery={actualSearchQuery}
              onDocumentSelect={setSelectedDocument}
              className={
                selectedDocument || (!hasSearchResults && showSettings) ? 'border-r' : undefined
              }
            />
          )}
        </div>
      </div>

      {selectedDocument ? (
        <div className="flex h-full w-1/2 border-l bg-white">
          <div className="relative flex-1">
            <DocumentViewer
              url={`${materialAccessUrl}/${selectedDocument.key}`}
              fileType={selectedDocument.fileType}
              options={{
                initialPage: (selectedDocument.metadata as DocumentMetadata)?.pageNumber
                  ? Number((selectedDocument.metadata as DocumentMetadata).pageNumber)
                  : undefined,
              }}
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute bottom-4 right-4 rounded-full bg-black/80 text-white hover:bg-black/60"
              onClick={() => setSelectedDocument(null)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : !hasSearchResults && showSettings ? (
        <div className="flex h-full w-1/2 border-l bg-white">
          <div className="w-full p-4">
            <SearchSettings
              finderMode={currentFinderMode}
              onFinderModeChange={setCurrentFinderMode}
            />
          </div>
        </div>
      ) : null}
    </div>
  );
}
