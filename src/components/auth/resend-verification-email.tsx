'use client';

import { Button } from '@/components/ui/button';
import { clientApi } from '@/lib/trpc/client-api';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useState } from 'react';

type ResendVerificationEmailProps = {
  email: string;
};

export function ResendVerificationEmail({ email }: ResendVerificationEmailProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [cooldown, setCooldown] = useState(0);

  const resendVerification = clientApi.auth.resendVerification.useMutation({
    onSuccess: (data) => {
      toast.success(data.message || 'Verification email sent');

      // Set a 60-second cooldown
      setCooldown(60);
      const interval = setInterval(() => {
        setCooldown((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to resend verification email');
    },
  });

  async function handleResend() {
    if (cooldown > 0 || isLoading || resendVerification.isPending) return;

    setIsLoading(true);

    try {
      await resendVerification.mutateAsync({ email });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="mt-6 text-center">
      <p className="mb-2 text-sm text-muted-foreground">
        Didn&apos;t receive the verification email?
      </p>
      <Button
        variant="outline"
        onClick={handleResend}
        disabled={isLoading || cooldown > 0 || resendVerification.isPending}
        size="sm"
      >
        {isLoading || resendVerification.isPending ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Sending...
          </>
        ) : cooldown > 0 ? (
          `Resend in ${cooldown}s`
        ) : (
          'Resend Verification Email'
        )}
      </Button>
    </div>
  );
}
