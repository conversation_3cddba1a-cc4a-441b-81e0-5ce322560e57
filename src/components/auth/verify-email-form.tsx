'use client';

import { Button } from '@/components/ui/button';
import { SITE_PATHS } from '@/configs/site';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

type VerifyEmailFormProps = {
  token: string;
};

export function VerifyEmailForm({ token }: VerifyEmailFormProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function verifyEmail() {
      try {
        const response = await fetch('/api/auth/verify-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();
        console.log('data', data);

        if (!response.ok) {
          throw new Error(data.message || 'Failed to verify email');
        }

        setIsSuccess(true);
        toast.success('Email verified successfully');

        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push(SITE_PATHS.AUTH.SIGN_IN);
        }, 3000);
      } catch (error) {
        const message = error instanceof Error ? error.message : 'Something went wrong';
        setError(message);
        toast.error(message);
      } finally {
        setIsLoading(false);
      }
    }

    verifyEmail();
  }, [token, router]);

  return (
    <div className="rounded-lg border p-6 shadow-sm">
      {isLoading && (
        <div className="flex flex-col items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="mt-4 text-center">Verifying your email address...</p>
        </div>
      )}

      {isSuccess && (
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-green-100 p-2">
            <svg
              className="h-8 w-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-xl font-medium">Email Verified!</h3>
          <p className="mb-4 text-muted-foreground">Your email has been successfully verified.</p>
          <p className="text-sm text-muted-foreground">Redirecting to login page...</p>
        </div>
      )}

      {error && (
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 p-2">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-xl font-medium">Verification Failed</h3>
          <p className="mb-4 text-muted-foreground">{error}</p>
          <Button onClick={() => router.push(SITE_PATHS.AUTH.SIGN_IN)}>Go to Sign In</Button>
        </div>
      )}
    </div>
  );
}
