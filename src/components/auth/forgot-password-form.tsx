'use client';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { clientApi } from '@/lib/trpc/client-api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Mail } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useState } from 'react';

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email'),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export function ForgotPasswordForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submittedEmail, setSubmittedEmail] = useState('');

  const forgotPassword = clientApi.auth.forgotPassword.useMutation({
    onSuccess: (data) => {
      setIsSubmitted(true);
      toast.success(data.message || 'Password reset email sent');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to process request');
    },
  });

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(values: ForgotPasswordFormValues) {
    setIsLoading(true);

    try {
      await forgotPassword.mutateAsync({ email: values.email });
      setSubmittedEmail(values.email);
    } finally {
      setIsLoading(false);
    }
  }

  if (isSubmitted) {
    return (
      <div className="rounded-lg border p-6 shadow-sm">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-blue-100 p-2">
            <Mail className="h-8 w-8 text-blue-600" />
          </div>
          <h3 className="mb-2 text-xl font-medium">Check Your Email</h3>
          <p className="text-muted-foreground">
            We&apos;ve sent a password reset link to <strong>{submittedEmail}</strong>.
          </p>
          <p className="mt-2 text-sm text-muted-foreground">
            Please check your email and follow the instructions to reset your password.
          </p>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter your email"
                  {...field}
                  disabled={isLoading || forgotPassword.isPending}
                  type="email"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isLoading || forgotPassword.isPending}>
          {isLoading || forgotPassword.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending reset instructions...
            </>
          ) : (
            'Send Reset Instructions'
          )}
        </Button>
      </form>
    </Form>
  );
}
