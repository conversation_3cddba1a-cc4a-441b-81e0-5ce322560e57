'use client';

import { Button } from '@/components/ui/button';
import { SITE_PATHS } from '@/configs/site';
import { useUserStore } from '@/stores/user';
import { CheckCircle2 } from 'lucide-react';
import { useEffect } from 'react';

type EmailVerificationSuccessProps = {
  email: string;
};

export function EmailVerificationSuccess({ email }: EmailVerificationSuccessProps) {
  const setUserVerified = useUserStore((state) => state.setEmailVerified);

  // First, update the user store to mark email as verified
  useEffect(() => {
    setUserVerified(true);
  }, [setUserVerified]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="mb-8 flex flex-col items-center justify-center text-center">
        <h1 className="mb-2 text-3xl font-bold">Email Verified</h1>
        <p className="text-muted-foreground">Your email has been successfully verified</p>
      </div>
      <div className="w-full max-w-md">
        <div className="rounded-lg border border-green-200 p-6 shadow-sm">
          <div className="flex flex-col items-center py-4">
            <div className="mb-4 rounded-full bg-green-100 p-3">
              <CheckCircle2 className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="mb-2 text-xl font-medium">Verification Successful</h3>
            <p className="mb-4 text-center text-muted-foreground">
              Your email address <strong>{email}</strong> has been verified. You can now access all
              features of the platform.
            </p>
            <div className="mb-4 mt-2 text-center text-sm">
              <p>You will be redirected to dashboard automatically in a few seconds...</p>
            </div>
            <div className="mt-4">
              <Button
                className="w-full"
                onClick={() => {
                  window.location.href = SITE_PATHS.HOME;
                }}
              >
                Go to Dashboard Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
