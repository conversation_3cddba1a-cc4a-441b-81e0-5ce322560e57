'use client';

import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';

type DocsViewerProps = {
  url: string;
};

export function DocsViewer({ url }: DocsViewerProps) {
  const documents = [{ uri: url }];

  return (
    <div className="h-[calc(100vh-4.1rem)] overflow-y-hidden bg-gray-100">
      <DocViewer
        documents={documents}
        pluginRenderers={DocViewerRenderers}
        prefetchMethod="GET"
        config={{
          header: {
            disableHeader: true,
            disableFileName: true,
          },
        }}
        // className="h-full"
      />
    </div>
  );
}
