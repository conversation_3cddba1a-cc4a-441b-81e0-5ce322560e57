'use client';

import { useDocumentHighlights } from '@/hooks/use-document-highlights';
import { useCallback } from 'react';
import { EnhancedPDFViewer, type OCRHighlight } from './enhanced-pdf-viewer';

interface DocumentViewerEnhancedProps {
  documentId: string;
  documentUrl: string;
  fileName: string;
  onTextReference?: (text: string, context?: Record<string, unknown>) => void;
  className?: string;
  selectedHighlightChunkId?: number | null;
  onWhitespaceClick?: () => void;
  onHighlightSelect?: (chunkId: number | null) => void;
}

export function DocumentViewerEnhanced({
  documentId,
  documentUrl,
  // fileName,
  // onTextReference,
  className,
  selectedHighlightChunkId,
  onWhitespaceClick,
  onHighlightSelect,
}: DocumentViewerEnhancedProps) {
  const { highlights } = useDocumentHighlights(documentId);

  console.log('🟢 DocumentViewerEnhanced props:', {
    documentId,
    selectedHighlightChunkId,
    highlightsCount: highlights.length,
    highlightIds: highlights.map((h) => ({
      id: h.id,
      chunkId: h.chunkId,
      pageIndex: h.pageIndex,
      text: h.text.substring(0, 30) + '...',
    })),
  });

  // Find the selected highlight and determine its page
  const selectedHighlight = selectedHighlightChunkId
    ? highlights.find(
        (h) =>
          h.chunkId &&
          (typeof h.chunkId === 'string'
            ? parseInt(h.chunkId) === selectedHighlightChunkId
            : h.chunkId === selectedHighlightChunkId)
      )
    : null;

  console.log(
    '🟢 Selected highlight found:',
    selectedHighlight
      ? {
          id: selectedHighlight.id,
          chunkId: selectedHighlight.chunkId,
          pageIndex: selectedHighlight.pageIndex,
          text: selectedHighlight.text.substring(0, 50) + '...',
        }
      : 'none'
  );

  const handleHighlightClick = useCallback(
    (highlight: OCRHighlight) => {
      console.log('🟢 Highlight clicked:', {
        chunkId: highlight.chunkId,
        text: highlight.text.substring(0, 100) + '...',
        confidence: highlight.confidence,
        layoutType: highlight.layoutType,
      });

      // Convert chunkId to number for selection
      const chunkId =
        typeof highlight.chunkId === 'string' ? parseInt(highlight.chunkId) : highlight.chunkId;

      if (chunkId && onHighlightSelect) {
        // Check if this highlight is already selected - if so, deselect it
        const isCurrentlySelected = selectedHighlightChunkId === chunkId;
        console.log('🟢 Highlight selection:', {
          chunkId,
          currentlySelected: selectedHighlightChunkId,
          isCurrentlySelected,
          action: isCurrentlySelected ? 'DESELECT' : 'SELECT',
        });

        if (isCurrentlySelected) {
          // Deselect by setting to null
          onHighlightSelect(null); // Deselect by passing null
        } else {
          // Select the new highlight
          onHighlightSelect(chunkId);
        }
      }
    },
    [onHighlightSelect, selectedHighlightChunkId]
  );

  const handleHighlightHover = useCallback(() => {
    // Could show preview tooltip here
  }, []);

  return (
    <div className={`h-full bg-gray-50 ${className}`}>
      {/* PDF Viewer */}
      <div className="relative h-full">
        {/* Enhanced PDF Viewer */}
        <EnhancedPDFViewer
          url={documentUrl}
          documentId={documentId}
          highlights={highlights}
          onHighlightClick={handleHighlightClick}
          onHighlightHover={handleHighlightHover}
          enableInteractions={true}
          options={{
            selectedHighlightChunkId: selectedHighlightChunkId,
            onWhitespaceClick: onWhitespaceClick,
            // Jump to the page where the selected highlight is located
            initialPage: selectedHighlight ? selectedHighlight.pageIndex + 1 : undefined, // Convert to 1-based
            timestamp: selectedHighlight ? Date.now() : undefined, // Force navigation when highlight changes
          }}
        />
      </div>
    </div>
  );
}
