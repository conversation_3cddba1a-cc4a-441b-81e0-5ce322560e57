'use client';

import { SidebarTrigger } from '@/components/ui/sidebar';
import { SITE_PATHS } from '@/configs/site';
import { cn } from '@/lib/utils';
import { User } from 'lucia';
import { Sparkles } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

type NavItemProps = {
  href: string;
  children: React.ReactNode;
};

function NavItem({ href, children }: NavItemProps) {
  const pathname = usePathname();
  return (
    <Link
      href={href}
      className={cn(
        'text-base font-medium transition-colors hover:text-primary',
        pathname === href ? 'text-primary' : 'text-muted-foreground'
      )}
    >
      {children}
    </Link>
  );
}

type MainNavProps = React.ComponentProps<'div'> & {
  user: User | null;
};

export function MainNav({ className, user, ...props }: MainNavProps) {
  return (
    <div className={cn('border-b', className)} {...props}>
      <div className="flex h-16 w-full items-center gap-4 px-4">
        <div>
          {user && <SidebarTrigger className="mr-2" />}
          <Link href={SITE_PATHS.HOME}>
            <span className="text-lg font-bold md:text-xl">Knowledge Sphere</span>
          </Link>
        </div>
        <nav className="flex items-center space-x-4 lg:space-x-6">
          <NavItem href={SITE_PATHS.SPACES}>Spaces</NavItem>
          <NavItem href={SITE_PATHS.MATERIALS}>Materials</NavItem>
          <NavItem href={SITE_PATHS.SEARCH}>Search</NavItem>
          <NavItem href={SITE_PATHS.CHAT}>
            <span className="flex items-center gap-1.5">
              Chat
              <Sparkles className="h-4 w-4 text-blue-500" />
            </span>
          </NavItem>
          {/* <NavItem href={SITE_PATHS.SETTINGS}>Settings</NavItem> */}
        </nav>
        {user ? (
          <div></div>
        ) : (
          <div className="ml-auto flex items-center space-x-4">
            <NavItem href={SITE_PATHS.AUTH.SIGN_IN}>Sign In</NavItem>
            <NavItem href={SITE_PATHS.AUTH.SIGN_UP}>Sign Up</NavItem>
          </div>
        )}
      </div>
    </div>
  );
}
