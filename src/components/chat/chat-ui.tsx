'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import type { TopicWithDocuments } from '@/database/repository/chat';
import { useChat } from '@/hooks/use-chat';
import { getDocumentPreviewUrl } from '@/lib/materials/utils';
import { cn } from '@/lib/utils';
import { useReferenceStore } from '@/stores/reference';
import { DocumentType } from '@/types/material';
import { Loader2, Settings2, X } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { useEffect, useMemo, useRef, useState } from 'react';
import { DocumentViewer } from '../documents/document-viewer';
import { ChatSettings } from './chat-settings';
import { References } from './references';

type ChatUIProps = {
  initialTopic: TopicWithDocuments;
};

export function ChatUI({ initialTopic }: ChatUIProps) {
  const { selectedReference, setSelectedReference } = useReferenceStore();
  const [message, setMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const { messages, isLoading, error, sendMessage } = useChat(initialTopic);
  const hasTriggeredInitialMessage = useRef(false);

  useEffect(() => {
    if (initialTopic.interactions.length > 1) return;
    if (hasTriggeredInitialMessage.current) return;

    const shouldTriggerAIResponse =
      initialTopic.interactions.length === 1 && initialTopic.interactions[0].senderType === 'user';

    if (shouldTriggerAIResponse) {
      hasTriggeredInitialMessage.current = true;
      sendMessage(initialTopic.interactions[0].content, { isInitialMessage: true });
    }
  }, [initialTopic.interactions, sendMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;

    await sendMessage(message);
    setMessage('');
  };

  const documentViewer = useMemo(
    () => (
      <div
        className={cn(
          'relative h-full w-1/2 overflow-y-auto border-l',
          selectedReference ? 'block' : 'hidden'
        )}
      >
        <DocumentViewer
          url={selectedReference ? getDocumentPreviewUrl(selectedReference.key) : ''}
          fileType={selectedReference?.fileType || DocumentType.PDF}
          options={{
            initialPage: selectedReference?.metadata?.pageNumber
              ? Number(selectedReference.metadata.pageNumber)
              : undefined,
            timestamp: selectedReference?.metadata?.timestamp,
          }}
        />
        <div
          className="absolute bottom-4 right-4 cursor-pointer rounded-full bg-black p-3 text-white opacity-80 shadow-lg hover:bg-gray-700"
          onClick={() => setSelectedReference(null)}
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close document viewer</span>
        </div>
      </div>
    ),
    [selectedReference, setSelectedReference]
  );

  return (
    <div className="flex h-[calc(100vh-4.1rem)] w-full overflow-hidden">
      <div className={cn('flex flex-col', selectedReference ? 'w-1/2' : 'w-full')}>
        <div className="flex-1 overflow-y-auto">
          <div className="space-y-4 p-4">
            {messages.map((msg, i) => (
              <div
                key={i}
                className={`flex ${msg.senderType === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[70%] rounded-lg px-4 py-2 shadow transition-all duration-300 ${
                    msg.senderType === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-100'
                  } ${msg.senderType === 'ai' ? 'prose prose-sm max-w-[80%] py-0' : ''}`}
                >
                  {msg.senderType === 'ai' ? (
                    <div className="[&_li]:my-2 [&_ol]:my-2 [&_p+ol]:mt-2 [&_p+ul]:mt-2 [&_p]:my-2 [&_ul]:my-2">
                      <ReactMarkdown>{msg.content}</ReactMarkdown>
                    </div>
                  ) : (
                    msg.content
                  )}
                  {msg.senderType === 'ai' && isLoading && i === messages.length - 1 && (
                    <span className="ml-2 animate-pulse">
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </span>
                  )}
                  {msg.senderType === 'ai' && msg.retrievedDocuments && (
                    <References className="mb-2" documents={msg.retrievedDocuments} />
                  )}
                </div>
              </div>
            ))}
            {error && (
              <div className="rounded-lg bg-red-100 p-4 text-red-600">Error: {error.message}</div>
            )}
          </div>
        </div>

        <div className="border-t bg-white p-4">
          <form onSubmit={handleSubmit}>
            <div className="flex items-end gap-2">
              <div className="flex-grow">
                <div className="relative">
                  <Input
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Type your message..."
                    disabled={isLoading}
                    className="pr-10"
                  />
                  <Popover open={showSettings} onOpenChange={setShowSettings}>
                    <PopoverTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-1/2 -translate-y-1/2"
                      >
                        <Settings2 className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80" align="end">
                      <ChatSettings />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <Button type="submit" disabled={isLoading}>
                Send
              </Button>
            </div>
          </form>
        </div>
      </div>

      {documentViewer}
    </div>
  );
}
