import { ChatHistoryItem } from '@/components/chat/chat-history-item';
import { SidebarGroupLabel, SidebarMenu } from '@/components/ui/sidebar';
import * as React from 'react';
import { GroupedChats } from '@/app/api/chats/route';

export type ChatHistoryListProps = {
  chats: GroupedChats | undefined;
  isLoading: boolean;
};

export function ChatHistoryList({ chats, isLoading }: ChatHistoryListProps) {
  if (isLoading) {
    return (
      <SidebarMenu>
        <div>
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="space-y-2 p-2">
              <div className="h-5 w-full animate-pulse rounded-md bg-muted" />
              <div className="h-4 w-3/4 animate-pulse rounded-md bg-muted opacity-70" />
            </div>
          ))}
        </div>
      </SidebarMenu>
    );
  }

  const hasChats =
    chats?.today?.length ||
    chats?.yesterday?.length ||
    chats?.previous7Days?.length ||
    chats?.older?.length;

  if (!chats || !hasChats) {
    return (
      <SidebarMenu>
        <div className="flex h-full items-center justify-center p-8 text-center">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">No topics yet</p>
          </div>
        </div>
      </SidebarMenu>
    );
  }

  const chatGroups = [
    { key: 'today', label: 'Today', data: chats.today },
    { key: 'yesterday', label: 'Yesterday', data: chats.yesterday },
    { key: 'previous7Days', label: 'Previous 7 Days', data: chats.previous7Days },
    { key: 'older', label: 'Older', data: chats.older },
  ] as const;

  return (
    <SidebarMenu>
      <div>
        {chatGroups.map(
          ({ key, label, data }) =>
            data?.length > 0 && (
              <React.Fragment key={key}>
                <SidebarGroupLabel className="text-xs font-bold text-gray-900">
                  {label}
                </SidebarGroupLabel>
                <ul className="space-y-1">
                  {data.map((chat) => (
                    <ChatHistoryItem key={chat.id} chat={chat} />
                  ))}
                </ul>
              </React.Fragment>
            )
        )}
      </div>
    </SidebarMenu>
  );
}
