'use client';

import { But<PERSON> } from '@/components/ui/button';
import type { AgentStep } from '@/hooks/use-chat-enhanced';
import type { DocumentSummary } from '@/lib/langgraph/types';
import { cn } from '@/lib/utils';
import { useReferenceStore } from '@/stores/reference';
import type { MaterialSearchResult } from '@/types/material';
import {
  AlertCircle,
  BookOpen,
  Brain,
  ChevronDown,
  ChevronRight,
  FileText,
  Lightbulb,
  Loader2,
  RefreshCw,
  Search,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { SummaryViewer } from './summary-viewer';

interface AgentProgressProps {
  steps: AgentStep[];
  isThinking: boolean;
  currentRunningStep?: {
    step: string;
    message: string;
  } | null;
  className?: string;
  autoExpanded?: boolean;
  onToggleDetails?: () => void;
}

export function AgentProgress({
  steps,
  isThinking,
  currentRunningStep,
  className,
  autoExpanded = false,
  onToggleDetails,
}: AgentProgressProps) {
  const [isExpanded, setIsExpanded] = useState(autoExpanded);
  const [selectedSummary, setSelectedSummary] = useState<DocumentSummary | null>(null);
  const [expandedChunks, setExpandedChunks] = useState<Record<string, boolean>>({});
  const [expandedSummaries, setExpandedSummaries] = useState<Record<string, boolean>>({});
  const { setSelectedReference } = useReferenceStore();

  // Auto-expand logic: expand when AI is working or when there are steps to show
  useEffect(() => {
    if (autoExpanded || isThinking || currentRunningStep || steps.length > 0) {
      setIsExpanded(true);
    }
  }, [autoExpanded, isThinking, currentRunningStep, steps.length]);

  // Handle chunk click - open document viewer with highlights
  const handleChunkClick = (chunk: MaterialSearchResult) => {
    console.log('🟠 Agent-progress chunk click:', chunk);
    const referenceData = {
      id: chunk.id,
      chunkId: chunk.chunkId,
      fileName: chunk.fileName,
      content: chunk.content,
      key: chunk.key,
      similarity: chunk.similarity,
      fileType: chunk.fileType,
      metadata: {
        pageNumber: (chunk.metadata as { pageNumber?: number })?.pageNumber,
        timestamp: Date.now(),
      },
    };
    console.log('🟠 Agent-progress setting reference:', referenceData);
    setSelectedReference(referenceData);
  };

  // Handle summary click - open summary viewer
  const handleSummaryClick = (summary: DocumentSummary) => {
    setSelectedSummary(summary);
  };

  // Simple step processing - maintain chronological order and avoid duplicates
  const getDisplaySteps = () => {
    const displaySteps: Array<{
      id: string;
      title: string;
      status: 'completed' | 'running';
      message?: string;
      data?: AgentStep;
      stepType: string;
    }> = [];

    // Check if we have a current running step that should be displayed
    const hasVisibleRunningStep =
      currentRunningStep &&
      !['create_research_plan', 'resolve_material_context', 'generate_answer'].includes(
        currentRunningStep.step
      );

    // Process completed steps - deduplication is now handled in streaming logic
    const seenStepIds = new Set<string>();

    steps.forEach((step, index) => {
      // Skip hidden internal steps and generate_answer
      if (
        ['create_research_plan', 'resolve_material_context', 'generate_answer'].includes(step.step)
      ) {
        return;
      }

      // Use stepId if available, otherwise fall back to index-based key
      const stepId = step.stepId || `${step.step}-${index}`;

      // Skip if we've already seen this exact step
      if (seenStepIds.has(stepId)) {
        return;
      }
      seenStepIds.add(stepId);

      // If this step type is currently running, don't show the completed version
      if (hasVisibleRunningStep && step.step === currentRunningStep.step) {
        return;
      }

      const title = getStepTitle(step.step);
      if (title) {
        displaySteps.push({
          id: stepId,
          title,
          status: 'completed',
          data: step,
          stepType: step.step,
        });
      }
    });

    // Add current running step if it exists and should be visible
    if (hasVisibleRunningStep) {
      const title = getStepTitle(currentRunningStep.step);
      if (title) {
        displaySteps.push({
          id: `running-current`,
          title,
          status: 'running',
          message: currentRunningStep.message,
          stepType: currentRunningStep.step,
        });
      }
    }

    return displaySteps;
  };

  // Simple title mapping
  const getStepTitle = (stepType: string): string | null => {
    switch (stepType) {
      case 'classify_query':
        return 'Understanding your question';
      case 'retrieve_context':
      case 'retrieve_summary_context':
      case 'retrieve_content':
        return 'Searching for context';
      case 'evaluate_results':
        return 'Evaluating retrieved context';
      case 'refine_query':
        return 'Refining search approach';
      case 'generate_answer':
        return null; // Don't show generate_answer step
      default:
        return null;
    }
  };

  // Get icon for step type
  const getStepIcon = (stepType: string, status: 'completed' | 'running') => {
    if (status === 'running') {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }

    switch (stepType) {
      case 'classify_query':
        return <Brain className="h-4 w-4 text-green-500" />;
      case 'retrieve_context':
      case 'retrieve_summary_context':
      case 'retrieve_content':
        return <Search className="h-4 w-4 text-green-500" />;
      case 'evaluate_results':
        return <AlertCircle className="h-4 w-4 text-green-500" />;
      case 'refine_query':
        return <RefreshCw className="h-4 w-4 text-green-500" />;
      case 'generate_answer':
        return <Lightbulb className="h-4 w-4 text-green-500" />;
      default:
        return <FileText className="h-4 w-4 text-green-500" />;
    }
  };

  const displaySteps = getDisplaySteps();
  const currentStepTitle =
    displaySteps.find((s) => s.status === 'running')?.title || 'Processing...';

  // Don't show if no steps and not thinking
  if (displaySteps.length === 0 && !isThinking && !currentRunningStep) {
    return null;
  }

  return (
    <div
      className={cn(
        'bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20',
        'border border-blue-200 dark:border-blue-800',
        'mb-2 rounded-lg p-2',
        'transition-all duration-300 ease-in-out',
        'shadow-md',
        className
      )}
    >
      {/* Header with current step indicator */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Brain className="h-4 w-4 text-blue-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            AI Thinking Process
          </span>
          {currentRunningStep && (
            <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>{currentStepTitle}</span>
            </div>
          )}
        </div>

        {displaySteps.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setIsExpanded(!isExpanded);
              onToggleDetails?.();
            }}
            className="h-8 px-2 text-xs"
          >
            {isExpanded ? (
              <>
                <ChevronDown className="mr-1 h-3 w-3" />
                Hide Details
              </>
            ) : (
              <>
                <ChevronRight className="mr-1 h-3 w-3" />
                Show Details
              </>
            )}
          </Button>
        )}
      </div>

      {/* Expandable Detailed Steps */}
      {isExpanded && (
        <div className="mt-1.5 space-y-2 border-t border-blue-200 pt-2 dark:border-blue-800">
          {/* Grouped steps */}
          {displaySteps.map((step, index) => {
            return (
              <div
                key={step.id}
                className={cn(
                  'rounded-lg border bg-white p-1.5 px-2 dark:border-gray-700 dark:bg-gray-800',
                  'transition-all duration-200',
                  step.status === 'running' && 'ring-1 ring-blue-300 dark:ring-blue-600'
                )}
              >
                {/* Step Header */}
                <div className="mb-1.5 flex items-center space-x-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-xs text-gray-600">
                      {String(index + 1).padStart(2, '0')}
                    </span>
                    {/* Status indicator */}
                    {step.status === 'completed' ? (
                      <div className="flex h-3 w-3 items-center justify-center rounded-full bg-green-500">
                        <div className="h-1.5 w-1.5 rounded-full bg-white"></div>
                      </div>
                    ) : (
                      getStepIcon(step.stepType, step.status)
                    )}
                  </div>
                  <div className="flex-1">
                    <span className="text-sm font-medium">{step.title}</span>
                    {step.status === 'running' && step.message && (
                      <div className="text-xs text-blue-600 dark:text-blue-400">{step.message}</div>
                    )}
                  </div>
                </div>

                {/* Step Details */}
                <div className="ml-5 space-y-2 text-sm">
                  {/* For classify: Show reasoning */}
                  {step.stepType === 'classify_query' && step.data?.reasoning && (
                    <div>
                      <p className="flex items-start gap-1 rounded bg-slate-100 p-2 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                        <Lightbulb className="h-3.5 w-3.5 shrink-0 text-yellow-500" />
                        {step.data.reasoning}
                      </p>
                    </div>
                  )}

                  {/* For retrieve: Show keywords, chunks, and summaries */}
                  {(step.stepType === 'retrieve_context' ||
                    step.stepType === 'retrieve_summary_context' ||
                    step.stepType === 'retrieve_content') && (
                    <>
                      {/* Show keywords if available */}
                      {step.data?.extractedKeywords && step.data.extractedKeywords.length > 0 && (
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                              Key terms
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {step.data.extractedKeywords.map((keyword, idx) => (
                              <span
                                key={idx}
                                className="flex items-center gap-1 rounded bg-slate-100 px-2 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                              >
                                <Search className="h-3 w-3 shrink-0 text-slate-400" />
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Show retrieved chunks */}
                      {step.data?.retrievedChunks && step.data.retrievedChunks.length > 0 && (
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <FileText className="h-3 w-3 text-blue-500" />
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              Found {step.data.retrievedChunks.length} relevant document chunks
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {(expandedChunks[step.id]
                              ? step.data.retrievedChunks
                              : step.data.retrievedChunks.slice(0, 3)
                            ).map((chunk, idx) => (
                              <div
                                key={idx}
                                className="flex cursor-pointer items-center gap-1 rounded-md border border-slate-300 bg-slate-100 px-2 py-1 text-xs text-gray-600 transition-colors hover:bg-gray-200 dark:border-slate-700 dark:bg-slate-800 dark:text-gray-400"
                                title={`Click to view ${chunk.fileName}`}
                                onClick={() => handleChunkClick(chunk as MaterialSearchResult)}
                              >
                                <span className="text-xs">📄</span>
                                <span className="truncate">
                                  {chunk.fileName.length > 20
                                    ? chunk.fileName.substring(0, 20) + '...'
                                    : chunk.fileName}
                                </span>
                              </div>
                            ))}
                            {step.data.retrievedChunks.length > 3 && (
                              <button
                                onClick={() =>
                                  setExpandedChunks((prev) => ({
                                    ...prev,
                                    [step.id]: !prev[step.id],
                                  }))
                                }
                                className="text-xs text-blue-600 hover:text-blue-800 hover:underline dark:text-blue-400 dark:hover:text-blue-300"
                              >
                                {expandedChunks[step.id]
                                  ? 'Show less'
                                  : `+${step.data.retrievedChunks.length - 3} more chunks`}
                              </button>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Show retrieved summaries */}
                      {step.data?.retrievedSummaries && step.data.retrievedSummaries.length > 0 && (
                        <div className="mt-2">
                          <div className="mb-1 flex items-center gap-2">
                            <BookOpen className="h-3 w-3 text-indigo-500" />
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              Found {step.data.retrievedSummaries.length} document summaries
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {(expandedSummaries[step.id]
                              ? step.data.retrievedSummaries
                              : step.data.retrievedSummaries.slice(0, 3)
                            ).map((summary, idx) => (
                              <div
                                key={idx}
                                className="flex cursor-pointer items-center gap-1 rounded-md border border-indigo-300 bg-indigo-50 px-2 py-1 text-xs text-indigo-700 transition-colors hover:bg-indigo-100 dark:border-indigo-700 dark:bg-indigo-950 dark:text-indigo-300"
                                title={`Click to view summary of ${summary.fileName}`}
                                onClick={() => handleSummaryClick(summary)}
                              >
                                <BookOpen className="h-3 w-3 shrink-0" />
                                <span className="truncate">
                                  {summary.fileName.length > 20
                                    ? summary.fileName.substring(0, 20) + '...'
                                    : summary.fileName}
                                </span>
                              </div>
                            ))}
                            {step.data.retrievedSummaries.length > 3 && (
                              <button
                                onClick={() =>
                                  setExpandedSummaries((prev) => ({
                                    ...prev,
                                    [step.id]: !prev[step.id],
                                  }))
                                }
                                className="text-xs text-blue-600 hover:text-blue-800 hover:underline dark:text-blue-400 dark:hover:text-blue-300"
                              >
                                {expandedSummaries[step.id]
                                  ? 'Show less'
                                  : `+${step.data.retrievedSummaries.length - 3} more summaries`}
                              </button>
                            )}
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  {/* For evaluate: Show reasoning */}
                  {step.stepType === 'evaluate_results' && step.data?.reasoning && (
                    <div>
                      <p className="flex items-start gap-1 rounded bg-slate-100 p-2 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                        <Lightbulb className="h-3.5 w-3.5 shrink-0 text-blue-500" />
                        {step.data.reasoning}
                      </p>
                    </div>
                  )}

                  {/* For refine: Show reasoning and keywords */}
                  {step.stepType === 'refine_query' && (
                    <>
                      {step.data?.reasoning && (
                        <div>
                          <div className="mb-1 flex items-center gap-2">
                            <RefreshCw className="h-3 w-3 text-orange-500" />
                            <span className="text-xs font-medium">Refinement:</span>
                          </div>
                          <p className="rounded bg-gray-100 p-2 text-xs text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                            {step.data.reasoning}
                          </p>
                        </div>
                      )}
                      {step.data?.extractedKeywords && step.data.extractedKeywords.length > 0 && (
                        <div className="mt-2">
                          <div className="mb-1 flex items-center gap-2">
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                              New key terms
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {step.data.extractedKeywords.map((keyword, idx) => (
                              <span
                                key={idx}
                                className="flex items-center gap-1 rounded bg-orange-100 px-2 py-1 text-xs text-orange-700 dark:bg-orange-900 dark:text-orange-300"
                              >
                                <Search className="h-3 w-3 shrink-0 text-orange-400" />
                                {keyword}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  {/* Error Messages (for any step) */}
                  {step.data?.error && (
                    <div>
                      <div className="mb-1 flex items-center gap-2">
                        <AlertCircle className="h-3 w-3 text-red-500" />
                        <span className="text-xs font-medium text-red-600">Error:</span>
                      </div>
                      <p className="rounded bg-red-50 p-2 text-xs text-red-600 dark:bg-red-950/20">
                        {step.data.error}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {selectedSummary && (
        <SummaryViewer
          isOpen={true}
          summary={selectedSummary}
          onClose={() => setSelectedSummary(null)}
        />
      )}
    </div>
  );
}

export default AgentProgress;
