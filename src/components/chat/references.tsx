import { cn } from '@/lib/utils';
import { useReferenceStore } from '@/stores/reference';
import type { RetrievedDocumentChunkDTO } from '@/types/chat';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

type GroupedDocuments = {
  [key: string]: {
    fileName: string;
    chunks: RetrievedDocumentChunkDTO[];
  };
};

export function References({
  documents,
  className,
}: {
  documents?: RetrievedDocumentChunkDTO[];
  className?: string;
}) {
  const { selectedReference, setSelectedReference } = useReferenceStore();
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Group documents by their source material (using key as unique identifier)
  const groupedDocuments = useMemo(() => {
    return (
      documents?.reduce<GroupedDocuments>((acc, doc) => {
        if (!acc[doc.key]) {
          acc[doc.key] = {
            fileName: doc.fileName,
            chunks: [],
          };
        }
        acc[doc.key].chunks.push(doc);
        return acc;
      }, {}) ?? {}
    );
  }, [documents]);
  // Initialize expanded state for single chunk groups
  useEffect(() => {
    if (!documents?.length) return;

    const initialExpandedState = Object.entries(groupedDocuments).reduce<Record<string, boolean>>(
      (acc, [key, { chunks }]) => {
        acc[key] = chunks.length === 1;
        return acc;
      },
      {}
    );
    setExpandedGroups(initialExpandedState);
  }, [documents, groupedDocuments]);

  if (!documents?.length) return null;

  const toggleGroup = (key: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const getSimilarityColor = (similarity: number) => {
    if (similarity >= 0.8) return 'bg-green-100 text-green-800';
    if (similarity >= 0.5) return 'bg-blue-100 text-blue-800';
    return 'bg-gray-100 text-gray-800';
  };

  const handleReferenceClick = (doc: RetrievedDocumentChunkDTO) => {
    // Always create a new reference object with updated timestamp
    setSelectedReference({
      ...doc,
      metadata: {
        ...doc.metadata,
        timestamp: Date.now(), // Add timestamp to force update
      },
    });
  };

  return (
    <div className="flex flex-col">
      <div className={cn('mt-2 text-sm', className)}>
        <div className="font-medium text-gray-500">References:</div>
        <div className="mt-1 space-y-1">
          {Object.entries(groupedDocuments).map(([key, { fileName, chunks }]) => (
            <div key={key} className="rounded border border-gray-300">
              <div
                className="flex cursor-pointer items-center justify-between bg-gray-50 p-2 hover:bg-gray-200"
                onClick={() => toggleGroup(key)}
              >
                <div className="flex items-center gap-1">
                  {expandedGroups[key] ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  <span className="text-xs text-gray-500">{fileName}</span>
                  <span className="text-xs text-gray-400">({chunks.length} chunks)</span>
                </div>
              </div>
              {expandedGroups[key] && (
                <div className="divide-y divide-gray-100">
                  {chunks.map((doc, i) => (
                    <div
                      key={`${doc.id}-${i}-${doc.similarity}`}
                      className={cn(
                        'cursor-pointer p-2 transition-colors hover:bg-slate-200',
                        selectedReference?.chunkId === doc.chunkId ? 'bg-slate-200' : 'bg-gray-50'
                      )}
                      onClick={() => handleReferenceClick(doc)}
                    >
                      <div className="flex w-full flex-1 items-center gap-4">
                        <div className="flex aspect-square min-w-5 shrink-0 items-center justify-center rounded-full bg-blue-300 p-0.5 text-xs text-white">
                          {i + 1}
                        </div>
                        {doc.content && (
                          <div className="mt-2 line-clamp-2 flex-1 text-xs text-gray-600">
                            {doc.content}
                          </div>
                        )}
                        {doc.similarity && (
                          <div className="flex flex-col items-center">
                            <span
                              className={cn(
                                'shrink-0 rounded px-2 py-0.5 text-xs font-medium',
                                getSimilarityColor(doc.similarity)
                              )}
                            >
                              {(doc.similarity * 100).toFixed(0)}%
                            </span>
                            {doc.metadata?.pageNumber && (
                              <span className="mt-1 shrink-0 whitespace-nowrap rounded bg-gray-200 px-2 py-0.5 text-xs text-gray-500">
                                pg. {doc.metadata.pageNumber}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
