'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { clientApi } from '@/lib/trpc/client-api';
import { cn } from '@/lib/utils';
import { useChatStore } from '@/stores/chat';
import { useMutation } from '@tanstack/react-query';
import { FileText, FolderOpen, Send, Settings2, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { FormEvent, useState } from 'react';
import { ChatSettings } from './chat-settings';

export default function ChatPage() {
  const [query, setQuery] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const router = useRouter();
  const { config: chatConfig } = useChatStore();

  // Fetch spaces and materials data for badges
  const { data: spaces } = clientApi.space.list.useQuery(undefined, {
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const { data: materials } = clientApi.material.list.useQuery(undefined, {
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  // Get selected spaces and materials for badges
  const selectedSpaces = spaces?.filter((space) => chatConfig.spaceIds?.includes(space.id)) ?? [];
  const selectedMaterials =
    materials?.filter((material) => chatConfig.documentIds?.includes(material.id)) ?? [];

  // Helper function to truncate text in the middle
  const truncateMiddle = (text: string, maxLength: number = 20): string => {
    if (text.length <= maxLength) return text;
    const start = Math.ceil((maxLength - 3) / 2);
    const end = Math.floor((maxLength - 3) / 2);
    return `${text.slice(0, start)}...${text.slice(-end)}`;
  };

  // Helper functions to remove items from selection
  const removeSpace = (spaceId: string) => {
    const { setConfig } = useChatStore.getState();
    setConfig({
      spaceIds: chatConfig.spaceIds?.filter((id) => id !== spaceId) ?? [],
    });
  };

  const removeMaterial = (materialId: string) => {
    const { setConfig } = useChatStore.getState();
    setConfig({
      documentIds: chatConfig.documentIds?.filter((id) => id !== materialId) ?? [],
    });
  };

  const createChatMutation = useMutation<string, Error, string>({
    mutationFn: async (initialQuery: string) => {
      const response = await fetch('/api/chat/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          initialQuery,
          config: {
            ...chatConfig,
            // Default to semantic search for chat
            searchMode: 'semantic',
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create chat session');
      }

      const { chatId } = await response.json();
      return chatId;
    },
  });

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!query.trim()) {
      return;
    }

    try {
      const chatId = await createChatMutation.mutateAsync(query);
      router.push(`/chat/${chatId}/enhanced`);
    } catch (error) {
      console.error('Failed to create chat session:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!query.trim() || createChatMutation.isPending) return;
      handleSubmit(e as unknown as FormEvent<HTMLFormElement>);
    }
  };

  return (
    <div className="flex h-[calc(100vh-4.1rem)] w-full items-center justify-center bg-white dark:bg-gray-900">
      <div className="w-full max-w-4xl space-y-12 p-6">
        {/* Brand Header */}
        <div className="text-center">
          <div className="flex items-center justify-center gap-3">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
              Knowledge Sphere
            </h1>
          </div>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-400">
            Ask questions about your documents and get AI-powered answers
          </p>
        </div>

        {/* Modern Chat Input Form - Same style as chat-ui-enhanced.tsx */}
        <form onSubmit={handleSubmit}>
          <div className="relative rounded-3xl border border-gray-200 bg-gray-50 shadow-sm transition-all duration-200 focus-within:border-gray-300 focus-within:shadow-md dark:border-gray-700 dark:bg-gray-800">
            <div className="flex items-end gap-2 p-3">
              {/* Textarea with CSS auto-resize */}
              <div className="flex-1">
                {/* Selected Items Badges */}
                {(selectedSpaces.length > 0 || selectedMaterials.length > 0) && (
                  <TooltipProvider delayDuration={100}>
                    <div className="mb-2 flex flex-wrap gap-1">
                      {/* Space badges */}
                      {selectedSpaces.map((space) => (
                        <Badge
                          key={space.id}
                          variant="secondary"
                          className="flex items-center gap-1 border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                        >
                          <FolderOpen className="h-3 w-3 flex-shrink-0" />
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-default truncate">
                                {truncateMiddle(space.name)}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-50 text-xs text-gray-900 dark:bg-gray-900 dark:text-gray-100">
                              <p>Space: {space.name}</p>
                            </TooltipContent>
                          </Tooltip>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              removeSpace(space.id);
                            }}
                            className="ml-1 flex-shrink-0 rounded-full p-0.5 transition-colors hover:bg-blue-200 dark:hover:bg-blue-800"
                            aria-label={`Remove ${space.name}`}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                      {/* Material badges */}
                      {selectedMaterials.map((material) => (
                        <Badge
                          key={material.id}
                          variant="secondary"
                          className="flex items-center gap-1 border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                        >
                          <FileText className="h-3 w-3 flex-shrink-0" />
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="cursor-default truncate">
                                {truncateMiddle(material.fileName)}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-50 text-xs text-gray-900 dark:bg-gray-900 dark:text-gray-100">
                              <p>Document: {material.fileName}</p>
                            </TooltipContent>
                          </Tooltip>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              removeMaterial(material.id);
                            }}
                            className="ml-1 flex-shrink-0 rounded-full p-0.5 transition-colors hover:bg-blue-200 dark:hover:bg-blue-800"
                            aria-label={`Remove ${material.fileName}`}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                  </TooltipProvider>
                )}

                <Textarea
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={
                    createChatMutation.isPending
                      ? 'Starting chat...'
                      : 'Ask me anything about your documents...'
                  }
                  disabled={createChatMutation.isPending}
                  className={cn(
                    'max-h-40 min-h-[40px] w-full resize-none border-0 bg-transparent px-3 py-2 text-sm leading-6',
                    'placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0',
                    'dark:placeholder:text-gray-400',
                    createChatMutation.isPending && 'cursor-not-allowed opacity-50'
                  )}
                  rows={1}
                  style={{
                    minHeight: '40px',
                    maxHeight: '160px', // 4 lines
                    overflowY: 'auto',
                    resize: 'none',
                  }}
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = 'auto';
                    target.style.height = `${Math.min(target.scrollHeight, 160)}px`;
                  }}
                />

                {/* Documents option positioned under textarea */}
                <div className="mt-2 flex items-center gap-2">
                  {/* Documents Label and Settings */}
                  <Popover open={showSettings} onOpenChange={setShowSettings}>
                    <PopoverTrigger asChild>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-7 rounded-full px-2 text-xs text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                        disabled={createChatMutation.isPending}
                      >
                        <Settings2 className="mr-1 h-3 w-3" />
                        Documents
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80" align="start">
                      <ChatSettings />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Send Button */}
              <Button
                type="submit"
                disabled={createChatMutation.isPending || !query.trim()}
                size="icon"
                className={cn(
                  'h-8 w-8 shrink-0 rounded-lg transition-all duration-200',
                  'bg-blue-500 hover:bg-blue-600 dark:bg-blue-100 dark:hover:bg-blue-200',
                  'disabled:cursor-not-allowed disabled:bg-gray-300 dark:disabled:bg-gray-600',
                  query.trim() && !createChatMutation.isPending && 'shadow-sm hover:shadow-md'
                )}
              >
                {createChatMutation.isPending ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent dark:border-gray-900 dark:border-t-transparent" />
                ) : (
                  <Send className="h-4 w-4 text-white dark:text-gray-900" />
                )}
              </Button>
            </div>
          </div>

          {/* Helper text */}
          <div className="mt-2 text-center">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Press Shift+Enter for new line • Enter to start chat
            </span>
          </div>
        </form>
      </div>
    </div>
  );
}
