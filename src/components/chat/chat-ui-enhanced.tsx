'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import type { TopicWithDocuments } from '@/database/repository/chat';
import { useChatEnhanced } from '@/hooks/use-chat-enhanced';
import { clientApi } from '@/lib/trpc/client-api';
import { cn } from '@/lib/utils';
import { useChatStore } from '@/stores/chat';
import { useReferenceStore } from '@/stores/reference';
import type { RetrievedDocumentChunkDTO } from '@/types/chat';
import { DocumentType } from '@/types/material';
import { Bot, FileText, FolderOpen, Send, Settings2, X } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { DocumentViewer } from '../documents/document-viewer';
import { AgentProgress } from './agent-progress';
import { ChatSettings } from './chat-settings';

type ChatUIEnhancedProps = {
  initialTopic: TopicWithDocuments;
  materialAccessUrl: string;
};

// Utility function to format timestamps
const formatTimestamp = (date: Date) => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;

  // For older messages, show date and time
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export function ChatUIEnhanced({ initialTopic, materialAccessUrl }: ChatUIEnhancedProps) {
  const { selectedReference, setSelectedReference } = useReferenceStore();
  const { config } = useChatStore();
  const [message, setMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true); // Track if user is at bottom
  const [selectedHighlightChunkId, setSelectedHighlightChunkId] = useState<number | null>(null);

  // Debug log for state changes
  useEffect(() => {
    console.log('🟣 selectedHighlightChunkId state changed to:', selectedHighlightChunkId);
  }, [selectedHighlightChunkId]);

  // Watch for reference changes and set initial selected highlight
  useEffect(() => {
    if (selectedReference?.chunkId) {
      console.log(
        '🟣 Reference changed, setting selectedHighlightChunkId to:',
        selectedReference.chunkId
      );
      setSelectedHighlightChunkId(selectedReference.chunkId);
    } else if (selectedReference === null) {
      console.log('🟣 Reference cleared, setting selectedHighlightChunkId to null');
      setSelectedHighlightChunkId(null);
    }
  }, [selectedReference]);

  const { messages, error, sendMessage, isThinking, isAgentWorking, currentRunningStep } =
    useChatEnhanced(initialTopic);

  // Fetch spaces and materials data for badges
  const { data: spaces } = clientApi.space.list.useQuery(undefined, {
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const { data: materials } = clientApi.material.list.useQuery(undefined, {
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  // Get selected spaces and materials for badges
  const selectedSpaces = spaces?.filter((space) => config.spaceIds?.includes(space.id)) ?? [];
  const selectedMaterials =
    materials?.filter((material) => config.documentIds?.includes(material.id)) ?? [];

  // Helper function to truncate text in the middle
  const truncateMiddle = (text: string, maxLength: number = 20): string => {
    if (text.length <= maxLength) return text;
    const start = Math.ceil((maxLength - 3) / 2);
    const end = Math.floor((maxLength - 3) / 2);
    return `${text.slice(0, start)}...${text.slice(-end)}`;
  };

  // Helper functions to remove items from selection
  const removeSpace = (spaceId: string) => {
    const { setConfig } = useChatStore.getState();
    setConfig({
      spaceIds: config.spaceIds?.filter((id) => id !== spaceId) ?? [],
    });
  };

  const removeMaterial = (materialId: string) => {
    const { setConfig } = useChatStore.getState();
    setConfig({
      documentIds: config.documentIds?.filter((id) => id !== materialId) ?? [],
    });
  };

  // console.log('agentSteps', agentSteps);

  const hasTriggeredInitialMessage = useRef(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Check if user is at bottom of scroll container
  const checkIfUserAtBottom = () => {
    if (!scrollContainerRef.current) return false;
    const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
    const threshold = 20; // 20px threshold for "near bottom"
    return scrollHeight - scrollTop - clientHeight < threshold;
  };

  // Handle scroll events to track user position
  const handleScroll = () => {
    setIsUserAtBottom(checkIfUserAtBottom());
  };

  // Smart auto scroll - only scroll when user is at bottom
  useEffect(() => {
    if (isUserAtBottom) {
      if (isThinking) {
        // Scroll to progress when agent starts working
        progressRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } else {
        // Scroll to bottom when messages are updated
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [messages, isThinking, isUserAtBottom]);

  // Ensure we're at bottom when new messages start
  useEffect(() => {
    if (isAgentWorking && isUserAtBottom) {
      setIsUserAtBottom(true);
    }
  }, [isAgentWorking, isUserAtBottom]);

  useEffect(() => {
    if (initialTopic.interactions.length > 1) return;
    if (hasTriggeredInitialMessage.current) return;

    const shouldTriggerAIResponse =
      initialTopic.interactions.length === 1 && initialTopic.interactions[0].senderType === 'user';

    if (shouldTriggerAIResponse) {
      hasTriggeredInitialMessage.current = true;
      sendMessage(initialTopic.interactions[0].content, { isInitialMessage: true });
    }
  }, [initialTopic.interactions, sendMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || isAgentWorking) return;

    // Ensure we're at bottom when sending new message
    setIsUserAtBottom(true);
    sendMessage(message);
    setMessage('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!message.trim() || isAgentWorking) return;
      handleSubmit(e);
    }
  };

  // Handle location reference clicks - using same format as agent-progress.tsx
  const handleLocationReferenceClick = useCallback(
    (referenceIndex: number, references: RetrievedDocumentChunkDTO[]) => {
      if (references && references[referenceIndex]) {
        const chunk = references[referenceIndex];
        console.log('🔵 Clicked location reference:', referenceIndex, chunk);
        console.log('🔵 Setting activeHighlightChunkId to:', chunk.chunkId);

        // Use same format as agent-progress.tsx handleChunkClick
        const referenceData = {
          id: chunk.id,
          chunkId: chunk.chunkId,
          fileName: chunk.fileName,
          content: chunk.content,
          key: chunk.key,
          similarity: chunk.similarity,
          fileType: chunk.fileType,
          metadata: {
            pageNumber: chunk.metadata?.pageNumber,
            timestamp: Date.now(),
          },
        };

        console.log('🔵 Setting reference data:', referenceData);
        setSelectedReference(referenceData);

        // Set the active highlight for direct display
        console.log(
          '🔵 About to call setSelectedHighlightChunkId with:',
          chunk.chunkId,
          typeof chunk.chunkId
        );
        setSelectedHighlightChunkId(chunk.chunkId);
        console.log('🔵 Called setSelectedHighlightChunkId');
      }
    },
    [setSelectedReference]
  );

  // Handle PDF viewer whitespace clicks to clear selected highlight
  const handlePDFWhitespaceClick = useCallback(() => {
    console.log('🔵 Whitespace clicked, clearing selected highlight');
    setSelectedHighlightChunkId(null);
  }, []);

  // Handle highlight selection - when user clicks on any highlight (or deselects with null)
  const handleHighlightSelection = useCallback((highlightChunkId: number | null) => {
    console.log('🔵 Highlight selection changed:', highlightChunkId);
    setSelectedHighlightChunkId(highlightChunkId);
  }, []);

  // Custom component for rendering location references as clickable elements
  const LocationReference = useCallback(
    ({ children, references }: { children: string; references: RetrievedDocumentChunkDTO[] }) => {
      // Extract reference number from [1], [2], etc.
      const match = children.match(/\[(\d+)\]/);
      if (!match) return <span>{children}</span>;

      const referenceIndex = parseInt(match[1]) - 1; // Convert to 0-based index

      return (
        <button
          onClick={() => handleLocationReferenceClick(referenceIndex, references)}
          className="inline-flex items-center gap-1 rounded px-1.5 py-0.5 text-xs font-medium text-blue-600 hover:bg-blue-50 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:text-blue-300"
          title={`Jump to reference ${match[1]} in document`}
        >
          {children}
        </button>
      );
    },
    [handleLocationReferenceClick]
  );

  const documentViewer = useMemo(() => {
    // Debug log for document viewer rendering
    console.log('🔴 DocumentViewer rendering with:', {
      selectedReference: selectedReference
        ? {
            id: selectedReference.id,
            chunkId: selectedReference.chunkId,
            fileName: selectedReference.fileName,
          }
        : null,
      selectedHighlightChunkId,
    });

    return (
      <div
        className={cn(
          'relative h-full w-1/2 overflow-y-auto border-l bg-gray-50 dark:bg-gray-900',
          selectedReference ? 'block' : 'hidden'
        )}
      >
        <DocumentViewer
          url={selectedReference ? `${materialAccessUrl}/${selectedReference.key}` : ''}
          fileType={selectedReference?.fileType || DocumentType.PDF}
          options={{
            initialPage: selectedReference?.metadata?.pageNumber
              ? Number(selectedReference.metadata.pageNumber)
              : undefined,
            timestamp: selectedReference?.metadata?.timestamp,
            selectedHighlightChunkId: selectedHighlightChunkId,
            onWhitespaceClick: handlePDFWhitespaceClick,
            onHighlightSelect: handleHighlightSelection,
            // Enable OCR highlights and pass document ID for automatic fetching
            enableOCRHighlights: true,
            documentId: selectedReference?.id,
            fileName: selectedReference?.fileName,
          }}
        />
        <div
          className="absolute bottom-4 right-4 cursor-pointer rounded-full bg-black p-3 text-white opacity-80 shadow-lg transition-all duration-200 hover:bg-gray-700"
          onClick={() => setSelectedReference(null)}
        >
          <X className="h-5 w-5" />
          <span className="sr-only">Close document viewer</span>
        </div>
      </div>
    );
  }, [
    selectedReference,
    setSelectedReference,
    materialAccessUrl,
    selectedHighlightChunkId,
    handlePDFWhitespaceClick,
    handleHighlightSelection,
  ]);

  return (
    <div className="flex h-[calc(100vh-4.1rem)] w-full overflow-hidden bg-white dark:bg-gray-900">
      <div className={cn('flex flex-col', selectedReference ? 'w-1/2' : 'w-full')}>
        {/* Chat Header */}
        {/* <div className="border-b bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm dark:bg-gray-900/80">
          <div className="flex items-center space-x-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg">
              <Bot className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                AI Assistant
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isAgentWorking ? 'Thinking...' : 'Ready to help with your documents'}
              </p>
            </div>
          </div>
        </div> */}

        <div ref={scrollContainerRef} className="flex-1 overflow-y-auto" onScroll={handleScroll}>
          <div className="mx-auto max-w-4xl space-y-6 p-6">
            {messages.map((msg, i) => {
              return (
                <div key={i}>
                  {/* User Message */}
                  {msg.senderType === 'user' && (
                    <div className="flex justify-end">
                      <div className="ml-auto mt-8 max-w-[50%] space-y-1">
                        <div className="rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3 text-white shadow-lg transition-all duration-300 hover:shadow-xl">
                          <div className="whitespace-pre-wrap text-sm leading-relaxed">
                            {msg.content}
                          </div>
                        </div>
                        <div className="flex justify-end">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {msg.createdAt ? formatTimestamp(msg.createdAt) : 'Just now'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* AI Message with Integrated Progress */}
                  {msg.senderType === 'ai' && (
                    <div className="space-y-3">
                      {/* Show Agent Progress Above AI Message */}
                      {((msg.agentSteps && msg.agentSteps.length > 0) ||
                        (i === messages.length - 1 && currentRunningStep)) && (
                        <div className="flex justify-start">
                          <div className="w-full max-w-[90%]">
                            <AgentProgress
                              steps={msg.agentSteps || []}
                              isThinking={
                                msg.isStreamingContent === true ||
                                (i === messages.length - 1 && !!currentRunningStep)
                              }
                              currentRunningStep={
                                i === messages.length - 1 ? currentRunningStep : null
                              }
                              autoExpanded={true} // Always expanded by default
                            />
                          </div>
                        </div>
                      )}

                      {/* AI Message Content */}
                      {msg.content && (
                        <div className="flex justify-start border-b border-gray-200 pb-10">
                          <div className="w-full max-w-[90%] space-y-2">
                            {/* AI Avatar and Header */}
                            <div className="flex items-center space-x-2">
                              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-purple-500 shadow-md">
                                <Bot className="h-4 w-4 text-white" />
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                  AI Assistant
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                  {msg.createdAt ? formatTimestamp(msg.createdAt) : 'Just now'}
                                </span>
                              </div>
                            </div>

                            {/* Message Content */}
                            <div className="prose prose-sm max-w-none rounded-lg px-4 py-3 transition-all duration-300 dark:prose-invert dark:text-gray-100">
                              <div className="[&_li]:my-1 [&_ol]:my-2 [&_p+ol]:mt-2 [&_p+ul]:mt-2 [&_p]:my-2 [&_ul]:my-2">
                                <ReactMarkdown
                                  remarkPlugins={[remarkGfm]}
                                  components={{
                                    // Enhanced markdown rendering with footnote support
                                    p: ({ children }) => (
                                      <p className="mb-2 last:mb-0">{children}</p>
                                    ),
                                    strong: ({ children }) => (
                                      <strong className="font-semibold text-gray-900 dark:text-gray-100">
                                        {children}
                                      </strong>
                                    ),
                                    // Custom text renderer to handle location references
                                    // This will intercept text nodes and replace [1], [2] etc with clickable components
                                    text: ({ children }) => {
                                      if (typeof children === 'string') {
                                        // Check if this text contains location references like [1], [2], etc.
                                        const referencePattern = /\[(\d+)\]/g;
                                        if (referencePattern.test(children)) {
                                          // Use retrievedDocuments from the message for location references
                                          const references = msg.retrievedDocuments || [];

                                          // Split text by reference patterns and render parts
                                          const parts = children.split(/(\[\d+\])/g);
                                          return (
                                            <>
                                              {parts.map((part, index) => {
                                                if (referencePattern.test(part)) {
                                                  return (
                                                    <LocationReference
                                                      key={index}
                                                      references={references}
                                                    >
                                                      {part}
                                                    </LocationReference>
                                                  );
                                                }
                                                return part;
                                              })}
                                            </>
                                          );
                                        }
                                      }
                                      return children;
                                    },
                                  }}
                                >
                                  {msg.content}
                                </ReactMarkdown>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}

            {/* Error Display */}
            {error && (
              <div className="flex justify-start">
                <div className="max-w-[85%] rounded-2xl border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/20">
                  <div className="flex items-center space-x-2">
                    <div className="flex h-6 w-6 items-center justify-center rounded-full bg-red-500">
                      <X className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-sm font-medium text-red-700 dark:text-red-300">
                      Error: {error.message}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Modern Input Area */}
        <div className="px-4 pb-1 dark:bg-gray-900">
          <form onSubmit={handleSubmit}>
            <div className="mx-auto max-w-4xl">
              <div className="relative rounded-3xl border border-gray-200 bg-gray-50 shadow-sm transition-all duration-200 focus-within:border-gray-300 focus-within:shadow-md dark:border-gray-700 dark:bg-gray-800">
                <div className="flex items-end gap-2 p-3">
                  {/* Textarea with CSS auto-resize */}
                  <div className="flex-1">
                    {/* Selected Items Badges */}
                    {(selectedSpaces.length > 0 || selectedMaterials.length > 0) && (
                      <TooltipProvider delayDuration={100}>
                        <div className="mb-2 flex flex-wrap gap-1">
                          {/* Space badges */}
                          {selectedSpaces.map((space) => (
                            <Badge
                              key={space.id}
                              variant="secondary"
                              className="flex items-center gap-1 border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                            >
                              <FolderOpen className="h-3 w-3 flex-shrink-0" />
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="cursor-default truncate">
                                    {truncateMiddle(space.name)}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent className="bg-gray-50 text-xs text-gray-900 dark:bg-gray-900 dark:text-gray-100">
                                  <p>Space: {space.name}</p>
                                </TooltipContent>
                              </Tooltip>
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  removeSpace(space.id);
                                }}
                                className="ml-1 flex-shrink-0 rounded-full p-0.5 transition-colors hover:bg-blue-200 dark:hover:bg-blue-800"
                                aria-label={`Remove ${space.name}`}
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                          {/* Material badges */}
                          {selectedMaterials.map((material) => (
                            <Badge
                              key={material.id}
                              variant="secondary"
                              className="flex items-center gap-1 border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300"
                            >
                              <FileText className="h-3 w-3 flex-shrink-0" />
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="cursor-default truncate">
                                    {truncateMiddle(material.fileName)}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent className="bg-gray-50 text-xs text-gray-900 dark:bg-gray-900 dark:text-gray-100">
                                  <p>Document: {material.fileName}</p>
                                </TooltipContent>
                              </Tooltip>
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  removeMaterial(material.id);
                                }}
                                className="ml-1 flex-shrink-0 rounded-full p-0.5 transition-colors hover:bg-blue-200 dark:hover:bg-blue-800"
                                aria-label={`Remove ${material.fileName}`}
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      </TooltipProvider>
                    )}
                    <Textarea
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={
                        isAgentWorking
                          ? 'AI is processing...'
                          : 'Ask me anything about your documents...'
                      }
                      disabled={isAgentWorking}
                      className={cn(
                        'max-h-40 min-h-[40px] w-full resize-none border-0 bg-transparent px-3 py-2 text-sm leading-6',
                        'placeholder:text-gray-500 focus-visible:ring-0 focus-visible:ring-offset-0',
                        'dark:placeholder:text-gray-400',
                        isAgentWorking && 'cursor-not-allowed opacity-50'
                      )}
                      rows={1}
                      style={{
                        minHeight: '40px',
                        maxHeight: '160px', // 4 lines
                        overflowY: 'auto',
                        resize: 'none',
                      }}
                      onInput={(e) => {
                        const target = e.target as HTMLTextAreaElement;
                        target.style.height = 'auto';
                        target.style.height = `${Math.min(target.scrollHeight, 160)}px`;
                      }}
                    />

                    {/* Documents option positioned under textarea */}
                    <div className="mt-2 flex items-center gap-2">
                      {/* Documents Label and Settings */}
                      <Popover open={showSettings} onOpenChange={setShowSettings}>
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-7 rounded-full px-2 text-xs text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                            disabled={isAgentWorking}
                          >
                            <Settings2 className="mr-1 h-3 w-3" />
                            Documents
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-80" align="start">
                          <ChatSettings />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  {/* Send Button */}
                  <Button
                    type="submit"
                    disabled={isAgentWorking || !message.trim()}
                    size="icon"
                    className={cn(
                      'h-8 w-8 shrink-0 rounded-lg transition-all duration-200',
                      'bg-blue-500 hover:bg-blue-600 dark:bg-blue-100 dark:hover:bg-blue-200',
                      'disabled:cursor-not-allowed disabled:bg-gray-300 dark:disabled:bg-gray-600',
                      message.trim() && !isAgentWorking && 'shadow-sm hover:shadow-md'
                    )}
                  >
                    <Send className="h-4 w-4 text-white dark:text-gray-900" />
                  </Button>
                </div>
              </div>

              {/* Helper text */}
              <div className="mt-2 text-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Press Shift+Enter for new line
                </span>
              </div>
            </div>
          </form>
        </div>
      </div>

      {documentViewer}
    </div>
  );
}
