'use client';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenuAction, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { clientApi } from '@/lib/trpc/client-api';
import { useQueryClient } from '@tanstack/react-query';
import { MoreHorizontal, Trash } from 'lucide-react';
import { toast } from 'sonner';
import { useParams, useRouter } from 'next/navigation';
import * as React from 'react';
import { UserTopic } from '@/app/api/chats/route';

export type ChatHistoryItemProps = {
  chat: UserTopic;
};

export function ChatHistoryItem({ chat }: ChatHistoryItemProps) {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const isActive = params.chatId === chat.id;

  const deleteMutation = clientApi.chat.delete.useMutation({
    onSuccess: () => {
      // If we're currently viewing the deleted chat, navigate away
      if (isActive) {
        router.push('/');
      }

      // Invalidate and refetch the chats list
      queryClient.invalidateQueries({ queryKey: ['chats'] });

      toast.success('Topic deleted successfully');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to delete topic');
    },
  });

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (confirm(`Are you sure you want to delete "${chat.name}"? This action cannot be undone.`)) {
      deleteMutation.mutate(chat.id);
    }
  };

  return (
    <SidebarMenuItem className="group/item relative flex w-full justify-between">
      <SidebarMenuButton
        isActive={isActive}
        // onClick={() => router.push(`/chat/${chat.id}`)}
        onClick={() => router.push(`/chat/${chat.id}/enhanced`)}
        className="flex w-full items-center justify-between py-3"
        tooltip={chat.name}
      >
        <span className="truncate font-medium">{chat.name}</span>

        <SidebarMenuAction asChild className="absolute right-2 top-1/2 -translate-y-1/2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="h-8 w-8 p-0 opacity-0 transition-opacity group-hover/item:opacity-100">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More</span>
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                className="text-destructive"
                onClick={handleDelete}
                disabled={deleteMutation.isPending}
              >
                <Trash className="mr-2 h-4 w-4" />
                {deleteMutation.isPending ? 'Deleting...' : 'Delete Topic'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuAction>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}
