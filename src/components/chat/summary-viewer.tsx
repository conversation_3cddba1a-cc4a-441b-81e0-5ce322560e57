'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import type { DocumentSummary } from '@/lib/langgraph/types';
import { clientApi } from '@/lib/trpc/client-api';
import { useReferenceStore } from '@/stores/reference';
import { DocumentType } from '@/types/material';
import { BookOpen, ExternalLink, FileText, Loader2 } from 'lucide-react';

interface SummaryViewerProps {
  summary: DocumentSummary | null;
  isOpen: boolean;
  onClose: () => void;
}

export function SummaryViewer({ summary, isOpen, onClose }: SummaryViewerProps) {
  const { setSelectedReference } = useReferenceStore();

  // Fetch the full summary content using tRPC
  const {
    data: fullSummaryData,
    isLoading,
    error,
  } = clientApi.material.getSummary.useQuery(summary?.id || '', {
    enabled: !!summary?.id && isOpen, // Only fetch when we have an ID and dialog is open
  });

  if (!summary) return null;

  // Parse the structured summary if it contains sections (same logic as MaterialSummary)
  const parseSummary = (summaryText: string) => {
    const sections = summaryText.split('\n\n');
    const parsed = {
      executiveSummary: '',
      overallSummary: '',
      chapters: [] as Array<{ title: string; content: string }>,
      keyTopics: [] as string[],
      documentType: '',
      targetAudience: '',
      mainPurpose: '',
    };

    let currentSection = '';

    for (const section of sections) {
      const trimmed = section.trim();
      if (!trimmed) continue;

      if (trimmed.startsWith('EXECUTIVE SUMMARY:')) {
        parsed.executiveSummary = trimmed.replace('EXECUTIVE SUMMARY:', '').trim();
        currentSection = 'executive';
      } else if (trimmed.startsWith('OVERALL SUMMARY:')) {
        parsed.overallSummary = trimmed.replace('OVERALL SUMMARY:', '').trim();
        currentSection = 'overall';
      } else if (trimmed.startsWith('CHAPTER SUMMARIES:')) {
        currentSection = 'chapters';
      } else if (trimmed.startsWith('KEY TOPICS:')) {
        const topicsText = trimmed.replace('KEY TOPICS:', '').trim();
        parsed.keyTopics = topicsText
          .split(',')
          .map((t) => t.trim())
          .filter(Boolean);
        currentSection = 'topics';
      } else if (trimmed.startsWith('DOCUMENT TYPE:')) {
        parsed.documentType = trimmed.replace('DOCUMENT TYPE:', '').trim();
        currentSection = 'type';
      } else if (trimmed.startsWith('TARGET AUDIENCE:')) {
        parsed.targetAudience = trimmed.replace('TARGET AUDIENCE:', '').trim();
        currentSection = 'audience';
      } else if (trimmed.startsWith('MAIN PURPOSE:')) {
        parsed.mainPurpose = trimmed.replace('MAIN PURPOSE:', '').trim();
        currentSection = 'purpose';
      } else if (currentSection === 'chapters' && trimmed.includes(':')) {
        const [title, ...contentParts] = trimmed.split(':');
        if (title && contentParts.length > 0) {
          parsed.chapters.push({
            title: title.trim(),
            content: contentParts.join(':').trim(),
          });
        }
      }
    }

    return parsed;
  };

  // Use the full summary from tRPC if available, otherwise fall back to the summary from agent steps
  const summaryContent = fullSummaryData?.summary || summary.summary;
  const parsedSummary = parseSummary(summaryContent);
  const hasStructuredContent =
    parsedSummary.executiveSummary ||
    parsedSummary.overallSummary ||
    parsedSummary.chapters.length > 0;

  const handleViewDocument = () => {
    // Create a reference object compatible with the existing system
    // Use the key from tRPC data if available, otherwise fall back to summary key
    const referenceData = {
      id: summary.id,
      chunkId: 0, // Summary doesn't have a specific chunk
      fileName: fullSummaryData?.fileName || summary.fileName,
      content: summaryContent,
      key: fullSummaryData?.key || summary.key,
      similarity: summary.similarity || 1,
      fileType: (fullSummaryData?.fileType || summary.fileType) as DocumentType,
      metadata: {
        timestamp: Date.now(),
      },
    };

    setSelectedReference(referenceData);
    onClose(); // Close the summary dialog when opening document viewer
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="flex h-full max-h-[80vh] max-w-4xl flex-col p-0">
        <DialogHeader className="p-6 pb-4">
          <div className="flex items-center gap-10">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100">
                <BookOpen className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-lg font-semibold">Document Summary</DialogTitle>
                <DialogDescription className="text-sm text-gray-600">
                  {summary.fileName}
                </DialogDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="default" size="sm" onClick={handleViewDocument} className="gap-2">
                <ExternalLink className="h-4 w-4" />
                View Document
              </Button>
            </div>
          </div>
        </DialogHeader>

        <Separator />

        <ScrollArea className="flex-1 p-6 pt-2">
          {isLoading ? (
            <div className="flex h-32 items-center justify-center">
              <div className="flex items-center gap-2 text-gray-600">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading full summary...
              </div>
            </div>
          ) : error ? (
            <div className="flex h-32 items-center justify-center">
              <div className="text-center text-red-600">
                <div className="mb-2">Failed to load full summary</div>
                <div className="text-sm text-gray-500">
                  Showing partial content from search results
                </div>
              </div>
            </div>
          ) : hasStructuredContent ? (
            <div className="space-y-6">
              {/* Executive Summary */}
              {parsedSummary.executiveSummary && (
                <div>
                  <h3 className="mb-3 flex items-center gap-2 text-base font-semibold text-gray-900">
                    <FileText className="h-4 w-4 text-blue-600" />
                    Executive Summary
                  </h3>
                  <div className="rounded-lg bg-blue-50 p-4">
                    <p className="whitespace-pre-wrap text-sm leading-relaxed text-gray-700">
                      {parsedSummary.executiveSummary}
                    </p>
                  </div>
                </div>
              )}

              {/* Overall Summary */}
              {parsedSummary.overallSummary && (
                <div>
                  <h3 className="mb-3 text-base font-semibold text-gray-900">Detailed Summary</h3>
                  <div className="rounded-lg border border-gray-200 bg-white p-4">
                    <p className="whitespace-pre-wrap text-sm leading-relaxed text-gray-700">
                      {parsedSummary.overallSummary}
                    </p>
                  </div>
                </div>
              )}

              {/* Chapter Summaries */}
              {parsedSummary.chapters.length > 0 && (
                <div>
                  <h3 className="mb-3 text-base font-semibold text-gray-900">Chapter Summaries</h3>
                  <div className="space-y-3">
                    {parsedSummary.chapters.map((chapter, index) => (
                      <div key={index} className="rounded-lg border border-gray-200 bg-white p-4">
                        <h4 className="mb-2 text-sm font-medium text-blue-700">{chapter.title}</h4>
                        <p className="whitespace-pre-wrap text-sm leading-relaxed text-gray-600">
                          {chapter.content}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Key Topics */}
              {parsedSummary.keyTopics.length > 0 && (
                <div>
                  <h3 className="mb-3 text-base font-semibold text-gray-900">Key Topics</h3>
                  <div className="flex flex-wrap gap-2">
                    {parsedSummary.keyTopics.map((topic, index) => (
                      <span
                        key={index}
                        className="rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-700"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Document Metadata */}
              {(parsedSummary.documentType ||
                parsedSummary.targetAudience ||
                parsedSummary.mainPurpose) && (
                <div>
                  <h3 className="mb-3 text-base font-semibold text-gray-900">Document Details</h3>
                  <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                    <div className="space-y-2 text-sm text-gray-600">
                      {parsedSummary.documentType && (
                        <div>
                          <span className="font-medium text-gray-900">Type:</span>{' '}
                          {parsedSummary.documentType}
                        </div>
                      )}
                      {parsedSummary.targetAudience && (
                        <div>
                          <span className="font-medium text-gray-900">Audience:</span>{' '}
                          {parsedSummary.targetAudience}
                        </div>
                      )}
                      {parsedSummary.mainPurpose && (
                        <div>
                          <span className="font-medium text-gray-900">Purpose:</span>{' '}
                          {parsedSummary.mainPurpose}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            // Fallback for unstructured summaries - show complete content
            <div>
              <h3 className="mb-3 flex items-center gap-2 text-base font-semibold text-gray-900">
                <FileText className="h-4 w-4 text-blue-600" />
                Summary
              </h3>
              <div className="rounded-lg border border-gray-200 bg-white p-4">
                <p className="whitespace-pre-wrap text-sm leading-relaxed text-gray-700">
                  {summaryContent}
                </p>
              </div>
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
