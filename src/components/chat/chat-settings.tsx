import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { clientApi } from '@/lib/trpc/client-api';
import { useChatStore } from '@/stores/chat';
import { Check, ChevronDown, X } from 'lucide-react';
import { useState } from 'react';

export function ChatSettings() {
  // Note: initialConfig is passed but not used - we rely on the current config state for tab selection
  const { config, setConfig, resetConfig, hasConfigChanged } = useChatStore();
  const { data: spaces, isLoading: spacesLoading } = clientApi.space.list.useQuery(undefined, {
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
  const { data: materials, isLoading: materialsLoading } = clientApi.material.list.useQuery();
  const [spaceOpen, setSpaceOpen] = useState(false);
  const [documentOpen, setDocumentOpen] = useState(false);
  const [spaceSearch, setSpaceSearch] = useState('');
  const [documentSearch, setDocumentSearch] = useState('');
  const [userClickedTab, setUserClickedTab] = useState<'space' | 'document' | null>(null);

  // Determine the active filter type based on current selections and user clicks
  // Priority: user's explicit tab click > current selections > default to document
  const activeFilter =
    userClickedTab || ((config.spaceIds?.length ?? 0) > 0 ? 'space' : 'document');

  // Note: Original config initialization is now handled by the parent chat hooks

  const selectedSpaces = spaces?.filter((space) => config.spaceIds?.includes(space.id)) ?? [];
  const selectedDocuments = materials?.filter((doc) => config.documentIds?.includes(doc.id)) ?? [];

  const handleSpaceSelect = (spaceId: string) => {
    if (spaceId === 'all') {
      setConfig({ ...config, spaceIds: [], documentIds: [] });
      setSpaceOpen(false);
      return;
    }

    const newSpaceIds = config.spaceIds?.includes(spaceId)
      ? config.spaceIds.filter((id) => id !== spaceId)
      : [...(config.spaceIds ?? []), spaceId];

    // When selecting spaces, clear document selections to maintain exclusivity
    setConfig({ ...config, spaceIds: newSpaceIds, documentIds: [] });
    // Clear user clicked tab so the tab reflects the actual selections
    setUserClickedTab(null);
  };

  const handleDocumentSelect = (documentId: string) => {
    if (documentId === 'all') {
      setConfig({ ...config, documentIds: [], spaceIds: [] });
      setDocumentOpen(false);
      return;
    }

    const newDocumentIds = config.documentIds?.includes(documentId)
      ? config.documentIds.filter((id) => id !== documentId)
      : [...(config.documentIds ?? []), documentId];

    // When selecting documents, clear space selections to maintain exclusivity
    setConfig({ ...config, documentIds: newDocumentIds, spaceIds: [] });
    // Clear user clicked tab so the tab reflects the actual selections
    setUserClickedTab(null);
  };

  const filteredSpaces = spaces?.filter((space) =>
    space.name.toLowerCase().includes(spaceSearch.toLowerCase())
  );

  const filteredDocuments = materials?.filter((doc) =>
    doc.fileName.toLowerCase().includes(documentSearch.toLowerCase())
  );

  return (
    <div className="space-y-4 px-1">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Label className="text-sm font-medium">Filter Type</Label>
          {hasConfigChanged() && (
            <Button variant="outline" size="sm" onClick={resetConfig} className="h-7 px-2 text-xs">
              Reset Choices
            </Button>
          )}
        </div>
        <Tabs
          value={activeFilter}
          onValueChange={(value: string) => {
            const newFilterType = value as 'space' | 'document';
            // Set the user's explicit tab selection
            setUserClickedTab(newFilterType);
            // When switching tabs, clear the selections of the other type
            // This ensures the two filter types are mutually exclusive
            if (newFilterType === 'space') {
              // Switching to space filter - clear document selections
              setConfig({ ...config, documentIds: [] });
            } else {
              // Switching to document filter - clear space selections
              setConfig({ ...config, spaceIds: [] });
            }
          }}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="document">Document Filter</TabsTrigger>
            <TabsTrigger value="space">Space Filter</TabsTrigger>
          </TabsList>
        </Tabs>

        {activeFilter === 'space' ? (
          <div className="space-y-3">
            {spacesLoading ? (
              <Skeleton className="h-10 w-full" />
            ) : (
              <>
                <Popover open={spaceOpen} onOpenChange={setSpaceOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={spaceOpen}
                      className="w-full justify-between"
                    >
                      <span className="truncate">
                        {selectedSpaces.length > 0
                          ? `${selectedSpaces.length} spaces selected`
                          : 'All Spaces'}
                      </span>
                      <div className="flex items-center gap-1">
                        {selectedSpaces.length > 0 && (
                          <X
                            className="h-4 w-4 opacity-50 hover:opacity-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              setConfig({ ...config, spaceIds: [], documentIds: [] });
                            }}
                          />
                        )}
                        <ChevronDown className="h-4 w-4 opacity-50" />
                      </div>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command shouldFilter={false}>
                      <CommandInput
                        placeholder="Search spaces..."
                        value={spaceSearch}
                        onValueChange={setSpaceSearch}
                      />
                      <CommandList>
                        <CommandEmpty>No spaces found.</CommandEmpty>
                        <CommandGroup>
                          <CommandItem
                            value="all"
                            onSelect={() => handleSpaceSelect('all')}
                            className="justify-between"
                          >
                            All Spaces
                            {!config.spaceIds?.length && <Check className="h-4 w-4" />}
                          </CommandItem>
                          {filteredSpaces?.map((space) => (
                            <CommandItem
                              key={space.id}
                              value={space.id}
                              onSelect={() => handleSpaceSelect(space.id)}
                              className="justify-between"
                            >
                              {space.name}
                              {config.spaceIds?.includes(space.id) && <Check className="h-4 w-4" />}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {selectedSpaces.length > 0 && (
                  <div className="flex flex-wrap gap-1 pt-2">
                    {selectedSpaces.map((space) => (
                      <Badge
                        key={space.id}
                        variant="secondary"
                        className="flex items-center gap-1 rounded-lg"
                      >
                        {space.name}
                        <X
                          className="h-3 w-3 cursor-pointer opacity-50 transition-opacity hover:opacity-100"
                          onClick={() => handleSpaceSelect(space.id)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {materialsLoading ? (
              <Skeleton className="h-10 w-full" />
            ) : (
              <>
                <Popover open={documentOpen} onOpenChange={setDocumentOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={documentOpen}
                      className="w-full justify-between"
                    >
                      <span className="truncate">
                        {selectedDocuments.length > 0
                          ? `${selectedDocuments.length} documents selected`
                          : 'All Documents'}
                      </span>
                      <div className="flex items-center gap-1">
                        {selectedDocuments.length > 0 && (
                          <X
                            className="h-4 w-4 opacity-50 hover:opacity-100"
                            onClick={(e) => {
                              e.stopPropagation();
                              setConfig({ ...config, documentIds: [], spaceIds: [] });
                            }}
                          />
                        )}
                        <ChevronDown className="h-4 w-4 opacity-50" />
                      </div>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0" align="start">
                    <Command shouldFilter={false}>
                      <CommandInput
                        placeholder="Search documents..."
                        value={documentSearch}
                        onValueChange={setDocumentSearch}
                      />
                      <CommandList>
                        <CommandEmpty>No documents found.</CommandEmpty>
                        <CommandGroup>
                          <CommandItem
                            value="all"
                            onSelect={() => handleDocumentSelect('all')}
                            className="justify-between"
                          >
                            All Documents
                            {!config.documentIds?.length && <Check className="h-4 w-4" />}
                          </CommandItem>
                          {filteredDocuments?.map((doc) => (
                            <CommandItem
                              key={doc.id}
                              value={doc.id}
                              onSelect={() => handleDocumentSelect(doc.id)}
                              className="justify-between"
                            >
                              {doc.fileName}
                              {config.documentIds?.includes(doc.id) && (
                                <Check className="h-4 w-4" />
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                {selectedDocuments.length > 0 && (
                  <div className="flex flex-wrap gap-1 pt-2">
                    {selectedDocuments.map((doc) => (
                      <Badge
                        key={doc.id}
                        variant="secondary"
                        className="flex items-center gap-1 rounded-lg"
                      >
                        {doc.fileName}
                        <X
                          className="h-3 w-3 cursor-pointer opacity-50 transition-opacity hover:opacity-100"
                          onClick={() => handleDocumentSelect(doc.id)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      <Separator />

      <div className="space-y-3">
        <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-950/20">
          <div className="flex items-center gap-2">
            <div className="flex h-4 w-4 items-center justify-center rounded-full bg-blue-500">
              <svg className="h-2.5 w-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <Label className="text-sm font-medium text-blue-900 dark:text-blue-100">
              Smart Document Selection
            </Label>
          </div>
          <p className="mt-2 text-xs text-blue-700 dark:text-blue-300">
            Our AI automatically determines the optimal number of documents (3-15) based on your
            question&apos;s complexity. No manual configuration needed!
          </p>
        </div>
      </div>
    </div>
  );
}
