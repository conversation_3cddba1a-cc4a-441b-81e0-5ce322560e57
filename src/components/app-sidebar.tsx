'use client';

import { ChatH<PERSON>oryList } from '@/components/chat/chat-history-list';
import { Sidebar, SidebarContent, SidebarHeader } from '@/components/ui/sidebar';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { User } from 'lucia';
// import { useLocalStorage } from 'usehooks-ts';
import * as React from 'react';
import { GroupedChats } from '@/app/api/chats/route';
import { SidebarUser } from './sidebar-user';

export type ChatHistory = {
  lastUpdated: number;
  chats: GroupedChats;
};

type AppSidebarProps = {
  user: User;
};

export function AppSidebar({ user }: AppSidebarProps) {
  // const [cached, setCache] = useLocalStorage<ChatHistory>('chat-history', {
  //   lastUpdated: 0,
  //   chats: { today: [], yesterday: [], previous7Days: [], older: [] },
  // });

  // console.log('cached: ', cached);

  const { data: chats, isLoading } = useQuery({
    queryKey: ['chats'],
    queryFn: async () => {
      const response = await axios.get<GroupedChats>('/api/chats');
      const chats = response.data;

      // setCache({
      //   lastUpdated: Date.now(),
      //   chats,
      // });

      return chats;
    },
    // Show cached data while fetching
    // initialData: cached.chats,
  });

  return (
    <Sidebar variant="sidebar" collapsible="offcanvas">
      <SidebarHeader>
        <div className="flex items-center justify-between p-2">
          <SidebarUser user={user} />
        </div>
      </SidebarHeader>

      <SidebarContent className="p-2">
        <ChatHistoryList chats={chats} isLoading={isLoading} />
      </SidebarContent>

      {/* <SidebarFooter className="border-t p-2">
      </SidebarFooter> */}
    </Sidebar>
  );
}
