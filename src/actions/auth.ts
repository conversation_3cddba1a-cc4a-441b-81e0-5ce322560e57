'use server';

import { SITE_PATHS } from '@/configs/site';
import { lucia, validateRequest } from '@/lib/auth';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export async function logOut() {
  const { session } = await validateRequest();
  if (!session) {
    throw new Error('Unauthorized');
  }

  await lucia.invalidateSession(session.id);

  const sessionCookie = lucia.createBlankSessionCookie();
  cookies().set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);
  return redirect(SITE_PATHS.AUTH.SIGN_IN);
}
