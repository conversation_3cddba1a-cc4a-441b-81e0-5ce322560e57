'use client';

import { SITE_PATHS } from '@/configs/site';
import { useUserStore } from '@/stores/user';
import type { User } from 'lucia';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

type UserProviderProps = {
  children: React.ReactNode;
  user: User | null;
};

const byPassUrls = [SITE_PATHS.AUTH.VERIFY_EMAIL, SITE_PATHS.AUTH.RESET_PASSWORD];

export function UserProvider({ children, user }: UserProviderProps) {
  const setUser = useUserStore((state) => state.setUser);
  const setLoading = useUserStore((state) => state.setLoading);
  const pathname = usePathname();
  const router = useRouter();
  const [redirecting, setRedirecting] = useState(false);

  // Update user store when user changes
  useEffect(() => {
    setUser(user);
    setLoading(false);
  }, [user, setUser, setLoading]);

  useEffect(() => {
    // Get real-time state from the store
    const storeUser = useUserStore.getState().user;
    const isVerified = storeUser?.emailVerified;

    // If email is not verified and we're not on the verification page
    // and we're not already redirecting
    if (!isVerified && !byPassUrls.includes(pathname) && !redirecting) {
      setRedirecting(true);
      router.push(SITE_PATHS.AUTH.VERIFY_EMAIL);
    }
  }, [user, pathname, router, redirecting]);

  return children;
}
