import { z } from 'zod';

export const UserSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  email: z.string().email(),
  password: z.string().min(6), // TODO: update the password to be more secure
  //   password: z.string().min(8),
});

export type User = z.infer<typeof UserSchema>;

export const SignUpSchema = UserSchema.omit({ id: true });
export type SignUpInput = z.infer<typeof SignUpSchema>;

export const SignInSchema = UserSchema.pick({ email: true, password: true });
export type SignInInput = z.infer<typeof SignInSchema>;
