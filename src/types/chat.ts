import { DocumentType } from '@/types/material';
import { z } from 'zod';

export type DocumentMetadata = {
  pageNumber?: number;
  loc?: {
    pageNumber?: number;
    [key: string]: unknown;
  };
  timestamp?: number;
} & {
  [key: string]: unknown;
};

export type RetrievedDocumentChunkDTO = {
  id: string; // document.id
  chunkId: number; // documentChunks.id
  fileName: string; // documents.fileName
  content: string; // documentChunks.content
  key: string; // documents.s3Key
  similarity?: number; // documentChunks.similarity
  fileType: DocumentType; // documents.fileType
  metadata: DocumentMetadata; // documentChunks.metadata
};

export type ChatConfig = {
  spaceIds?: string[];
  documentIds?: string[];
  searchMode: 'semantic' | 'keyword';
};

export const ChatConfigSchema = z.object({
  spaceIds: z.array(z.string()).optional(),
  documentIds: z.array(z.string()).optional(),
  searchMode: z.enum(['semantic', 'keyword']),
});
