/**
 * Test script for the enhanced UI progress component
 */

import { runRAGAgent } from '@/lib/langgraph/graph';

// Test configuration
const TEST_CONFIG = {
  userId: 'test-user-id',
  maxDocuments: 5,
  spaceIds: undefined,
  documentIds: undefined,
};

async function testUIProgress() {
  console.log('🧪 Testing Enhanced UI Progress Component');
  console.log('==========================================\n');

  const testQueries = [
    'Give me a summary of the documents',
    'What is machine learning?',
    'Compare different approaches to neural networks',
  ];

  for (const query of testQueries) {
    console.log(`\n🔍 Testing Query: "${query}"`);
    console.log('-----------------------------------');

    try {
      const steps: Array<{
        step: string;
        reasoning?: string;
        extractedKeywords?: string[];
        retrievedChunks?: Array<{
          id: string;
          fileName: string;
          content: string;
          similarity?: number;
          fileType: string;
        }>;
        retrievedSummaries?: Array<{
          id: string;
          fileName: string;
          summary: string;
          similarity?: number;
        }>;
        retrievalStrategy?: string | null;
        error?: string | null;
      }> = [];

      const result = await runRAGAgent(
        query,
        TEST_CONFIG,
        (nodeName, state) => {
          // Simulate step completion
          steps.push({
            step: nodeName,
            reasoning: state.reasoning,
            extractedKeywords: state.refinedKeywords,
            retrievedChunks: state.retrievedChunks,
            retrievedSummaries: state.retrievedSummaries,
            retrievalStrategy: state.retrievalStrategy,
            error: state.error,
          });

          console.log(`📍 Step completed: ${nodeName}`);
          if (state.refinedKeywords?.length) {
            console.log(`  🔑 Keywords: ${state.refinedKeywords.join(', ')}`);
          }
          if (state.retrievedChunks?.length) {
            console.log(`  📄 Chunks: ${state.retrievedChunks.length}`);
          }
          if (state.retrievedSummaries?.length) {
            console.log(`  📋 Summaries: ${state.retrievedSummaries.length}`);
          }
        },
        (event) => {
          if (event.type === 'step_start') {
            console.log(`  ⏳ ${event.step}: ${event.message}`);
          }
        }
      );

      console.log('\n📊 UI Progress Test Results:');
      console.log(`  Total Steps Captured: ${steps.length}`);
      console.log(`  Final Answer Generated: ${!!result.finalAnswer}`);

      // Simulate what the UI component would receive
      const uiSteps = steps.filter(
        (step) =>
          !['generate_answer', 'generate_direct_response', 'handle_failure'].includes(step.step)
      );

      console.log(`  UI-Visible Steps: ${uiSteps.length}`);
      console.log(`  Step Types: ${uiSteps.map((s) => s.step).join(' → ')}`);

      // Check for grouped step patterns
      const hasClassify = uiSteps.some((s) => s.step === 'classify_query');
      const hasRetrieve = uiSteps.some((s) =>
        ['retrieve_context', 'retrieve_summary_context'].includes(s.step)
      );
      const hasEvaluate = uiSteps.some((s) => s.step === 'evaluate_results');
      const hasRefine = uiSteps.some((s) => s.step === 'refine_query');

      console.log('\n🎯 Expected UI Groups:');
      console.log(`  ✅ Classify Intent: ${hasClassify ? 'Present' : 'Missing'}`);
      console.log(`  ✅ Retrieve Context: ${hasRetrieve ? 'Present' : 'Missing'}`);
      console.log(`  ✅ Evaluate Results: ${hasEvaluate ? 'Present' : 'Missing'}`);
      console.log(
        `  ${hasRefine ? '✅' : '⚪'} Refine Query: ${hasRefine ? 'Present' : 'Not needed'}`
      );
    } catch (error) {
      console.error(`❌ Test failed for "${query}":`, error);
    }

    console.log('\n' + '='.repeat(50));
  }

  console.log('\n🎉 UI Progress Testing Complete!');
  console.log('\n📝 Summary:');
  console.log('The enhanced UI should now show:');
  console.log('1. 🧠 Understanding your question');
  console.log('2. 🔍 Searching for context (with keywords, chunks, summaries)');
  console.log('3. ⚖️ Evaluating retrieved context');
  console.log('4. 🔄 Refining search approach (if needed)');
  console.log('5. 🔍 Searching for additional context (if refined)');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testUIProgress().catch(console.error);
}

export { testUIProgress };
