/**
 * Test complex UI scenario with multiple retrieval and evaluation steps
 */

import { runRAGAgent } from '@/lib/langgraph/graph';

// Test configuration
const TEST_CONFIG = {
  userId: 'test-user-id',
  maxDocuments: 5,
  spaceIds: undefined,
  documentIds: undefined,
};

async function testComplexUIScenario() {
  console.log('🧪 Testing Complex UI Scenario');
  console.log('===============================\n');

  console.log('Testing Query: "Compare machine learning and artificial intelligence"');
  console.log('This should trigger multiple retrieval and evaluation cycles');
  console.log('----------------------------------------------------------------------');

  try {
    const steps: Array<{
      step: string;
      reasoning?: string;
      extractedKeywords?: string[];
      retrievedChunks?: Array<{
        id: string;
        fileName: string;
        content: string;
        similarity?: number;
        fileType: string;
      }>;
      retrievedSummaries?: Array<{
        id: string;
        fileName: string;
        summary: string;
        similarity?: number;
      }>;
      retrievalStrategy?: string | null;
      evaluationResult?: string;
      error?: string | null;
    }> = [];
    const runningSteps: Array<{
      step: string;
      message: string;
    }> = [];

    await runRAGAgent(
      'Compare machine learning and artificial intelligence',
      TEST_CONFIG,
      (nodeName, state) => {
        // Capture completed steps
        steps.push({
          step: nodeName,
          reasoning: state.reasoning,
          extractedKeywords: state.refinedKeywords,
          retrievedChunks: state.retrievedChunks,
          retrievedSummaries: state.retrievedSummaries,
          retrievalStrategy: state.retrievalStrategy,
          evaluationResult: state.evaluationResult,
          error: state.error,
        });

        console.log(`✅ Step completed: ${nodeName}`);
      },
      (event) => {
        if (event.type === 'step_start') {
          // Capture running steps
          runningSteps.push({
            step: event.step,
            message: event.message,
          });

          console.log(`🔄 Step started: ${event.step} - ${event.message}`);
        }
      }
    );

    console.log('\n📊 Complex UI Scenario Analysis:');
    console.log('=================================');

    // Count different types of steps
    const retrievalSteps = steps.filter((s) =>
      ['retrieve_context', 'retrieve_summary_context'].includes(s.step)
    );
    const evaluationSteps = steps.filter((s) => s.step === 'evaluate_results');
    const refineSteps = steps.filter((s) => s.step === 'refine_query');

    console.log(`\n📄 Total retrieval steps: ${retrievalSteps.length}`);
    retrievalSteps.forEach((step, idx) => {
      console.log(`   ${idx + 1}. ${step.step} - Strategy: ${step.retrievalStrategy}`);
    });

    console.log(`\n⚖️ Total evaluation steps: ${evaluationSteps.length}`);
    evaluationSteps.forEach((step, idx) => {
      console.log(`   ${idx + 1}. ${step.step} - Result: ${step.evaluationResult}`);
    });

    console.log(`\n🔄 Total refine steps: ${refineSteps.length}`);
    refineSteps.forEach((step, idx) => {
      console.log(`   ${idx + 1}. ${step.step}`);
    });

    console.log('\n🎯 Expected UI Behavior (NEW LOGIC):');
    console.log('====================================');

    console.log('1. 🧠 Understanding your question');
    console.log('   - ONE step, regardless of internal processing');

    console.log('2. 🔍 Searching for context');
    console.log('   - ONE consolidated step showing:');
    console.log('     • All keywords from all retrieval attempts');
    console.log('     • All chunks from all retrieval attempts');
    console.log('     • All summaries from all retrieval attempts');
    console.log('     • Latest retrieval strategy');

    console.log('3. ⚖️ Evaluating retrieved context');
    console.log('   - ONE consolidated step showing:');
    console.log('     • Latest evaluation reasoning');
    console.log('     • Final evaluation result');

    if (refineSteps.length > 0) {
      console.log('4. 🔄 Refining search approach');
      console.log('   - Separate steps for each refinement (these are truly different operations)');
    }

    console.log('\n✅ Key Improvements:');
    console.log('====================');
    console.log('❌ OLD: Multiple "Searching for context (attempt 2)" steps');
    console.log('✅ NEW: Single "Searching for context" step with consolidated data');
    console.log('');
    console.log('❌ OLD: Multiple "Evaluating context (attempt 2)" steps');
    console.log('✅ NEW: Single "Evaluating retrieved context" step with latest data');
    console.log('');
    console.log('❌ OLD: Steps merge together after completion (confusing behavior)');
    console.log('✅ NEW: Each logical operation has exactly one persistent UI step');
    console.log('');
    console.log('❌ OLD: "Understanding your question" shows green circle immediately');
    console.log('✅ NEW: "Understanding your question" shows spinner when running');
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testComplexUIScenario().catch(console.error);
}

export { testComplexUIScenario };
