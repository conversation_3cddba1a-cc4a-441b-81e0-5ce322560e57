/**
 * Test script for the enhanced AI Agent flow with Plan-and-Execute model
 */

import { runRAGAgent } from '@/lib/langgraph/graph';

// Test configuration
const TEST_CONFIG = {
  userId: 'test-user-id',
  maxDocuments: 5,
  spaceIds: undefined,
  documentIds: undefined,
};

// Test queries to validate different flow paths
const TEST_QUERIES = [
  {
    name: 'Direct Summary Request',
    query: 'Give me a summary of the documents',
    expectedPlan: 'direct_summary',
    expectedAction: 'retrieve_summary_context',
  },
  {
    name: 'Keyword Search Request',
    query: 'What is machine learning?',
    expectedPlan: 'keyword_search',
    expectedAction: 'retrieve_context',
  },
  {
    name: 'Comprehensive Research Request',
    query: 'Compare different approaches to neural networks',
    expectedPlan: 'comprehensive_research',
    expectedAction: 'retrieve_context',
  },
  {
    name: 'Casual Greeting',
    query: 'Hello there',
    expectedPlan: null, // Should skip research
    expectedAction: null,
  },
];

async function testEnhancedFlow() {
  console.log('🧪 Testing Enhanced AI Agent Flow');
  console.log('=====================================\n');

  for (const testCase of TEST_QUERIES) {
    console.log(`\n🔍 Testing: ${testCase.name}`);
    console.log(`Query: "${testCase.query}"`);
    console.log('-----------------------------------');

    try {
      const result = await runRAGAgent(
        testCase.query,
        TEST_CONFIG,
        (nodeName, state) => {
          console.log(`📍 Node: ${nodeName}`);

          // Log key state changes
          if (nodeName === 'create_research_plan') {
            console.log(`  🧠 Plan Type: ${state.planType}`);
            console.log(`  🎯 Target Action: ${state.targetAction}`);
            console.log(`  🔍 Strategy: ${state.retrievalStrategy}`);
            console.log(`  🔧 Needs Keywords: ${state.needsKeywordGeneration}`);
          }

          if (nodeName === 'retrieve_context' && state.refinedKeywords?.length) {
            console.log(`  🔑 Keywords: ${state.refinedKeywords.join(', ')}`);
          }

          if (nodeName === 'generate_answer' || nodeName === 'generate_direct_response') {
            console.log(`  ✅ Final Answer Generated`);
          }
        },
        (event) => {
          if (event.type === 'step_start') {
            console.log(`  ⏳ ${event.message}`);
          }
        }
      );

      // Validate results
      console.log('\n📊 Test Results:');
      console.log(`  Query Intent: ${result.queryIntent}`);
      console.log(`  Skip Research: ${result.skipResearch}`);

      if (!result.skipResearch) {
        console.log(`  Plan Type: ${result.planType}`);
        console.log(`  Target Action: ${result.targetAction}`);
        console.log(`  Retrieval Strategy: ${result.retrievalStrategy}`);
        console.log(`  Keywords Generated: ${result.needsKeywordGeneration}`);
        console.log(`  Retrieved Chunks: ${result.retrievedChunks?.length || 0}`);
        console.log(`  Retrieved Summaries: ${result.retrievedSummaries?.length || 0}`);

        // Validate expectations
        if (testCase.expectedPlan && result.planType !== testCase.expectedPlan) {
          console.log(`  ⚠️  Expected plan: ${testCase.expectedPlan}, got: ${result.planType}`);
        } else if (testCase.expectedPlan) {
          console.log(`  ✅ Plan type matches expectation`);
        }

        if (testCase.expectedAction && result.targetAction !== testCase.expectedAction) {
          console.log(
            `  ⚠️  Expected action: ${testCase.expectedAction}, got: ${result.targetAction}`
          );
        } else if (testCase.expectedAction) {
          console.log(`  ✅ Target action matches expectation`);
        }
      }

      console.log(`  Final Answer Length: ${result.finalAnswer?.length || 0} characters`);
      console.log(`  Error: ${result.error || 'None'}`);

      if (result.finalAnswer) {
        console.log(`\n💬 Response Preview: ${result.finalAnswer.substring(0, 150)}...`);
      }
    } catch (error) {
      console.error(`❌ Test failed for "${testCase.name}":`, error);
    }

    console.log('\n' + '='.repeat(50));
  }

  console.log('\n🎉 Enhanced Flow Testing Complete!');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEnhancedFlow().catch(console.error);
}

export { testEnhancedFlow };
