import { MaterialRepository } from '@/database/repository/material';
import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';

dotenvExpand.expand(dotenv.config());

async function main() {
  const material = await MaterialRepository.getMaterialByKey(
    'a95d7237-ddda-49fd-bd8d-a965a38c9c82'
  );
  console.log(material);
  await MaterialRepository.deleteMaterial(material.id, material.s3Key);
  process.exit(0);
}

main();
