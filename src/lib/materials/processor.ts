import {
  analyzeDocumentWithTextract,
  checkTextractAvailability,
  processTextractBlocksToChunks,
  type TextractResult,
} from '@/lib/aws/textract';
import { r2Client } from '@/lib/cloudflare/r2';
import { env } from '@/lib/env.mjs';
import { GetObjectCommand } from '@aws-sdk/client-s3';
import { DocxLoader } from '@langchain/community/document_loaders/fs/docx';
import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf';
import { Document } from '@langchain/core/documents';
import { TextLoader } from 'langchain/document_loaders/fs/text';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';

export interface ProcessedChunk {
  content: string;
  chunkOrder: number;
  metadata: Record<string, unknown>;
  // OCR-specific fields
  boundingBox?: unknown;
  polygonCoordinates?: unknown;
  layoutType?: string;
  confidenceScore?: number;
}

export interface ProcessMaterialResult {
  chunks: ProcessedChunk[];
  ocrAnalysis?: TextractResult;
}

/**
 * Process a material file with enhanced OCR support and generate embeddings for its content
 * @param file File to process
 * @param fileType MIME type of the file
 * @param debug Whether to log detailed processing information (default: false)
 * @returns Processed chunks with embeddings and OCR data
 */
export async function processMaterialEnhanced(
  file: File,
  fileType: string,
  debug = false
): Promise<ProcessMaterialResult> {
  if (debug) {
    console.log(`\n==== STARTING ENHANCED MATERIAL PROCESSING ====`);
    console.log(
      `File type: ${fileType}, File name: ${file.name}, Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`
    );
  }
  const processStartTime = performance.now();

  const supportedTypes = [
    'text/plain',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
  ];

  if (!supportedTypes.includes(fileType)) {
    console.error(`Unsupported file type: ${fileType}`);
    throw new Error(
      `Unsupported file type: ${fileType}. Supported types are: ${supportedTypes.join(', ')}`
    );
  }

  // Check if this is a PDF and Textract is available
  const isPdf = fileType === 'application/pdf';
  const textractAvailable = isPdf ? await checkTextractAvailability() : false;

  if (debug) {
    console.log(`PDF file: ${isPdf}, Textract available: ${textractAvailable}`);
  }

  let chunks: Array<{
    content: string;
    chunkOrder: number;
    metadata: Record<string, unknown>;
    boundingBox?: unknown;
    polygonCoordinates?: unknown;
    layoutType?: string;
    confidenceScore?: number;
  }> = [];
  let ocrAnalysis: TextractResult | undefined;

  // Use Textract for PDFs when available
  if (isPdf && textractAvailable) {
    if (debug) {
      console.log('🔍 Using AWS Textract for PDF processing...');
    }

    try {
      const fileBuffer = await file.arrayBuffer();
      const documentBytes = Buffer.from(fileBuffer);

      // Analyze with Textract
      ocrAnalysis = await analyzeDocumentWithTextract(documentBytes, debug);

      // Convert Textract results to semantic chunks (using LAYOUT_* boundaries)
      const textractChunks = processTextractBlocksToChunks(ocrAnalysis);

      chunks = textractChunks.map((chunk) => {
        // Extract page index from the first geometry item (most chunks will be on single page)
        const pageIndex =
          ocrAnalysis?.extractedText.find((item) =>
            chunk.geometry.some(
              (g) =>
                Math.abs(g.boundingBox.left - item.geometry.boundingBox.left) < 0.01 &&
                Math.abs(g.boundingBox.top - item.geometry.boundingBox.top) < 0.01
            )
          )?.pageIndex || 0;

        return {
          content: chunk.content,
          chunkOrder: chunk.chunkOrder,
          metadata: {
            layoutTypes: chunk.layoutTypes,
            extractionMethod: 'textract',
            pageIndex,
          },
          boundingBox:
            chunk.geometry.length > 0 ? chunk.geometry.map((g) => g.boundingBox) : undefined,
          polygonCoordinates:
            chunk.geometry.length > 0 ? chunk.geometry.map((g) => g.polygon) : undefined,
          layoutType: chunk.layoutTypes.length > 0 ? chunk.layoutTypes[0] : 'UNKNOWN',
          confidenceScore: Math.round(chunk.confidenceScore * 100), // Convert to percentage
        };
      });

      if (debug) {
        console.log(`✅ Textract processing completed. Generated ${chunks.length} chunks`);
      }
    } catch (error) {
      console.warn(
        '⚠️ Textract processing failed, falling back to standard PDF processing:',
        error
      );
      // Fall back to standard processing
      chunks = await processWithStandardMethod(file, fileType, debug);
    }
  } else {
    // Use standard processing for non-PDFs or when Textract is unavailable
    chunks = await processWithStandardMethod(file, fileType, debug);
  }

  const processEndTime = performance.now();
  const totalProcessingTime = (processEndTime - processStartTime) / 1000;

  if (debug) {
    console.log(`\n==== ENHANCED MATERIAL PROCESSING COMPLETE ====`);
    console.log(`Total processing time: ${totalProcessingTime.toFixed(2)}s`);
    console.log(`Extraction method: ${isPdf && textractAvailable ? 'Textract' : 'Standard'}`);
    console.log(`Chunks generated: ${chunks.length}`);
    if (ocrAnalysis) {
      console.log(`OCR blocks processed: ${ocrAnalysis.extractedText.length}`);
    }
  }

  return {
    chunks,
    ocrAnalysis,
  };
}

/**
 * Standard material processing using traditional text extraction
 */
async function processWithStandardMethod(file: File, fileType: string, debug = false) {
  let loader;
  switch (fileType) {
    case 'application/pdf':
      loader = new PDFLoader(file);
      break;
    case 'application/msword':
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      loader = new DocxLoader(file);
      break;
    case 'text/plain':
      loader = new TextLoader(file);
      break;
    default:
      throw new Error(`Unsupported file type: ${fileType}`);
  }

  if (debug) {
    console.log(`Loading file content using ${loader.constructor.name} (standard method)...`);
  }

  const pages = await loader.load();

  // Split text into chunks
  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize: 1000,
    chunkOverlap: 200,
  });

  const splitChunks = await splitter.splitDocuments(pages);

  return splitChunks.map((chunk: Document, index: number) => ({
    content: chunk.pageContent,
    chunkOrder: index,
    metadata: { ...chunk.metadata?.loc, extractionMethod: 'standard' },
  }));
}

/**
 * Process a material file from Cloudflare R2 storage with enhanced OCR support
 * @param fileKey Key of the file in R2 storage
 * @param fileName Original filename for the document
 * @param fileType MIME type of the file
 * @param debug Whether to log detailed processing information (default: false)
 * @returns Processed chunks with embeddings and OCR data
 */
export async function processMaterialFromR2Enhanced(
  fileKey: string,
  fileName: string,
  fileType: string,
  debug = false
): Promise<ProcessMaterialResult> {
  if (debug) {
    console.log(`\n==== STARTING ENHANCED R2 MATERIAL PROCESSING ====`);
    console.log(`File key: ${fileKey}, File type: ${fileType}, File name: ${fileName}`);
  }
  const r2ProcessStartTime = performance.now();

  const supportedTypes = [
    'text/plain',
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
  ];

  if (!supportedTypes.includes(fileType)) {
    console.error(`Unsupported file type: ${fileType}`);
    throw new Error(
      `Unsupported file type: ${fileType}. Supported types are: ${supportedTypes.join(', ')}`
    );
  }

  try {
    // Download file from R2
    if (debug) {
      console.log(`Downloading file from R2 bucket: ${env.CLOUDFLARE_BUCKET_NAME}...`);
    }
    const downloadStartTime = performance.now();

    const getObjectCommand = new GetObjectCommand({
      Bucket: env.CLOUDFLARE_BUCKET_NAME,
      Key: fileKey,
    });

    const response = await r2Client.send(getObjectCommand);
    if (!response.Body) {
      throw new Error('Failed to retrieve file from R2');
    }

    // Convert the stream to an array buffer
    const arrayBuffer = await response.Body.transformToByteArray();

    const downloadEndTime = performance.now();
    if (debug) {
      console.log(
        `✅ File downloaded from R2 in ${((downloadEndTime - downloadStartTime) / 1000).toFixed(2)}s`
      );
      console.log(`File size: ${(arrayBuffer.length / 1024 / 1024).toFixed(2)} MB`);
    }

    // Create a File object from the array buffer using the actual filename
    const file = new File([arrayBuffer], fileName, { type: fileType });

    const r2ProcessEndTime = performance.now();
    if (debug) {
      console.log(
        `R2 preparation time: ${((r2ProcessEndTime - r2ProcessStartTime) / 1000).toFixed(2)}s`
      );
      console.log('Passing file to processMaterialEnhanced function...');
    }

    // Use the enhanced processing function
    return await processMaterialEnhanced(file, fileType, debug);
  } catch (error) {
    console.error('Error processing material from R2:', error);
    throw new Error('Failed to process material from R2');
  }
}
