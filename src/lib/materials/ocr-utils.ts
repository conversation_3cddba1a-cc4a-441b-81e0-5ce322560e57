import { type OCRHighlight } from '@/components/documents/enhanced-pdf-viewer';
import { type DocumentChunk, type OcrAnalysis } from '@/database/schema';
import { type TextractResult } from '@/lib/aws/textract';
import { type Block } from '@aws-sdk/client-textract';

/**
 * Convert document chunks with OCR data to PDF highlights
 */
export function convertChunksToHighlights(chunks: DocumentChunk[]): OCRHighlight[] {
  const highlights: OCRHighlight[] = [];

  chunks.forEach((chunk) => {
    if (!chunk.boundingBox || !chunk.layoutType) {
      return; // Skip chunks without OCR data
    }

    try {
      // Parse bounding boxes (could be an array for chunks spanning multiple regions)
      const boundingBoxes = Array.isArray(chunk.boundingBox)
        ? chunk.boundingBox
        : [chunk.boundingBox];

      // Parse polygon coordinates if available
      const polygons = chunk.polygonCoordinates
        ? Array.isArray(chunk.polygonCoordinates)
          ? chunk.polygonCoordinates
          : [chunk.polygonCoordinates]
        : [];

      // Handle chunks with multiple bounding boxes by calculating combined bounds (same as original method)
      if (boundingBoxes.length > 0) {
        const metadata = chunk.metadata as Record<string, unknown> | null;
        const pageIndex = (metadata?.pageIndex as number) || 0;

        // Convert chunk bounding boxes to Textract format for the existing function
        const textractBoundingBoxes = boundingBoxes.map((bbox) => ({
          Left: bbox.left,
          Top: bbox.top,
          Width: bbox.width,
          Height: bbox.height,
        }));

        // Use the existing calculateCombinedBoundingBox function (same as original working method)
        const combinedBoundingBox = calculateCombinedBoundingBox(textractBoundingBoxes) ?? {
          Left: 0,
          Top: 0,
          Width: 0,
          Height: 0,
        };

        // Use the first polygon or create one from the combined bounding box
        const combinedPolygon =
          polygons.length > 0
            ? polygons[0]
            : [
                { x: combinedBoundingBox.Left ?? 0, y: combinedBoundingBox.Top ?? 0 },
                {
                  x: (combinedBoundingBox.Left ?? 0) + (combinedBoundingBox.Width ?? 0),
                  y: combinedBoundingBox.Top ?? 0,
                },
                {
                  x: (combinedBoundingBox.Left ?? 0) + (combinedBoundingBox.Width ?? 0),
                  y: (combinedBoundingBox.Top ?? 0) + (combinedBoundingBox.Height ?? 0),
                },
                {
                  x: combinedBoundingBox.Left ?? 0,
                  y: (combinedBoundingBox.Top ?? 0) + (combinedBoundingBox.Height ?? 0),
                },
              ];

        const highlight: OCRHighlight = {
          id: chunk.id?.toString() || '',
          pageIndex,
          boundingBox: {
            left: combinedBoundingBox.Left ?? 0,
            top: combinedBoundingBox.Top ?? 0,
            width: combinedBoundingBox.Width ?? 0,
            height: combinedBoundingBox.Height ?? 0,
          },
          polygon: combinedPolygon,
          text: chunk.content,
          layoutType: chunk.layoutType || 'UNKNOWN',
          confidence: chunk.confidenceScore ? chunk.confidenceScore / 100 : 0,
          chunkId: chunk.id?.toString() || '',
        };

        highlights.push(highlight);
      }
    } catch (error) {
      console.warn('Failed to parse OCR data for chunk:', chunk.id, error);
    }
  });

  return highlights;
}

/**
 * Convert Textract analysis data to PDF highlights using semantic LAYOUT_* blocks
 */
export function convertTextractToHighlights(ocrAnalysis: OcrAnalysis): OCRHighlight[] {
  const highlights: OCRHighlight[] = [];

  try {
    const analysisData = ocrAnalysis.analysisData as TextractResult;
    const rawBlocks = analysisData.rawBlocks;

    if (!rawBlocks || rawBlocks.length === 0) {
      console.warn('No raw blocks found in Textract analysis');
      return highlights;
    }

    // Create a map of block ID to block for quick lookups
    const blocksMap = new Map<string, Block>();
    rawBlocks.forEach((block: Block) => {
      if (block.Id) {
        blocksMap.set(block.Id, block);
      }
    });

    // Find all LAYOUT_* blocks (semantic containers)
    const layoutBlocks = rawBlocks.filter(
      (block: Block) =>
        block.BlockType?.startsWith('LAYOUT_') &&
        ['LAYOUT_TITLE', 'LAYOUT_HEADER', 'LAYOUT_SECTION_HEADER', 'LAYOUT_TEXT'].includes(
          block.BlockType
        )
    );

    // Sort layout blocks by position (top to bottom, left to right)
    const sortedLayoutBlocks = layoutBlocks.sort((a: Block, b: Block) => {
      if (!a.Geometry?.BoundingBox || !b.Geometry?.BoundingBox) return 0;
      const aTop = a.Geometry.BoundingBox.Top ?? 0;
      const bTop = b.Geometry.BoundingBox.Top ?? 0;
      const aLeft = a.Geometry.BoundingBox.Left ?? 0;
      const bLeft = b.Geometry.BoundingBox.Left ?? 0;
      const topDiff = aTop - bTop;
      if (Math.abs(topDiff) < 0.01) {
        return aLeft - bLeft;
      }
      return topDiff;
    });

    let highlightIndex = 0;

    for (const layoutBlock of sortedLayoutBlocks) {
      const semanticHighlight = createSemanticHighlight(
        layoutBlock,
        blocksMap,
        ocrAnalysis.id,
        highlightIndex
      );

      if (semanticHighlight && semanticHighlight.text.trim().length > 0) {
        highlights.push(semanticHighlight);
        highlightIndex++;
      }
    }
  } catch (error) {
    console.warn('Failed to parse Textract analysis data:', error);
  }

  return highlights;
}

/**
 * Create a semantic highlight from a LAYOUT_* block by combining its child LINE blocks
 */
function createSemanticHighlight(
  layoutBlock: Block,
  blocksMap: Map<string, Block>,
  analysisId: string,
  index: number
): OCRHighlight | null {
  const lineTexts: string[] = [];
  const boundingBoxes: NonNullable<Block['Geometry']>['BoundingBox'][] = [];
  const polygons: NonNullable<Block['Geometry']>['Polygon'][] = [];
  const confidences: number[] = [];
  let pageIndex = 0;

  // Map layout block type to simplified type
  const layoutType = mapTextractLayoutType(layoutBlock.BlockType || 'UNKNOWN');

  // Get the page index from the layout block itself
  if (layoutBlock.Geometry?.BoundingBox) {
    // Find the page this block belongs to by checking PAGE blocks
    for (const block of Array.from(blocksMap.values())) {
      if (block.BlockType === 'PAGE' && block.Relationships) {
        const isChildOfPage = block.Relationships.some(
          (rel) => rel.Type === 'CHILD' && layoutBlock.Id && rel.Ids?.includes(layoutBlock.Id)
        );
        if (isChildOfPage && block.Page !== undefined) {
          pageIndex = block.Page - 1; // Convert to 0-based
          break;
        }
      }
    }
  }

  // Get child LINE blocks through relationships
  if (layoutBlock.Relationships) {
    for (const relationship of layoutBlock.Relationships) {
      if (relationship.Type === 'CHILD' && relationship.Ids) {
        for (const lineId of relationship.Ids) {
          const lineBlock = blocksMap.get(lineId);
          if (lineBlock && lineBlock.BlockType === 'LINE') {
            // Add the line text
            if (lineBlock.Text) {
              lineTexts.push(lineBlock.Text);
            }

            // Add the line bounding box and polygon
            if (lineBlock.Geometry?.BoundingBox) {
              boundingBoxes.push(lineBlock.Geometry.BoundingBox);
            }
            if (lineBlock.Geometry?.Polygon) {
              polygons.push(lineBlock.Geometry.Polygon);
            }

            // Add confidence
            if (lineBlock.Confidence) {
              confidences.push(lineBlock.Confidence);
            }
          }
        }
      }
    }
  }

  // If no child lines found, skip this block
  if (lineTexts.length === 0 || boundingBoxes.length === 0) {
    return null;
  }

  // Calculate combined bounding box (encompassing all line bounding boxes)
  const combinedBoundingBox = calculateCombinedBoundingBox(boundingBoxes) ?? {
    Left: 0,
    Top: 0,
    Width: 0,
    Height: 0,
  };

  // Use the first polygon or create one from the combined bounding box
  const combinedPolygon =
    polygons.length > 0
      ? polygons[0]
      : [
          { X: combinedBoundingBox.Left ?? 0, Y: combinedBoundingBox.Top ?? 0 },
          {
            X: (combinedBoundingBox.Left ?? 0) + (combinedBoundingBox.Width ?? 0),
            Y: combinedBoundingBox.Top ?? 0,
          },
          {
            X: (combinedBoundingBox.Left ?? 0) + (combinedBoundingBox.Width ?? 0),
            Y: (combinedBoundingBox.Top ?? 0) + (combinedBoundingBox.Height ?? 0),
          },
          {
            X: combinedBoundingBox.Left ?? 0,
            Y: (combinedBoundingBox.Top ?? 0) + (combinedBoundingBox.Height ?? 0),
          },
        ];

  return {
    id: `textract-${analysisId}-${index}`,
    pageIndex,
    boundingBox: {
      left: combinedBoundingBox.Left ?? 0,
      top: combinedBoundingBox.Top ?? 0,
      width: combinedBoundingBox.Width ?? 0,
      height: combinedBoundingBox.Height ?? 0,
    },
    polygon: (combinedPolygon || []).map((point) => ({
      x: point.X || 0,
      y: point.Y || 0,
    })),
    text: lineTexts.join(' '),
    layoutType,
    confidence:
      confidences.length > 0 ? confidences.reduce((a, b) => a + b, 0) / confidences.length : 1.0,
  };
}

/**
 * Calculate a bounding box that encompasses all provided bounding boxes
 */
function calculateCombinedBoundingBox(
  boundingBoxes: NonNullable<Block['Geometry']>['BoundingBox'][]
): NonNullable<Block['Geometry']>['BoundingBox'] {
  if (boundingBoxes.length === 0) {
    return { Left: 0, Top: 0, Width: 0, Height: 0 };
  }

  if (boundingBoxes.length === 1) {
    return boundingBoxes[0];
  }

  let minLeft = Number.MAX_VALUE;
  let minTop = Number.MAX_VALUE;
  let maxRight = Number.MIN_VALUE;
  let maxBottom = Number.MIN_VALUE;

  for (const bbox of boundingBoxes) {
    if (!bbox) continue;
    minLeft = Math.min(minLeft, bbox.Left ?? 0);
    minTop = Math.min(minTop, bbox.Top ?? 0);
    maxRight = Math.max(maxRight, (bbox.Left ?? 0) + (bbox.Width ?? 0));
    maxBottom = Math.max(maxBottom, (bbox.Top ?? 0) + (bbox.Height ?? 0));
  }

  return {
    Left: minLeft,
    Top: minTop,
    Width: maxRight - minLeft,
    Height: maxBottom - minTop,
  };
}

/**
 * Map Textract layout types to simpler semantic types
 */
function mapTextractLayoutType(blockType: string): string {
  switch (blockType) {
    case 'LAYOUT_TITLE':
      return 'TITLE';
    case 'LAYOUT_HEADER':
      return 'HEADER';
    case 'LAYOUT_SECTION_HEADER':
      return 'SECTION_HEADER';
    case 'LAYOUT_TEXT':
      return 'PARAGRAPH';
    default:
      return 'TEXT';
  }
}

/**
 * Group highlights by confidence levels
 */
export function groupHighlightsByConfidence(highlights: OCRHighlight[]) {
  return {
    high: highlights.filter((h) => h.confidence >= 0.9),
    medium: highlights.filter((h) => h.confidence >= 0.7 && h.confidence < 0.9),
    low: highlights.filter((h) => h.confidence < 0.7),
  };
}

/**
 * Group highlights by layout type
 */
export function groupHighlightsByLayoutType(highlights: OCRHighlight[]) {
  const grouped: Record<string, OCRHighlight[]> = {};

  highlights.forEach((highlight) => {
    if (!grouped[highlight.layoutType]) {
      grouped[highlight.layoutType] = [];
    }
    grouped[highlight.layoutType].push(highlight);
  });

  return grouped;
}

/**
 * Filter highlights by confidence threshold
 */
export function filterHighlightsByConfidence(
  highlights: OCRHighlight[],
  minConfidence: number
): OCRHighlight[] {
  return highlights.filter((h) => h.confidence >= minConfidence);
}

/**
 * Get highlight statistics
 */
export function getHighlightStatistics(highlights: OCRHighlight[]) {
  const total = highlights.length;
  const confidenceGroups = groupHighlightsByConfidence(highlights);
  const layoutGroups = groupHighlightsByLayoutType(highlights);

  const avgConfidence =
    highlights.length > 0
      ? highlights.reduce((sum, h) => sum + h.confidence, 0) / highlights.length
      : 0;

  const uniqueLayoutTypes = Object.keys(layoutGroups);

  return {
    total,
    averageConfidence: avgConfidence,
    confidenceDistribution: {
      high: confidenceGroups.high.length,
      medium: confidenceGroups.medium.length,
      low: confidenceGroups.low.length,
    },
    layoutTypes: uniqueLayoutTypes,
    layoutTypeDistribution: Object.fromEntries(
      uniqueLayoutTypes.map((type) => [type, layoutGroups[type].length])
    ),
  };
}
