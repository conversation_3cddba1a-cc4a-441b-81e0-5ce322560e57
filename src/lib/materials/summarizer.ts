import { generateEmbeddings } from '@/lib/azure/openai';
import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { z } from 'zod';

// Schema for multi-level structured summary generation
const SummarySchema = z.object({
  // Overall document summary
  overallSummary: z.string().describe('A comprehensive overview of the entire document'),

  // Executive summary for quick understanding
  executiveSummary: z.string().describe('A concise 2-3 sentence summary of the main points'),

  // Chapter/section summaries
  chapterSummaries: z
    .array(
      z.object({
        title: z.string().describe('Chapter or section title'),
        summary: z.string().describe('Summary of this chapter/section'),
        keyPoints: z.array(z.string()).describe('Main points from this section'),
      })
    )
    .describe('Summaries of major chapters or sections'),

  // Key information extraction
  keyTopics: z.array(z.string()).describe('Main topics and themes covered'),
  keyFindings: z.array(z.string()).describe('Important findings, conclusions, or insights'),
  keyTerms: z.array(z.string()).describe('Important terms, concepts, or terminology'),

  // Document metadata
  documentType: z
    .enum(['academic', 'manual', 'report', 'article', 'book', 'guide', 'other'])
    .describe('Type of document'),
  targetAudience: z.string().describe('Intended audience for this document'),
  mainPurpose: z.string().describe('Primary purpose or goal of the document'),
});

export type DocumentSummaryResult = z.infer<typeof SummarySchema>;

/**
 * Generate a comprehensive summary of a document from its chunks with enhanced prompt
 * @param chunks Array of document chunks containing the content
 * @param fileName Original file name for context
 * @param debug Whether to enable debug logging
 * @returns Generated summary and its embedding
 */
export async function generateDocumentSummary(
  chunks: Array<{ content: string; chunkOrder: number }>,
  fileName: string,
  debug = false
): Promise<{
  summary: string;
  summaryEmbedding: number[] | null;
  keyTopics: string[];
  documentType: string;
}> {
  if (debug) {
    console.log(`🔍 Generating enhanced summary for document: ${fileName}`);
  }

  // Combine all chunks into a single text, respecting order
  const sortedChunks = chunks.sort((a, b) => a.chunkOrder - b.chunkOrder);
  const fullText = sortedChunks.map((chunk) => chunk.content).join('\n\n');

  // For very long documents, we need to be strategic about processing
  const maxLength = 80000; // Increased limit for better analysis
  const processedText =
    fullText.length > maxLength ? fullText.substring(0, maxLength) + '...' : fullText;

  try {
    // Generate enhanced structured summary using AI with best practice prompt
    const result = await generateObject({
      model: google('gemini-2.5-flash'),
      schema: SummarySchema,
      prompt: `
        You are an expert document analyst. Analyze the following document and provide a comprehensive, multi-level summary.

        IMPORTANT GUIDELINES:
        - Focus ONLY on the content provided in the document
        - Do NOT add external opinions, interpretations, or knowledge not present in the text
        - Be objective and factual, summarizing what the document actually says
        - Maintain the author's perspective and tone without adding your own commentary
        - If the document has clear chapters/sections, identify and summarize them separately

        Document Name: ${fileName}

        Content:
        ${processedText}

        Please provide a structured analysis with:

        1. OVERALL SUMMARY: A comprehensive overview covering the main themes, arguments, and conclusions presented in the document

        2. EXECUTIVE SUMMARY: A concise 2-3 sentence summary that captures the core message

        3. CHAPTER/SECTION SUMMARIES: If the document has clear divisions (chapters, sections, parts), provide:
           - Title of each major section
           - Summary of that section's content
           - Key points from that section

        4. KEY INFORMATION EXTRACTION:
           - Main topics and themes discussed
           - Important findings, conclusions, or insights presented by the author
           - Significant terms, concepts, or terminology defined or used

        5. DOCUMENT METADATA:
           - Type of document (academic, manual, report, article, book, guide, other)
           - Intended audience based on writing style and content
           - Primary purpose or goal of the document

        Remember: Summarize what IS in the document, not what you think SHOULD be there.
      `,
    });

    if (debug) {
      console.log(
        `✅ Generated enhanced summary for ${fileName}: ${result.object.overallSummary.substring(0, 100)}...`
      );
      console.log(`📊 Found ${result.object.chapterSummaries.length} chapters/sections`);
    }

    // Create a combined summary for storage and embedding generation
    const combinedSummary = `
DOCUMENT: ${fileName}

EXECUTIVE SUMMARY:
${result.object.executiveSummary}

OVERALL SUMMARY:
${result.object.overallSummary}

CHAPTER SUMMARIES:
${result.object.chapterSummaries
  .map((chapter) => `${chapter.title}: ${chapter.summary}`)
  .join('\n\n')}

KEY FINDINGS:
${result.object.keyFindings.join('; ')}

KEY TOPICS: ${result.object.keyTopics.join(', ')}

DOCUMENT TYPE: ${result.object.documentType}
TARGET AUDIENCE: ${result.object.targetAudience}
MAIN PURPOSE: ${result.object.mainPurpose}
`.trim();

    // Generate embedding for the combined summary
    // TODO: Uncomment this when we have a way to generate embeddings
    // const [summaryEmbedding] = await generateEmbeddings([combinedSummary], 1, debug);
    const summaryEmbedding = null;

    return {
      summary: combinedSummary,
      summaryEmbedding,
      keyTopics: result.object.keyTopics,
      documentType: result.object.documentType,
    };
  } catch (error) {
    console.error(`❌ Error generating enhanced summary for ${fileName}:`, error);

    // Fallback: create a simple summary from first few chunks
    const fallbackSummary = `Document: ${fileName}\n\nContent preview:\n${sortedChunks
      .slice(0, 3)
      .map((chunk) => chunk.content.substring(0, 200))
      .join('\n\n')}`;

    const [fallbackEmbedding] = await generateEmbeddings([fallbackSummary], 1, debug);

    return {
      summary: fallbackSummary,
      summaryEmbedding: fallbackEmbedding,
      keyTopics: ['document', 'content'],
      documentType: 'other',
    };
  }
}

/**
 * Batch process existing documents to generate summaries
 * This function can be used to retroactively add summaries to existing documents
 */
export async function batchGenerateSummaries(
  documents: Array<{
    id: string;
    fileName: string;
    chunks: Array<{ content: string; chunkOrder: number }>;
  }>,
  debug = false
): Promise<
  Array<{
    documentId: string;
    summary: string;
    summaryEmbedding: number[] | null;
    keyTopics: string[];
    documentType: string;
  }>
> {
  if (debug) {
    console.log(`🔄 Starting batch summary generation for ${documents.length} documents`);
  }

  const results = [];

  for (const doc of documents) {
    try {
      const summaryResult = await generateDocumentSummary(doc.chunks, doc.fileName, debug);

      results.push({
        documentId: doc.id,
        ...summaryResult,
      });

      if (debug) {
        console.log(`✅ Processed ${doc.fileName}`);
      }

      // Add a small delay to avoid rate limiting
      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`❌ Failed to process ${doc.fileName}:`, error);
    }
  }

  if (debug) {
    console.log(`🎉 Batch processing complete. Generated ${results.length} summaries.`);
  }

  return results;
}
