import { db } from '@/database/drizzle';
import { documentChunks, documents, documentSpaces, spaces } from '@/database/schema';
import { generateEmbeddings } from '@/lib/azure/openai';
import type { MaterialSearchResult } from '@/types/material';
import { FinderMode } from '@/types/search';
import { and, cosineDistance, eq, gt, inArray, sql } from 'drizzle-orm';

type BaseSearchOptions = {
  query: string;
  limit?: number;
  offset?: number;
  spaceIds?: string[];
  documentIds?: string[];
  userId?: string;
  debug?: boolean;
};

type SearchResultsWithCount = {
  results: MaterialSearchResult[];
  totalCount: number;
};

type EmbeddingSearchOptions = BaseSearchOptions & {
  similarityThreshold?: number;
};

type FullTextSearchOptions = BaseSearchOptions & {
  language?: string;
};

type SearchMaterialsOptions = BaseSearchOptions & {
  mode?: FinderMode;
};

export async function searchMaterials({
  query,
  mode = 'embedding',
  limit = 10,
  spaceIds,
  documentIds,
  userId,
  debug = false,
}: SearchMaterialsOptions): Promise<MaterialSearchResult[]> {
  if (mode === 'embedding') {
    return searchMaterialsByEmbedding({ query, limit, spaceIds, documentIds, userId, debug });
  } else {
    return searchMaterialsByFullText({ query, limit, spaceIds, documentIds, userId, debug });
  }
}

export async function searchMaterialsWithCount({
  query,
  mode = 'embedding',
  limit = 20,
  offset = 0,
  spaceIds,
  documentIds,
  userId,
  debug = false,
}: SearchMaterialsOptions & { offset?: number }): Promise<SearchResultsWithCount> {
  if (mode === 'embedding') {
    return searchMaterialsByEmbeddingWithCount({
      query,
      limit,
      offset,
      spaceIds,
      documentIds,
      userId,
      debug,
    });
  } else {
    return searchMaterialsByFullTextWithCount({
      query,
      limit,
      offset,
      spaceIds,
      documentIds,
      userId,
      debug,
    });
  }
}

async function searchMaterialsByEmbedding({
  query,
  limit = 10,
  spaceIds,
  documentIds,
  userId,
  similarityThreshold = 0.1,
  debug = false,
}: EmbeddingSearchOptions): Promise<MaterialSearchResult[]> {
  if (debug) {
    console.log(`🔍 Generating embedding for search query: "${query}"`);
  }
  const embedStartTime = performance.now();

  // For search queries, we're only processing one item, so use a single batch
  const [queryEmbedding] = await generateEmbeddings([query], 1, debug);

  if (debug) {
    const embedEndTime = performance.now();
    console.log(
      `✅ Search query embedding generated in ${((embedEndTime - embedStartTime) / 1000).toFixed(2)}s`
    );
  }

  const similarity = sql<number>`1 - (${cosineDistance(documentChunks.embedding, queryEmbedding)})`;

  // Build conditions including space/document filtering
  const conditions = [gt(similarity, similarityThreshold)];

  // Add document ID filter if provided (direct SQL filtering)
  if (documentIds?.length) {
    conditions.push(inArray(documents.id, documentIds));
  }

  try {
    let query = db
      .select({
        id: documents.id,
        chunkId: documentChunks.id,
        fileName: documents.fileName,
        content: documentChunks.content,
        key: documents.s3Key,
        similarity: similarity,
        fileType: documents.fileType,
        metadata: documentChunks.metadata,
        uploadedBy: documents.uploadedBy,
      })
      .from(documentChunks)
      .innerJoin(documents, eq(documentChunks.documentId, documents.id));

    // Apply space filtering directly in SQL if specific spaces are requested
    if (spaceIds && spaceIds.length > 0) {
      if (debug) {
        console.log(`🔍 [EMBEDDING] Applying SQL space filter for spaces: ${spaceIds.join(', ')}`);
      }

      // Join with documentSpaces and spaces tables to filter by space
      query = query
        .innerJoin(documentSpaces, eq(documents.id, documentSpaces.documentId))
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id));

      // Add space filtering conditions
      conditions.push(inArray(documentSpaces.spaceId, spaceIds));
      conditions.push(eq(spaces.isShared, true));

      if (debug) {
        console.log(`📋 [EMBEDDING] Added space filtering conditions to SQL query`);
      }
    } else if (userId) {
      // When no specific spaces requested, apply general access control
      // This is more complex, so we'll handle it with a subquery approach
      if (debug) {
        console.log(`🔍 Applying general access control for user: ${userId}`);
      }

      // Add condition to include documents owned by user OR in accessible spaces
      const accessibleDocumentsSubquery = db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(eq(spaces.isShared, true));

      conditions.push(
        sql`(${documents.uploadedBy} = ${userId} OR ${documents.id} IN (${accessibleDocumentsSubquery}))`
      );
    }

    // Apply all conditions and execute query
    const results = await query.where(and(...conditions));

    if (debug) {
      console.log(`✅ Found ${results.length} results after SQL filtering`);
    }

    // Sort and limit results
    return (
      results
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        .map(({ uploadedBy, ...rest }) => rest)
    ); // Remove uploadedBy from results
  } catch (error) {
    console.error('Error in searchMaterialsByEmbedding:', error);
    return [];
  }
}

async function searchMaterialsByFullText({
  query,
  limit = 10,
  spaceIds,
  documentIds,
  userId,
  language = 'simple',
  debug = false,
}: FullTextSearchOptions): Promise<MaterialSearchResult[]> {
  const cleanedQuery = cleanQuery(query);

  const searchQuery = sql`to_tsquery(${language}::regconfig, ${cleanedQuery})`;
  const rank =
    sql<number>`ts_rank(to_tsvector(${language}::regconfig, ${documentChunks.content}), ${searchQuery})`.as(
      'rank'
    );

  // Build conditions including space/document filtering
  const conditions = [
    sql`to_tsvector(${language}::regconfig, ${documentChunks.content}) @@ ${searchQuery}`,
  ];

  // Add document ID filter if provided (direct SQL filtering)
  if (documentIds?.length) {
    conditions.push(inArray(documents.id, documentIds));
  }

  try {
    let query = db
      .select({
        id: documents.id,
        chunkId: documentChunks.id,
        fileName: documents.fileName,
        content: documentChunks.content,
        key: documents.s3Key,
        similarity: rank,
        fileType: documents.fileType,
        metadata: documentChunks.metadata,
        uploadedBy: documents.uploadedBy,
      })
      .from(documentChunks)
      .innerJoin(documents, eq(documentChunks.documentId, documents.id));

    // Apply space filtering directly in SQL if specific spaces are requested
    if (spaceIds && spaceIds.length > 0) {
      if (debug) {
        console.log(`🔍 [FULLTEXT] Applying SQL space filter for spaces: ${spaceIds.join(', ')}`);
      }

      // Join with documentSpaces and spaces tables to filter by space
      query = query
        .innerJoin(documentSpaces, eq(documents.id, documentSpaces.documentId))
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id));

      // Add space filtering conditions
      conditions.push(inArray(documentSpaces.spaceId, spaceIds));
      conditions.push(eq(spaces.isShared, true));

      if (debug) {
        console.log(`📋 [FULLTEXT] Added space filtering conditions to SQL query`);
      }
    } else if (userId) {
      // When no specific spaces requested, apply general access control
      if (debug) {
        console.log(
          `🔍 [EMBEDDING] No space filter - applying general access control for user: ${userId}`
        );
      }

      // Add condition to include documents owned by user OR in accessible spaces
      const accessibleDocumentsSubquery = db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(eq(spaces.isShared, true));

      conditions.push(
        sql`(${documents.uploadedBy} = ${userId} OR ${documents.id} IN (${accessibleDocumentsSubquery}))`
      );
    }

    // Apply all conditions and execute query
    const results = await query.where(and(...conditions));

    if (debug) {
      console.log(`✅ Found ${results.length} results after SQL filtering`);
    }

    // Sort and limit results
    return (
      results
        .sort((a, b) => (b.similarity as number) - (a.similarity as number))
        .slice(0, limit)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        .map(({ uploadedBy, ...rest }) => rest)
    ); // Remove uploadedBy from results
  } catch (error) {
    console.error('Error in searchMaterialsByFullText:', error);
    return [];
  }
}

function cleanQuery(query: string): string {
  // Clean and format the query:
  // 1. Replace special characters with spaces
  // 2. Split into words
  // 3. Remove empty strings
  // 4. Join with ' & ' for AND operations
  const cleanedQuery = query
    .replace(/['"(),:;&|!?]/g, ' ')
    .split(/\s+/)
    .filter(Boolean)
    .join(' & ');

  return cleanedQuery;
}

// New functions that return both results and total count for pagination
async function searchMaterialsByEmbeddingWithCount({
  query,
  limit = 20,
  offset = 0,
  spaceIds,
  documentIds,
  userId,
  similarityThreshold = 0.1,
  debug = false,
}: EmbeddingSearchOptions & { offset?: number }): Promise<SearchResultsWithCount> {
  if (debug) {
    console.log(
      `🔍 Generating embedding for search query: "${query}" (limit: ${limit}, offset: ${offset})`
    );
  }
  const embedStartTime = performance.now();

  const [queryEmbedding] = await generateEmbeddings([query], 1, debug);

  if (debug) {
    const embedEndTime = performance.now();
    console.log(
      `✅ Search query embedding generated in ${((embedEndTime - embedStartTime) / 1000).toFixed(2)}s`
    );
  }

  const similarity = sql<number>`1 - (${cosineDistance(documentChunks.embedding, queryEmbedding)})`;

  // Build conditions including space/document filtering
  const conditions = [gt(similarity, similarityThreshold)];

  // Add document ID filter if provided (direct SQL filtering)
  if (documentIds?.length) {
    conditions.push(inArray(documents.id, documentIds));
  }

  try {
    let query_builder = db
      .select({
        id: documents.id,
        chunkId: documentChunks.id,
        fileName: documents.fileName,
        content: documentChunks.content,
        key: documents.s3Key,
        similarity: similarity,
        fileType: documents.fileType,
        metadata: documentChunks.metadata,
        uploadedBy: documents.uploadedBy,
      })
      .from(documentChunks)
      .innerJoin(documents, eq(documentChunks.documentId, documents.id));

    // Apply space filtering directly in SQL if specific spaces are requested
    if (spaceIds && spaceIds.length > 0) {
      if (debug) {
        console.log(`🔍 [EMBEDDING] Applying SQL space filter for spaces: ${spaceIds.join(', ')}`);
      }

      query_builder = query_builder
        .innerJoin(documentSpaces, eq(documents.id, documentSpaces.documentId))
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id));

      conditions.push(inArray(documentSpaces.spaceId, spaceIds));
      conditions.push(eq(spaces.isShared, true));

      if (debug) {
        console.log(`📋 [EMBEDDING] Added space filtering conditions to SQL query`);
      }
    } else if (userId) {
      if (debug) {
        console.log(
          `🔍 [EMBEDDING] No space filter - applying general access control for user: ${userId}`
        );
      }

      const accessibleDocumentsSubquery = db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(eq(spaces.isShared, true));

      conditions.push(
        sql`(${documents.uploadedBy} = ${userId} OR ${documents.id} IN (${accessibleDocumentsSubquery}))`
      );
    }

    // Get total count first
    const countQuery = db
      .select({ count: sql<number>`count(*)` })
      .from(documentChunks)
      .innerJoin(documents, eq(documentChunks.documentId, documents.id));

    // Apply same joins and conditions for count
    if (spaceIds && spaceIds.length > 0) {
      countQuery
        .innerJoin(documentSpaces, eq(documents.id, documentSpaces.documentId))
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id));
    }

    const [{ count: totalCount }] = await countQuery.where(and(...conditions));

    // Get paginated results
    const results = await query_builder
      .where(and(...conditions))
      .orderBy(sql`${similarity} DESC`)
      .limit(limit)
      .offset(offset);

    if (debug) {
      console.log(`✅ Found ${results.length} results (${totalCount} total) after SQL filtering`);
    }

    return {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      results: results.map(({ uploadedBy, ...rest }) => rest),
      totalCount,
    };
  } catch (error) {
    console.error('Error in searchMaterialsByEmbeddingWithCount:', error);
    return { results: [], totalCount: 0 };
  }
}

async function searchMaterialsByFullTextWithCount({
  query,
  limit = 20,
  offset = 0,
  spaceIds,
  documentIds,
  userId,
  language = 'simple',
  debug = false,
}: FullTextSearchOptions & { offset?: number }): Promise<SearchResultsWithCount> {
  const cleanedQuery = cleanQuery(query);

  const searchQuery = sql`to_tsquery(${language}::regconfig, ${cleanedQuery})`;
  const rank =
    sql<number>`ts_rank(to_tsvector(${language}::regconfig, ${documentChunks.content}), ${searchQuery})`.as(
      'rank'
    );

  // Build conditions including space/document filtering
  const conditions = [
    sql`to_tsvector(${language}::regconfig, ${documentChunks.content}) @@ ${searchQuery}`,
  ];

  // Add document ID filter if provided (direct SQL filtering)
  if (documentIds?.length) {
    conditions.push(inArray(documents.id, documentIds));
  }

  try {
    let query_builder = db
      .select({
        id: documents.id,
        chunkId: documentChunks.id,
        fileName: documents.fileName,
        content: documentChunks.content,
        key: documents.s3Key,
        similarity: rank,
        fileType: documents.fileType,
        metadata: documentChunks.metadata,
        uploadedBy: documents.uploadedBy,
      })
      .from(documentChunks)
      .innerJoin(documents, eq(documentChunks.documentId, documents.id));

    // Apply space filtering directly in SQL if specific spaces are requested
    if (spaceIds && spaceIds.length > 0) {
      if (debug) {
        console.log(`🔍 [FULLTEXT] Applying SQL space filter for spaces: ${spaceIds.join(', ')}`);
      }

      query_builder = query_builder
        .innerJoin(documentSpaces, eq(documents.id, documentSpaces.documentId))
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id));

      conditions.push(inArray(documentSpaces.spaceId, spaceIds));
      conditions.push(eq(spaces.isShared, true));

      if (debug) {
        console.log(`📋 [FULLTEXT] Added space filtering conditions to SQL query`);
      }
    } else if (userId) {
      if (debug) {
        console.log(
          `🔍 [FULLTEXT] No space filter - applying general access control for user: ${userId}`
        );
      }

      const accessibleDocumentsSubquery = db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(eq(spaces.isShared, true));

      conditions.push(
        sql`(${documents.uploadedBy} = ${userId} OR ${documents.id} IN (${accessibleDocumentsSubquery}))`
      );
    }

    // Get total count first
    const countQuery = db
      .select({ count: sql<number>`count(*)` })
      .from(documentChunks)
      .innerJoin(documents, eq(documentChunks.documentId, documents.id));

    // Apply same joins and conditions for count
    if (spaceIds && spaceIds.length > 0) {
      countQuery
        .innerJoin(documentSpaces, eq(documents.id, documentSpaces.documentId))
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id));
    }

    const [{ count: totalCount }] = await countQuery.where(and(...conditions));

    // Get paginated results
    const results = await query_builder
      .where(and(...conditions))
      .orderBy(sql`${rank} DESC`)
      .limit(limit)
      .offset(offset);

    if (debug) {
      console.log(`✅ Found ${results.length} results (${totalCount} total) after SQL filtering`);
    }

    return {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      results: results.map(({ uploadedBy, ...rest }) => rest),
      totalCount,
    };
  } catch (error) {
    console.error('Error in searchMaterialsByFullTextWithCount:', error);
    return { results: [], totalCount: 0 };
  }
}
