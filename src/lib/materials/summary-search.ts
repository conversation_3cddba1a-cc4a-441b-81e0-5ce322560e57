import { db } from '@/database/drizzle';
import { documents, documentSpaces, spaces } from '@/database/schema';
import type { DocumentSummary } from '@/lib/langgraph/types';
import { and, eq, inArray, isNotNull, sql } from 'drizzle-orm';

type SummarySearchOptions = {
  query: string;
  limit?: number;
  spaceIds?: string[];
  documentIds?: string[];
  userId?: string;
  similarityThreshold?: number;
  debug?: boolean;
};

/**
 * Search document summaries using keyword-based search
 * This provides high-level document matching for broader context
 * Using keyword search since summary embeddings are not yet implemented
 */
export async function searchDocumentSummaries({
  query,
  limit = 10,
  spaceIds,
  documentIds,
  userId,
  debug = false,
}: Omit<SummarySearchOptions, 'similarityThreshold'>): Promise<DocumentSummary[]> {
  if (debug) {
    console.log(`🔍 Searching document summaries for: "${query}"`);
  }

  // Extract keywords from the query for text search
  const keywords = query
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter((word) => word.length > 2)
    .slice(0, 5); // Take first 5 meaningful words

  if (debug) {
    console.log(`🎯 Using keywords for summary search: ${keywords.join(', ')}`);
  }

  // Create a search query from keywords
  const searchTerms = keywords
    .map((keyword) => {
      // Escape and clean keywords for tsquery
      const cleaned = keyword.replace(/[|&!()]/g, '').trim();
      return cleaned;
    })
    .filter((term) => term && term.length > 0)
    .join(' | ');

  const tsQuery = sql`to_tsquery('simple', ${searchTerms})`;

  // Build base conditions for keyword search
  const baseConditions = [
    sql`to_tsvector('simple', ${documents.summary}) @@ ${tsQuery}`,
    isNotNull(documents.summary), // Only include documents with summaries
  ];

  // Add document ID filter if provided
  if (documentIds?.length) {
    baseConditions.push(inArray(documents.id, documentIds));
  }

  try {
    // Get base results first without user/space filtering
    const baseQuery = db
      .select({
        id: documents.id,
        fileName: documents.fileName,
        summary: documents.summary,
        key: documents.s3Key,
        fileType: documents.fileType,
        uploadedBy: documents.uploadedBy,
        // Calculate text search rank as similarity score
        rank: sql<number>`ts_rank(to_tsvector('simple', ${documents.summary}), ${tsQuery})`,
      })
      .from(documents)
      .where(and(...baseConditions));

    // Execute the query
    let results = await baseQuery;

    // Apply space and user filtering
    if (spaceIds && spaceIds.length > 0) {
      // When specific spaces are requested, ONLY return documents from those spaces
      // Get document IDs from the requested spaces
      const spaceDocumentsResult = await db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(
          and(
            inArray(documentSpaces.spaceId, spaceIds),
            eq(spaces.isShared, true) // Only include shared spaces
          )
        );

      const documentsInSpaces = spaceDocumentsResult.map((row) => row.documentId);

      // Filter to ONLY documents in the specified spaces
      results = results.filter((doc) => documentsInSpaces.includes(doc.id));
    } else if (userId) {
      // When no specific spaces are requested, apply general access control
      // Include documents owned by user or in any accessible spaces
      const allAccessibleSpacesResult = await db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(eq(spaces.isShared, true));

      const documentsInAccessibleSpaces = allAccessibleSpacesResult.map((row) => row.documentId);

      results = results.filter((doc) => {
        const isOwnedByUser = doc.uploadedBy === userId;
        const isInAccessibleSpace = documentsInAccessibleSpaces.includes(doc.id);
        return isOwnedByUser || isInAccessibleSpace;
      });
    }

    // Sort by rank and limit results
    const finalResults = results
      .sort((a, b) => b.rank - a.rank)
      .slice(0, limit)
      .map((doc) => ({
        id: doc.id,
        fileName: doc.fileName,
        summary: doc.summary || '',
        similarity: doc.rank, // Use rank as similarity score
        key: doc.key,
        fileType: doc.fileType,
      }));

    if (debug) {
      console.log(`📄 Found ${finalResults.length} document summaries`);
    }

    return finalResults;
  } catch (error) {
    console.error('Error in searchDocumentSummaries:', error);
    return [];
  }
}

/**
 * Get summary for a specific document by ID
 */
export async function getDocumentSummary(documentId: string): Promise<DocumentSummary | null> {
  try {
    const result = await db
      .select({
        id: documents.id,
        fileName: documents.fileName,
        summary: documents.summary,
        key: documents.s3Key,
        fileType: documents.fileType,
      })
      .from(documents)
      .where(and(eq(documents.id, documentId), isNotNull(documents.summary)))
      .limit(1);

    if (result.length === 0) {
      return null;
    }

    const doc = result[0];
    return {
      id: doc.id,
      fileName: doc.fileName,
      summary: doc.summary || '',
      key: doc.key,
      fileType: doc.fileType,
    };
  } catch (error) {
    console.error('Error in getDocumentSummary:', error);
    return null;
  }
}

/**
 * Search summaries with keyword matching for fallback scenarios
 */
export async function searchSummariesByKeywords(
  keywords: string[],
  options: Omit<SummarySearchOptions, 'query'>
): Promise<DocumentSummary[]> {
  const { limit = 10, spaceIds, documentIds, userId, debug = false } = options;

  if (debug) {
    console.log(`🔍 Searching summaries by keywords: ${keywords.join(', ')}`);
  }

  // Create a search query from keywords
  // Escape and quote keywords properly for tsquery
  const searchTerms = keywords
    .map((keyword) => {
      // Replace spaces with & for phrase matching and escape special characters
      const cleaned = keyword
        .toLowerCase()
        .replace(/[|&!()]/g, ' ')
        .trim();
      // Split multi-word phrases and join with &
      const words = cleaned.split(/\s+/).filter((word) => word.length > 0);
      return words.length > 1 ? `(${words.join(' & ')})` : words[0];
    })
    .filter((term) => term && term.length > 0)
    .join(' | ');

  const tsQuery = sql`to_tsquery('simple', ${searchTerms})`;

  // Build base conditions for keyword search
  const baseConditions = [
    sql`to_tsvector('simple', ${documents.summary}) @@ ${tsQuery}`,
    isNotNull(documents.summary),
  ];

  // Add document ID filter if provided
  if (documentIds?.length) {
    baseConditions.push(inArray(documents.id, documentIds));
  }

  try {
    const baseQuery = db
      .select({
        id: documents.id,
        fileName: documents.fileName,
        summary: documents.summary,
        key: documents.s3Key,
        fileType: documents.fileType,
        uploadedBy: documents.uploadedBy,
        // Calculate text search rank
        rank: sql<number>`ts_rank(to_tsvector('simple', ${documents.summary}), ${tsQuery})`,
      })
      .from(documents)
      .where(and(...baseConditions));

    let results = await baseQuery;

    // Apply space and user filtering
    if (spaceIds && spaceIds.length > 0) {
      // When specific spaces are requested, ONLY return documents from those spaces
      // Get document IDs from the requested spaces
      const spaceDocumentsResult = await db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(and(inArray(documentSpaces.spaceId, spaceIds), eq(spaces.isShared, true)));

      const documentsInSpaces = spaceDocumentsResult.map((row) => row.documentId);

      // Filter to ONLY documents in the specified spaces
      results = results.filter((doc) => documentsInSpaces.includes(doc.id));
    } else if (userId) {
      // When no specific spaces are requested, apply general access control
      // Include documents owned by user or in any accessible spaces
      const allAccessibleSpacesResult = await db
        .select({ documentId: documentSpaces.documentId })
        .from(documentSpaces)
        .innerJoin(spaces, eq(documentSpaces.spaceId, spaces.id))
        .where(eq(spaces.isShared, true));

      const documentsInAccessibleSpaces = allAccessibleSpacesResult.map((row) => row.documentId);

      results = results.filter((doc) => {
        const isOwnedByUser = doc.uploadedBy === userId;
        const isInAccessibleSpace = documentsInAccessibleSpaces.includes(doc.id);
        return isOwnedByUser || isInAccessibleSpace;
      });
    }

    // Sort by rank and limit
    const finalResults = results
      .sort((a, b) => b.rank - a.rank)
      .slice(0, limit)
      .map(({ rank, ...doc }) => ({
        id: doc.id,
        fileName: doc.fileName,
        summary: doc.summary || '',
        similarity: rank, // Use rank as similarity score
        key: doc.key,
        fileType: doc.fileType,
      }));

    if (debug) {
      console.log(`📄 Found ${finalResults.length} summaries by keywords`);
    }

    return finalResults;
  } catch (error) {
    console.error('Error in searchSummariesByKeywords:', error);
    return [];
  }
}
