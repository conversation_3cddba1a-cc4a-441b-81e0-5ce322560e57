import { DocumentType } from '@/types/material';

export function getDocumentType(fileType: string): DocumentType {
  if (fileType.includes('pdf')) {
    return DocumentType.PDF;
  } else if (fileType.includes('word') || fileType.includes('docx')) {
    return DocumentType.DOCX;
  } else if (fileType.includes('text/plain')) {
    return DocumentType.TXT;
  } else {
    return DocumentType.OTHER;
  }
}

export function getMimeTypeFromDocumentType(documentType: DocumentType): string {
  switch (documentType) {
    case DocumentType.PDF:
      return 'application/pdf';
    case DocumentType.DOCX:
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case DocumentType.TXT:
      return 'text/plain';
    default:
      return 'application/octet-stream';
  }
}

export function getDocumentPreviewUrl(key: string) {
  // Use the same URL pattern as the material page
  // NEXT_PUBLIC_ variables are available on both client and server
  const baseUrl = process.env.NEXT_PUBLIC_MATERIAL_ACCESS_URL;
  if (!baseUrl) {
    console.error('NEXT_PUBLIC_MATERIAL_ACCESS_URL is not defined');
    return '';
  }
  const fullUrl = `${baseUrl}/${key}`;
  console.log('Generated document URL:', fullUrl, 'for key:', key);
  return fullUrl;
}
