import { createEnv } from '@t3-oss/env-nextjs';
import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';
import { z } from 'zod';

dotenvExpand.expand(dotenv.config());

export const env = createEnv({
  server: {
    NODE_ENV: z.enum(['development', 'production']).default('development'),
    // Mock system configuration
    USE_MOCKS: z.string().optional().default('false'),
    // AI testing configuration - when true, uses mock responses instead of real AI calls
    USE_AI_TESTING: z.string().optional().default('false'),
    AZURE_OPENAI_ENDPOINT: z.string().url(),
    AZURE_OPENAI_RESOURCE_NAME: z.string().min(1),
    AZURE_OPENAI_API_KEY: z.string().min(1),
    GOOGLE_GENERATIVE_AI_API_KEY: z.string().min(1),
    CLOUDFLARE_ACCOUNT_ID: z.string().min(1),
    CLOUDFLARE_ACCESS_KEY_ID: z.string().min(1),
    CLOUDFLARE_SECRET_ACCESS_KEY: z.string().min(1),
    CLOUDFLARE_BUCKET_NAME: z.string().min(1),
    AWS_REGION: z.string().min(1).optional(),
    AWS_ACCESS_KEY_ID: z.string().min(1),
    AWS_SECRET_ACCESS_KEY: z.string().min(1),
    // AWS S3 for Textract async processing
    AWS_S3_BUCKET: z.string().min(1).optional(),
    AWS_S3_REGION: z.string().min(1).optional(),
    // AWS SNS for Textract completion notifications
    AWS_SNS_TOPIC_ARN: z.string().min(1).optional(),
    AWS_ROLE_ARN: z.string().min(1).optional(),
    DATABASE_URL: z.string().url(),
    RESEND_API_KEY: z.string().min(1),
    FROM_EMAIL: z.string().email(),
    QSTASH_URL: z.string().url(),
    QSTASH_TOKEN: z.string().min(1),
    // Add other environment variables as needed
  },
  client: {
    NEXT_PUBLIC_APP_ENV: z.enum(['development', 'production']).default('development'),
    NEXT_PUBLIC_APP_URL: z.string(),
    NEXT_PUBLIC_SERVER_URL: z.string(),
    NEXT_PUBLIC_MATERIAL_ACCESS_URL: z.string().url(),
    // Add client-side environment variables if needed
  },
  // runtimeEnv: {
  //   AZURE_OPENAI_ENDPOINT: process.env.AZURE_OPENAI_ENDPOINT,
  //   AZURE_OPENAI_API_KEY: process.env.AZURE_OPENAI_API_KEY,
  //   CLOUDFLARE_ACCESS_KEY_ID: process.env.CLOUDFLARE_ACCESS_KEY_ID,
  //   CLOUDFLARE_SECRET_ACCESS_KEY: process.env.CLOUDFLARE_SECRET_ACCESS_KEY,
  //   // Add other environment variables as needed
  // },
});
