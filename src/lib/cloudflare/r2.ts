import { env } from '@/lib/env.mjs';
import { DeleteObjectCommand, PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export const R2_BUCKET_NAME = 'your-bucket-name';

export const r2Client = new S3Client({
  endpoint: `https://${env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  region: 'auto',
  credentials: {
    accessKeyId: env.CLOUDFLARE_ACCESS_KEY_ID,
    secretAccessKey: env.CLOUDFLARE_SECRET_ACCESS_KEY,
  },
});

export async function uploadToR2(file: File, fileKey: string): Promise<string> {
  const fileBuffer = await file.arrayBuffer();

  const command = new PutObjectCommand({
    Bucket: env.CLOUDFLARE_BUCKET_NAME,
    Key: fileKey,
    Body: Buffer.from(fileBuffer),
    ContentType: file.type,
  });

  try {
    await r2Client.send(command);

    const publicUrl = `https://${env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com/${env.CLOUDFLARE_BUCKET_NAME}/${fileKey}`;
    return publicUrl;
  } catch (error) {
    console.error('Error uploading file to R2:', error);
    throw new Error('Failed to upload file to R2');
  }
}

export async function deleteFromR2(fileKey: string): Promise<void> {
  const command = new DeleteObjectCommand({
    Bucket: env.CLOUDFLARE_BUCKET_NAME,
    Key: fileKey,
  });

  try {
    await r2Client.send(command);
  } catch (error) {
    console.error('Error deleting file from R2:', error);
    throw new Error('Failed to delete file from R2');
  }
}

export async function getPresignedUrl(fileKey: string, contentType: string): Promise<string> {
  try {
    const command = new PutObjectCommand({
      Bucket: env.CLOUDFLARE_BUCKET_NAME,
      Key: fileKey,
      ContentType: contentType,
    });

    // Create presigned URL with 15-minute expiration
    const presignedUrl = await getSignedUrl(r2Client, command, { expiresIn: 15 * 60 });
    return presignedUrl;
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    throw new Error('Failed to generate presigned URL');
  }
}
