import { Resend } from 'resend';
import { env } from './env.mjs';

const resend = new Resend(env.RESEND_API_KEY);
const fromEmail = env.FROM_EMAIL;
const baseUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL
  ? `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`
  : env.NEXT_PUBLIC_APP_URL;

export const EmailService = {
  async sendVerificationEmail(email: string, name: string, token: string) {
    const verificationLink = `${baseUrl}/auth/verify-email?token=${token}`;

    return await resend.emails.send({
      from: fromEmail,
      to: email,
      subject: 'Verify your email address',
      html: `
        <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Welcome to Knowledge Sphere!</h1>
          <p>Hello ${name},</p>
          <p>Thank you for signing up. Please verify your email address by clicking the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationLink}" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
          </div>
          <p>If you didn't create an account with us, you can safely ignore this email.</p>
          <p>This link will expire in 24 hours.</p>
          <p>Regards,<br>The Knowledge Sphere Team</p>
        </div>
      `,
    });
  },

  async sendPasswordResetEmail(email: string, token: string) {
    const resetLink = `${baseUrl}/auth/reset-password?token=${token}`;

    return await resend.emails.send({
      from: fromEmail,
      to: email,
      subject: 'Reset your password',
      html: `
        <div style="font-family: sans-serif; max-width: 600px; margin: 0 auto;">
          <h1>Password Reset Request</h1>
          <p>Hello,</p>
          <p>We received a request to reset your password. Click the button below to reset it:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetLink}" style="background-color: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
          </div>
          <p>If you didn't request a password reset, you can safely ignore this email.</p>
          <p>This link will expire in 1 hour.</p>
          <p>Regards,<br>The Knowledge Sphere Team</p>
        </div>
      `,
    });
  },
};
