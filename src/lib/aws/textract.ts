import { env } from '@/lib/env.mjs';
import {
  AnalyzeDocumentCommand,
  Block,
  FeatureType,
  Geometry,
  GetDocumentAnalysisCommand,
  GetDocumentAnalysisResponse,
  JobStatus,
  Point,
  StartDocumentAnalysisCommand,
  StartDocumentAnalysisResponse,
  TextractClient,
} from '@aws-sdk/client-textract';
import { PDFDocument } from 'pdf-lib';

export const textractClient = new TextractClient({
  // DEBUG: using localstack for local development
  // endpoint: 'localhost.localstack.cloud:4566',
  region: env.AWS_S3_REGION,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

export interface TextractGeometry {
  boundingBox: {
    width: number;
    height: number;
    left: number;
    top: number;
  };
  polygon: Array<{ x: number; y: number }>;
}

export interface TextractExtractedText {
  text: string;
  confidence: number;
  geometry: TextractGeometry;
  layoutType: string;
  blockId: string;
  pageIndex: number;
}

export interface TextractResult {
  extractedText: TextractExtractedText[];
  layoutElements: {
    paragraphs: TextractExtractedText[];
    titles: TextractExtractedText[];
    headers: TextractExtractedText[];
    footers: TextractExtractedText[];
    sectionHeaders: TextractExtractedText[];
  };
  rawBlocks: Block[];
}

/**
 * Map AWS Textract block types to our database enum values
 */
function mapTextractBlockTypeToLayoutType(blockType: string): string {
  const blockTypeMap: Record<string, string> = {
    WORD: 'LAYOUT_TEXT',
    LINE: 'LINE',
    LAYOUT_TEXT: 'LAYOUT_TEXT',
    LAYOUT_TITLE: 'TITLE',
    LAYOUT_HEADER: 'HEADER',
    LAYOUT_FOOTER: 'FOOTER',
    LAYOUT_SECTION_HEADER: 'SECTION_HEADER',
    LAYOUT_LIST: 'PARAGRAPH',
    LAYOUT_FIGURE: 'PARAGRAPH',
    LAYOUT_TABLE: 'PARAGRAPH',
    LAYOUT_KEY_VALUE: 'PARAGRAPH',
    PAGE: 'UNKNOWN',
    MERGED_CELL: 'UNKNOWN',
    SELECTION_ELEMENT: 'UNKNOWN',
  };

  return blockTypeMap[blockType] || 'UNKNOWN';
}

/**
 * Convert Textract geometry to our standard format
 */
function convertGeometry(geometry: Geometry): TextractGeometry {
  const boundingBox = geometry?.BoundingBox || {};
  const polygon = geometry?.Polygon || [];

  return {
    boundingBox: {
      width: boundingBox.Width || 0,
      height: boundingBox.Height || 0,
      left: boundingBox.Left || 0,
      top: boundingBox.Top || 0,
    },
    polygon: polygon.map((point: Point) => ({
      x: point.X || 0,
      y: point.Y || 0,
    })),
  };
}

/**
 * Split a multi-page PDF into individual pages
 * @param pdfBytes Buffer containing the PDF document
 * @returns Array of PDF page buffers
 */
async function splitPdfIntoPages(pdfBytes: Buffer): Promise<Buffer[]> {
  const pdfDoc = await PDFDocument.load(pdfBytes);
  const pageCount = pdfDoc.getPageCount();
  const pages: Buffer[] = [];

  for (let i = 0; i < pageCount; i++) {
    const singlePageDoc = await PDFDocument.create();
    const [copiedPage] = await singlePageDoc.copyPages(pdfDoc, [i]);
    singlePageDoc.addPage(copiedPage);
    const pdfBuffer = await singlePageDoc.save();
    pages.push(Buffer.from(pdfBuffer));
  }

  return pages;
}

/**
 * Extract text from a PDF document using AWS Textract with LAYOUT analysis
 * Enhanced with smart processing logic and async support for multi-page PDFs
 * @param documentBytes Buffer containing the PDF document
 * @param debug Whether to log detailed processing information
 * @param forceSync Force synchronous processing (bypass async)
 * @param s3Key Optional S3 key if document is already in S3
 * @returns Extracted text with layout and geometry information
 */
export async function analyzeDocumentWithTextract(
  documentBytes: Buffer,
  debug = false,
  forceSync = false,
  s3Key?: string
): Promise<TextractResult> {
  // Early return with mock data when USE_MOCKS=true
  if (process.env.USE_MOCKS === 'true') {
    console.log(`🎭 [MOCK] Analyzing document of ${documentBytes.length} bytes`);
    await new Promise((resolve) => setTimeout(resolve, 500 + Math.random() * 1000));

    return {
      extractedText: [
        {
          text: 'Sample Document Title',
          confidence: 0.99,
          geometry: { boundingBox: { left: 0.1, top: 0.05, width: 0.8, height: 0.1 }, polygon: [] },
          layoutType: 'TITLE',
          blockId: 'mock-title-1',
          pageIndex: 0,
        },
        {
          text: 'This is a mock document content for testing purposes.',
          confidence: 0.95,
          geometry: { boundingBox: { left: 0.1, top: 0.2, width: 0.8, height: 0.1 }, polygon: [] },
          layoutType: 'LINE',
          blockId: 'mock-line-1',
          pageIndex: 0,
        },
      ],
      layoutElements: { paragraphs: [], titles: [], headers: [], footers: [], sectionHeaders: [] },
      rawBlocks: [],
    };
  }

  const startTime = performance.now();

  try {
    // Determine the best processing strategy
    const processingStrategy = await determineProcessingStrategy(
      documentBytes,
      debug,
      forceSync,
      s3Key
    );

    let result: TextractResult;

    switch (processingStrategy.strategy) {
      case 'async':
        if (!s3Key) {
          throw new Error('S3 key required for async processing but not provided');
        }
        result = await processAsyncTextractDirect(s3Key);
        break;

      case 'sync-single':
        result = await processSyncSinglePage(documentBytes, debug);
        break;

      case 'sync-split':
        result = await processSyncSplitPages(documentBytes, debug);
        break;

      default:
        // Fallback to original logic
        result = await processWithOriginalLogic(documentBytes, debug);
        break;
    }

    const endTime = performance.now();
    if (debug) {
      console.log(
        `✅ Smart Textract analysis completed in ${((endTime - startTime) / 1000).toFixed(2)}s`
      );
      console.log(`Found ${result.extractedText.length} text blocks`);
    }

    return result;
  } catch (error) {
    console.error('❌ Smart Textract analysis failed:', error);

    // Fallback to original logic if smart processing fails
    if (debug) {
      console.log('🔄 Falling back to original processing logic...');
    }

    try {
      return await processWithOriginalLogic(documentBytes, debug);
    } catch (fallbackError) {
      console.error('❌ Fallback processing also failed:', fallbackError);
      throw new Error(
        `Textract analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }
}

/**
 * Determine the best processing strategy based on document characteristics
 */
async function determineProcessingStrategy(
  documentBytes: Buffer,
  debug: boolean,
  forceSync: boolean,
  s3Key?: string
): Promise<{
  strategy: 'async' | 'sync-single' | 'sync-split' | 'original';
  reason?: string;
}> {
  try {
    // If sync is forced, skip async considerations
    if (forceSync) {
      const pageCount = await getPageCount(documentBytes);
      return {
        strategy: pageCount > 1 ? 'sync-split' : 'sync-single',
        reason: `Sync processing forced, ${pageCount} pages detected`,
      };
    }

    // Check if async processing is available
    const asyncConfig = await validateAsyncConfiguration();
    if (!asyncConfig.isValid) {
      const pageCount = await getPageCount(documentBytes);
      return {
        strategy: pageCount > 1 ? 'sync-split' : 'sync-single',
        reason: `Async not available (missing: ${asyncConfig.missingConfig.join(', ')}), ${pageCount} pages`,
      };
    }

    // Get document characteristics
    const pageCount = await getPageCount(documentBytes);
    const fileSizeMB = documentBytes.length / 1024 / 1024;

    // Decision matrix for processing strategy
    if (pageCount === 1) {
      // Single page - use sync for speed
      return {
        strategy: 'sync-single',
        reason: 'Single page document, sync processing faster',
      };
    }

    if (pageCount > 10 || fileSizeMB > 20) {
      // Large documents - prefer async if available
      if (s3Key && asyncConfig.isValid) {
        return {
          strategy: 'async',
          reason: `Large document (${pageCount} pages, ${fileSizeMB.toFixed(1)}MB), async processing preferred`,
        };
      } else {
        return {
          strategy: 'sync-split',
          reason: `Large document but async not available, using sync split processing`,
        };
      }
    }

    if (pageCount > 1 && pageCount <= 10) {
      // Medium documents - prefer async for accuracy
      if (s3Key && asyncConfig.isValid) {
        return {
          strategy: 'async',
          reason: `Multi-page document (${pageCount} pages), async for better accuracy`,
        };
      } else {
        return {
          strategy: 'sync-split',
          reason: `Multi-page document but async not available, using sync split processing`,
        };
      }
    }

    // Default fallback
    return {
      strategy: 'original',
      reason: 'Unable to determine optimal strategy, using original logic',
    };
  } catch (error) {
    if (debug) {
      console.warn('Failed to determine processing strategy:', error);
    }
    return {
      strategy: 'original',
      reason: 'Strategy determination failed, using original logic',
    };
  }
}

/**
 * Process document using async Textract (requires S3 key)
 */
async function processAsyncTextractDirect(s3Key: string): Promise<TextractResult> {
  if (!env.AWS_S3_BUCKET || !env.AWS_SNS_TOPIC_ARN || !env.AWS_ROLE_ARN) {
    throw new Error('Async Textract configuration not available');
  }

  const jobId = await startAsyncDocumentAnalysis(
    env.AWS_S3_BUCKET,
    s3Key,
    env.AWS_SNS_TOPIC_ARN,
    env.AWS_ROLE_ARN,
    `direct-${Date.now()}`
  );

  // Poll for completion (in a real workflow, this would use SNS notifications)
  let attempts = 0;
  const maxAttempts = 60; // 5 minutes max

  while (attempts < maxAttempts) {
    await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait 5 seconds

    const status = await checkAsyncJobStatus(jobId);

    if (status.status === 'SUCCEEDED') {
      return await getAsyncDocumentAnalysis(jobId);
    } else if (status.status === 'FAILED') {
      throw new Error(`Async Textract job failed: ${status.statusMessage}`);
    }

    attempts++;
  }

  throw new Error('Async Textract job timeout');
}

/**
 * Process single page document synchronously
 */
async function processSyncSinglePage(
  documentBytes: Buffer,
  debug: boolean
): Promise<TextractResult> {
  const result = await analyzeSinglePageDocument(documentBytes, debug);
  if (!result) {
    throw new Error('Failed to process single page document');
  }
  return result;
}

/**
 * Process multi-page document by splitting pages
 */
async function processSyncSplitPages(
  documentBytes: Buffer,
  debug: boolean
): Promise<TextractResult> {
  const pageBuffers = await splitPdfIntoPages(documentBytes);

  // Process each page separately
  const allResults: TextractResult[] = [];

  for (let pageIndex = 0; pageIndex < pageBuffers.length; pageIndex++) {
    try {
      const pageResult = await analyzeSinglePageDocument(pageBuffers[pageIndex], debug);
      if (pageResult) {
        // Update page indices for this page's blocks
        const updatedResult = updatePageIndices(pageResult, pageIndex);
        allResults.push(updatedResult);
      }
    } catch (error) {
      console.warn(`⚠️ Failed to process page ${pageIndex + 1}, skipping:`, error);
    }
  }

  // Merge all results into a single TextractResult
  return mergeTextractResults(allResults);
}

/**
 * Use the original processing logic as fallback
 */
async function processWithOriginalLogic(
  documentBytes: Buffer,
  debug: boolean
): Promise<TextractResult> {
  // First, try to analyze as a single document
  const singlePageResult = await analyzeSinglePageDocument(documentBytes, debug);

  if (singlePageResult) {
    return singlePageResult;
  }

  // If single page analysis fails, split into pages and process separately
  if (debug) {
    console.log('📄 Single-page analysis failed, splitting PDF into individual pages...');
  }

  return await processSyncSplitPages(documentBytes, debug);
}

/**
 * Analyze a single page document with Textract
 * @param documentBytes Buffer containing the single-page PDF document
 * @param debug Whether to log detailed processing information
 * @returns Extracted text with layout and geometry information, or null if failed
 */
async function analyzeSinglePageDocument(
  documentBytes: Buffer,
  debug = false
): Promise<TextractResult | null> {
  try {
    const command = new AnalyzeDocumentCommand({
      Document: {
        Bytes: documentBytes,
      },
      FeatureTypes: [FeatureType.LAYOUT],
    });

    const response = await textractClient.send(command);

    if (!response.Blocks) {
      throw new Error('No blocks returned from Textract');
    }

    const extractedText: TextractExtractedText[] = [];
    const layoutElements = {
      paragraphs: [] as TextractExtractedText[],
      titles: [] as TextractExtractedText[],
      headers: [] as TextractExtractedText[],
      footers: [] as TextractExtractedText[],
      sectionHeaders: [] as TextractExtractedText[],
    };

    // Process blocks and extract text with layout information
    for (const block of response.Blocks) {
      if (!block.Text || !block.Confidence || !block.Geometry) {
        continue;
      }

      // Extract page index from block metadata (Textract provides page info)
      const pageIndex = (block.Page || 1) - 1; // Convert to 0-based indexing

      const extractedItem: TextractExtractedText = {
        text: block.Text,
        confidence: block.Confidence / 100, // Convert to 0-1 scale
        geometry: convertGeometry(block.Geometry),
        layoutType: mapTextractBlockTypeToLayoutType(block.BlockType || ''),
        blockId: block.Id || '',
        pageIndex,
      };

      extractedText.push(extractedItem);

      // Categorize by mapped layout type
      switch (extractedItem.layoutType) {
        case 'LAYOUT_TEXT':
          // For layout text, check if it's part of a specific layout element
          if (block.Text.length > 50) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
        case 'TITLE':
          layoutElements.titles.push(extractedItem);
          break;
        case 'HEADER':
          layoutElements.headers.push(extractedItem);
          break;
        case 'FOOTER':
          layoutElements.footers.push(extractedItem);
          break;
        case 'SECTION_HEADER':
          layoutElements.sectionHeaders.push(extractedItem);
          break;
        case 'LINE':
          // Lines are individual text lines, group them as paragraphs if they're substantial
          if (block.Text.length > 20) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
        case 'PARAGRAPH':
          layoutElements.paragraphs.push(extractedItem);
          break;
      }
    }

    if (debug) {
      console.log('📊 Textract extraction summary:');
      console.log(`  - Total text blocks: ${extractedText.length}`);
      console.log(`  - Paragraphs: ${layoutElements.paragraphs.length}`);
      console.log(`  - Titles: ${layoutElements.titles.length}`);
      console.log(`  - Headers: ${layoutElements.headers.length}`);
      console.log(`  - Footers: ${layoutElements.footers.length}`);
      console.log(`  - Section headers: ${layoutElements.sectionHeaders.length}`);

      // Show confidence distribution
      const confidences = extractedText.map((item) => item.confidence);
      const avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length;
      const minConfidence = Math.min(...confidences);
      const maxConfidence = Math.max(...confidences);

      console.log(
        `  - Confidence: avg=${(avgConfidence * 100).toFixed(1)}%, min=${(minConfidence * 100).toFixed(1)}%, max=${(maxConfidence * 100).toFixed(1)}%`
      );
    }

    return {
      extractedText,
      layoutElements,
      rawBlocks: response.Blocks,
    };
  } catch (error) {
    if (debug) {
      console.log('⚠️ Single page analysis failed:', error);
    }
    return null;
  }
}

/**
 * Update page indices in a TextractResult for a specific page number
 * @param result Original TextractResult
 * @param actualPageIndex The actual page index this result represents
 * @returns Updated TextractResult with corrected page indices
 */
function updatePageIndices(result: TextractResult, actualPageIndex: number): TextractResult {
  const updatedExtractedText = result.extractedText.map((item) => ({
    ...item,
    pageIndex: actualPageIndex,
  }));

  const updatedLayoutElements = {
    paragraphs: result.layoutElements.paragraphs.map((item) => ({
      ...item,
      pageIndex: actualPageIndex,
    })),
    titles: result.layoutElements.titles.map((item) => ({
      ...item,
      pageIndex: actualPageIndex,
    })),
    headers: result.layoutElements.headers.map((item) => ({
      ...item,
      pageIndex: actualPageIndex,
    })),
    footers: result.layoutElements.footers.map((item) => ({
      ...item,
      pageIndex: actualPageIndex,
    })),
    sectionHeaders: result.layoutElements.sectionHeaders.map((item) => ({
      ...item,
      pageIndex: actualPageIndex,
    })),
  };

  // Update raw blocks with unique IDs to prevent conflicts and update relationships
  const updatedRawBlocks = result.rawBlocks.map((block) => ({
    ...block,
    Id: block.Id ? `p${actualPageIndex}_${block.Id}` : undefined,
    Page: actualPageIndex + 1,
    // Update relationship IDs to match the new block IDs
    Relationships: block.Relationships?.map((relationship) => ({
      ...relationship,
      Ids: relationship.Ids?.map((id) => `p${actualPageIndex}_${id}`),
    })),
  }));

  return {
    extractedText: updatedExtractedText,
    layoutElements: updatedLayoutElements,
    rawBlocks: updatedRawBlocks,
  };
}

/**
 * Merge multiple TextractResults into a single result
 * @param results Array of TextractResults from individual pages
 * @returns Combined TextractResult
 */
function mergeTextractResults(results: TextractResult[]): TextractResult {
  const mergedExtractedText: TextractExtractedText[] = [];
  const mergedLayoutElements = {
    paragraphs: [] as TextractExtractedText[],
    titles: [] as TextractExtractedText[],
    headers: [] as TextractExtractedText[],
    footers: [] as TextractExtractedText[],
    sectionHeaders: [] as TextractExtractedText[],
  };
  const mergedRawBlocks: Block[] = [];

  for (const result of results) {
    mergedExtractedText.push(...result.extractedText);
    mergedLayoutElements.paragraphs.push(...result.layoutElements.paragraphs);
    mergedLayoutElements.titles.push(...result.layoutElements.titles);
    mergedLayoutElements.headers.push(...result.layoutElements.headers);
    mergedLayoutElements.footers.push(...result.layoutElements.footers);
    mergedLayoutElements.sectionHeaders.push(...result.layoutElements.sectionHeaders);
    mergedRawBlocks.push(...result.rawBlocks);
  }

  return {
    extractedText: mergedExtractedText,
    layoutElements: mergedLayoutElements,
    rawBlocks: mergedRawBlocks,
  };
}

/**
 * Start asynchronous document analysis for multi-page PDFs
 * @param s3Bucket S3 bucket name where document is stored
 * @param s3Key S3 object key for the document
 * @param snsTopicArn SNS topic ARN for completion notification
 * @param roleArn IAM role ARN that allows Textract to publish to SNS
 * @param clientRequestToken Optional token to prevent duplicate jobs
 * @returns JobId for tracking the analysis
 */
export async function startAsyncDocumentAnalysis(
  s3Bucket: string,
  s3Key: string,
  snsTopicArn: string,
  roleArn: string,
  clientRequestToken?: string
): Promise<string> {
  // Early return with mock data when USE_MOCKS=true
  // if (process.env.USE_MOCKS === 'true') {
  //   console.log(`🎭 [MOCK] Started async analysis for ${s3Key} in bucket ${s3Bucket}`);
  //   await new Promise((resolve) => setTimeout(resolve, 200 + Math.random() * 300));
  //   return `mock-job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  // }

  try {
    // Validate parameters
    if (!s3Bucket || s3Bucket.length < 3 || s3Bucket.length > 63) {
      throw new Error(`Invalid S3 bucket name: "${s3Bucket}"`);
    }

    if (!s3Key || s3Key.length === 0) {
      throw new Error(`Invalid S3 key: "${s3Key}"`);
    }

    if (!snsTopicArn || !snsTopicArn.startsWith('arn:aws:sns:')) {
      throw new Error(`Invalid SNS Topic ARN format: "${snsTopicArn}"`);
    }

    if (!roleArn || !roleArn.startsWith('arn:aws:iam::')) {
      throw new Error(`Invalid Role ARN format: "${roleArn}"`);
    }

    // Ensure ClientRequestToken is valid (alphanumeric, hyphens, max 64 chars)
    const sanitizedToken = clientRequestToken?.replace(/[^a-zA-Z0-9-]/g, '-').substring(0, 64);
    console.log(`  - Sanitized Client Request Token: "${sanitizedToken}"`);

    const command = new StartDocumentAnalysisCommand({
      DocumentLocation: {
        S3Object: {
          Bucket: s3Bucket,
          Name: s3Key,
        },
      },
      FeatureTypes: [FeatureType.LAYOUT],
      NotificationChannel: {
        RoleArn: roleArn,
        SNSTopicArn: snsTopicArn,
      },
      // ClientRequestToken: sanitizedToken,
      JobTag: `multipage-pdf-${Date.now().toString().slice(-8)}`, // Help identify the job type
    });

    const response: StartDocumentAnalysisResponse = await textractClient.send(command);

    if (!response.JobId) {
      throw new Error('Failed to get JobId from StartDocumentAnalysis response');
    }

    console.log(`✅ Started async Textract job: ${response.JobId} for ${s3Key}`);
    return response.JobId;
  } catch (error) {
    console.error('❌ Failed to start async document analysis:', error);
    throw new Error(
      `Failed to start async document analysis: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Get results from asynchronous document analysis
 * @param jobId JobId returned from StartDocumentAnalysis
 * @param maxResults Maximum number of results per page (default 1000)
 * @param nextToken Token for pagination (if results are large)
 * @returns TextractResult with extracted text and layout information
 */
export async function getAsyncDocumentAnalysis(
  jobId: string,
  maxResults = 1000,
  nextToken?: string
): Promise<TextractResult> {
  // Early return with mock data when USE_MOCKS=true
  if (process.env.USE_MOCKS === 'true') {
    console.log(`🎭 [MOCK] Getting results for job ${jobId}`);
    await new Promise((resolve) => setTimeout(resolve, 300 + Math.random() * 500));

    return {
      extractedText: [
        {
          text: 'Async Mock Document Title',
          confidence: 0.98,
          geometry: { boundingBox: { left: 0.1, top: 0.05, width: 0.8, height: 0.1 }, polygon: [] },
          layoutType: 'TITLE',
          blockId: 'async-mock-title-1',
          pageIndex: 0,
        },
      ],
      layoutElements: { paragraphs: [], titles: [], headers: [], footers: [], sectionHeaders: [] },
      rawBlocks: [],
    };
  }

  try {
    console.log(`🔍 Getting async Textract results for job: ${jobId}`);

    const allBlocks: Block[] = [];
    let currentToken = nextToken;
    let hasMore = true;

    // Handle pagination to get all results
    while (hasMore) {
      const command = new GetDocumentAnalysisCommand({
        JobId: jobId,
        MaxResults: maxResults,
        NextToken: currentToken,
      });

      const response: GetDocumentAnalysisResponse = await textractClient.send(command);

      if (!response.Blocks) {
        throw new Error('No blocks returned from GetDocumentAnalysis');
      }

      allBlocks.push(...response.Blocks);

      if (response.NextToken) {
        currentToken = response.NextToken;
      } else {
        hasMore = false;
      }
    }

    console.log(`📄 Retrieved ${allBlocks.length} blocks from async Textract job`);

    // Process blocks similar to sync processing
    const extractedText: TextractExtractedText[] = [];
    const layoutElements = {
      paragraphs: [] as TextractExtractedText[],
      titles: [] as TextractExtractedText[],
      headers: [] as TextractExtractedText[],
      footers: [] as TextractExtractedText[],
      sectionHeaders: [] as TextractExtractedText[],
    };

    for (const block of allBlocks) {
      if (!block.Text || !block.Confidence || !block.Geometry) {
        continue;
      }

      const pageIndex = (block.Page || 1) - 1; // Convert to 0-based indexing

      const extractedItem: TextractExtractedText = {
        text: block.Text,
        confidence: block.Confidence / 100, // Convert to 0-1 scale
        geometry: convertGeometry(block.Geometry),
        layoutType: mapTextractBlockTypeToLayoutType(block.BlockType || ''),
        blockId: block.Id || '',
        pageIndex,
      };

      extractedText.push(extractedItem);

      // Categorize by layout type
      switch (extractedItem.layoutType) {
        case 'LAYOUT_TEXT':
          if (block.Text.length > 50) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
        case 'TITLE':
          layoutElements.titles.push(extractedItem);
          break;
        case 'HEADER':
          layoutElements.headers.push(extractedItem);
          break;
        case 'FOOTER':
          layoutElements.footers.push(extractedItem);
          break;
        case 'SECTION_HEADER':
          layoutElements.sectionHeaders.push(extractedItem);
          break;
        case 'LINE':
          if (block.Text.length > 20) {
            layoutElements.paragraphs.push(extractedItem);
          }
          break;
        case 'PARAGRAPH':
          layoutElements.paragraphs.push(extractedItem);
          break;
      }
    }

    console.log(`✅ Processed async Textract results: ${extractedText.length} text items`);

    return {
      extractedText,
      layoutElements,
      rawBlocks: allBlocks,
    };
  } catch (error) {
    console.error('❌ Failed to get async document analysis results:', error);
    throw new Error(
      `Failed to get async document analysis: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Check the status of an async Textract job
 * @param jobId JobId returned from StartDocumentAnalysis
 * @returns Job status information
 */
export async function checkAsyncJobStatus(jobId: string): Promise<{
  status: JobStatus;
  statusMessage?: string;
  jobId: string;
}> {
  try {
    const command = new GetDocumentAnalysisCommand({
      JobId: jobId,
      MaxResults: 1, // We only need status, not results
    });

    const response = await textractClient.send(command);

    return {
      status: response.JobStatus || JobStatus.IN_PROGRESS,
      statusMessage: response.StatusMessage,
      jobId,
    };
  } catch (error) {
    console.error(`Failed to check async job status for ${jobId}:`, error);
    throw error;
  }
}

/**
 * Validate async Textract configuration
 * @returns True if async processing is properly configured
 */
export async function validateAsyncConfiguration(): Promise<{
  isValid: boolean;
  missingConfig: string[];
}> {
  const missingConfig: string[] = [];

  if (!env.AWS_S3_BUCKET) {
    missingConfig.push('AWS_S3_BUCKET');
  }

  if (!env.AWS_SNS_TOPIC_ARN) {
    missingConfig.push('AWS_SNS_TOPIC_ARN');
  }

  if (!env.AWS_ROLE_ARN) {
    missingConfig.push('AWS_ROLE_ARN');
  }

  return {
    isValid: missingConfig.length === 0,
    missingConfig,
  };
}

/**
 * Get PDF page count from buffer
 * @param pdfBytes Buffer containing the PDF document
 * @returns Number of pages in the PDF
 */
export async function getPageCount(pdfBytes: Buffer): Promise<number> {
  try {
    const pdfDoc = await PDFDocument.load(pdfBytes);
    return pdfDoc.getPageCount();
  } catch (error) {
    console.warn('Failed to get page count, assuming single page:', error);
    return 1;
  }
}

/**
 * Check if AWS Textract service is available and properly configured
 */
export async function checkTextractAvailability(): Promise<boolean> {
  try {
    // Simple test to verify credentials and service availability
    const testCommand = new AnalyzeDocumentCommand({
      Document: {
        Bytes: Buffer.from('test'),
      },
      FeatureTypes: [FeatureType.LAYOUT],
    });

    // This will fail with invalid document but should not fail with auth errors
    await textractClient.send(testCommand);
    return true;
  } catch (error: unknown) {
    // If it's an invalid document error, credentials are working
    const errorName = error instanceof Error ? error.name : 'Unknown';
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (errorName === 'InvalidParameterException' || errorName === 'UnsupportedDocumentException') {
      return true;
    }

    // Other errors indicate service unavailability or auth issues
    console.warn('Textract service unavailable:', errorMessage);
    return false;
  }
}

/**
 * Process Textract blocks into semantic chunks using LAYOUT_* blocks
 * This creates chunks based on semantic document structure (paragraphs, titles, headers)
 * Falls back to LINE blocks if no LAYOUT_* blocks are found
 */
export function processTextractBlocksToChunks(result: TextractResult): Array<{
  content: string;
  chunkOrder: number;
  geometry: TextractGeometry[];
  layoutTypes: string[];
  confidenceScore: number;
  pageIndex: number;
}> {
  const chunks = [];
  const rawBlocks = result.rawBlocks;

  // Create a map of block ID to block for quick lookups
  const blocksMap = new Map<string, Block>();
  rawBlocks.forEach((block) => {
    if (block.Id) {
      blocksMap.set(block.Id, block);
    }
  });

  // Find all LAYOUT_* blocks (semantic containers)
  const layoutBlocks = rawBlocks.filter(
    (block) =>
      block.BlockType?.startsWith('LAYOUT_') &&
      ['LAYOUT_TITLE', 'LAYOUT_HEADER', 'LAYOUT_SECTION_HEADER', 'LAYOUT_TEXT'].includes(
        block.BlockType
      )
  );

  let chunkOrder = 0;

  if (layoutBlocks.length > 0) {
    // Use LAYOUT_* blocks when available (preferred method)
    const sortedLayoutBlocks = layoutBlocks.sort((a, b) => {
      if (!a.Geometry?.BoundingBox || !b.Geometry?.BoundingBox) return 0;
      const aTop = a.Geometry.BoundingBox.Top ?? 0;
      const bTop = b.Geometry.BoundingBox.Top ?? 0;
      const aLeft = a.Geometry.BoundingBox.Left ?? 0;
      const bLeft = b.Geometry.BoundingBox.Left ?? 0;
      const topDiff = aTop - bTop;
      if (Math.abs(topDiff) < 0.01) {
        return aLeft - bLeft;
      }
      return topDiff;
    });

    for (const layoutBlock of sortedLayoutBlocks) {
      const semanticChunk = extractTextFromLayoutBlock(layoutBlock, blocksMap);

      if (semanticChunk.text.trim().length > 0) {
        chunks.push({
          content: semanticChunk.text.trim(),
          chunkOrder: chunkOrder++,
          geometry: semanticChunk.geometry,
          layoutTypes: [semanticChunk.layoutType],
          confidenceScore: semanticChunk.confidence,
          pageIndex: semanticChunk.pageIndex,
        });
      }
    }
  } else {
    // Fallback: Use LINE blocks when no LAYOUT_* blocks are found
    console.log('📄 No LAYOUT_* blocks found, falling back to LINE block processing');
    const lineBlocks = rawBlocks.filter(
      (block) => block.BlockType === 'LINE' && block.Text && block.Text.trim().length > 0
    );

    // Sort LINE blocks by position (top to bottom, left to right)
    const sortedLineBlocks = lineBlocks.sort((a, b) => {
      if (!a.Geometry?.BoundingBox || !b.Geometry?.BoundingBox) return 0;
      const aTop = a.Geometry.BoundingBox.Top ?? 0;
      const bTop = b.Geometry.BoundingBox.Top ?? 0;
      const aLeft = a.Geometry.BoundingBox.Left ?? 0;
      const bLeft = b.Geometry.BoundingBox.Left ?? 0;
      const topDiff = aTop - bTop;
      if (Math.abs(topDiff) < 0.02) {
        // Allow slightly more tolerance for line alignment
        return aLeft - bLeft;
      }
      return topDiff;
    });

    // Group consecutive lines into paragraphs
    const paragraphs: Block[][] = [];
    let currentParagraph: Block[] = [];
    let lastBottom = 0;

    for (const lineBlock of sortedLineBlocks) {
      const top = lineBlock.Geometry?.BoundingBox?.Top ?? 0;
      const height = lineBlock.Geometry?.BoundingBox?.Height ?? 0.05;
      const currentBottom = top + height;

      // If there's a significant gap from the last line, start a new paragraph
      const gap = top - lastBottom;
      if (currentParagraph.length > 0 && gap > height * 1.5) {
        paragraphs.push([...currentParagraph]);
        currentParagraph = [];
      }

      currentParagraph.push(lineBlock);
      lastBottom = currentBottom;
    }

    // Add the last paragraph
    if (currentParagraph.length > 0) {
      paragraphs.push(currentParagraph);
    }

    // Convert paragraphs to chunks
    for (const paragraphLines of paragraphs) {
      const texts = paragraphLines
        .map((line) => line.Text || '')
        .filter((text) => text.trim().length > 0);
      const confidences = paragraphLines.map((line) => line.Confidence || 100);

      if (texts.length > 0) {
        const geometries: TextractGeometry[] = paragraphLines.map((line) => ({
          boundingBox: {
            left: line.Geometry?.BoundingBox?.Left || 0,
            top: line.Geometry?.BoundingBox?.Top || 0,
            width: line.Geometry?.BoundingBox?.Width || 0,
            height: line.Geometry?.BoundingBox?.Height || 0,
          },
          polygon: (line.Geometry?.Polygon || []).map((point) => ({
            x: point.X || 0,
            y: point.Y || 0,
          })),
        }));

        const avgConfidence = confidences.reduce((a, b) => a + b, 0) / confidences.length;
        const content = texts.join(' ');

        // Extract page index from the first line block in this paragraph
        let pageIndex = 0;
        const firstLine = paragraphLines[0];
        if (firstLine?.Page !== undefined) {
          pageIndex = firstLine.Page - 1; // Convert to 0-based indexing
        }

        // Only create chunk if content is substantial
        if (content.trim().length > 10) {
          chunks.push({
            content: content.trim(),
            chunkOrder: chunkOrder++,
            geometry: geometries,
            layoutTypes: ['PARAGRAPH'],
            confidenceScore: avgConfidence / 100, // Convert to 0-1 scale
            pageIndex,
          });
        }
      }
    }
  }

  return chunks;
}

/**
 * Extract text and combine geometry from a LAYOUT_* block by following its child LINE relationships
 */
function extractTextFromLayoutBlock(
  layoutBlock: Block,
  blocksMap: Map<string, Block>
): {
  text: string;
  geometry: TextractGeometry[];
  layoutType: string;
  confidence: number;
  pageIndex: number;
} {
  const lineTexts: string[] = [];
  const geometries: TextractGeometry[] = [];
  const confidences: number[] = [];

  // Map layout block type to simplified type
  const layoutType = mapLayoutType(layoutBlock.BlockType || 'UNKNOWN');

  // Get child LINE blocks through relationships
  if (layoutBlock.Relationships) {
    for (const relationship of layoutBlock.Relationships) {
      if (relationship.Type === 'CHILD' && relationship.Ids) {
        for (const lineId of relationship.Ids) {
          const lineBlock = blocksMap.get(lineId);
          if (lineBlock && lineBlock.BlockType === 'LINE') {
            // Add the line text
            if (lineBlock.Text) {
              lineTexts.push(lineBlock.Text);
            }

            // Add the line geometry
            if (lineBlock.Geometry?.BoundingBox && lineBlock.Geometry?.Polygon) {
              geometries.push({
                boundingBox: {
                  left: lineBlock.Geometry.BoundingBox.Left || 0,
                  top: lineBlock.Geometry.BoundingBox.Top || 0,
                  width: lineBlock.Geometry.BoundingBox.Width || 0,
                  height: lineBlock.Geometry.BoundingBox.Height || 0,
                },
                polygon: lineBlock.Geometry.Polygon.map((point) => ({
                  x: point.X || 0,
                  y: point.Y || 0,
                })),
              });
            }

            // Add confidence
            if (lineBlock.Confidence) {
              confidences.push(lineBlock.Confidence);
            }
          }
        }
      }
    }
  }

  // Extract page index from the layout block or find its parent PAGE block
  let pageIndex = 0;

  // First try to get page from the layout block itself
  if (layoutBlock.Page !== undefined) {
    pageIndex = layoutBlock.Page - 1; // Convert to 0-based indexing
  } else {
    // Find the PAGE block that contains this layout block
    for (const block of Array.from(blocksMap.values())) {
      if (block.BlockType === 'PAGE' && block.Relationships) {
        const containsLayout = block.Relationships.some(
          (rel) => rel.Type === 'CHILD' && layoutBlock.Id && rel.Ids?.includes(layoutBlock.Id)
        );
        if (containsLayout && block.Page !== undefined) {
          pageIndex = block.Page - 1; // Convert to 0-based indexing
          break;
        }
      }
    }
  }

  return {
    text: lineTexts.join(' '),
    geometry: geometries,
    layoutType,
    confidence:
      confidences.length > 0 ? confidences.reduce((a, b) => a + b, 0) / confidences.length : 1.0,
    pageIndex,
  };
}

/**
 * Map Textract layout types to simpler semantic types
 */
function mapLayoutType(blockType: string): string {
  switch (blockType) {
    case 'LAYOUT_TITLE':
      return 'TITLE';
    case 'LAYOUT_HEADER':
      return 'HEADER';
    case 'LAYOUT_SECTION_HEADER':
      return 'SECTION_HEADER';
    case 'LAYOUT_TEXT':
      return 'PARAGRAPH';
    default:
      return 'TEXT';
  }
}
