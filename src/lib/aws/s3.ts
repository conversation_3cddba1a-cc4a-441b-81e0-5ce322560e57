import { Readable } from 'stream';
import { r2Client } from '@/lib/cloudflare/r2';
import { env } from '@/lib/env.mjs';
import {
  DeleteObjectCommand,
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';

// AWS S3 Client for Textract processing
export const s3Client = new S3Client({
  // DEBUG: using localstack for local development
  // endpoint: 'http://s3.localhost.localstack.cloud:4566',
  region: env.AWS_S3_REGION,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

export interface S3BridgeConfig {
  r2BucketName: string;
  s3BucketName: string;
}

export class S3Bridge {
  private config: S3BridgeConfig;

  constructor(config?: Partial<S3BridgeConfig>) {
    this.config = {
      r2BucketName: env.CLOUDFLARE_BUCKET_NAME,
      s3BucketName: env.AWS_S3_BUCKET || 'knowledge-sphere-textract-temp',
      ...config,
    };
  }

  /**
   * Copy a document from Cloudflare R2 to AWS S3 for Textract processing
   * @param r2Key The R2 object key (s3Key from documents table)
   * @param documentId The document ID for tracking
   * @returns The S3 key where the file was uploaded
   */
  async copyFromR2ToS3(r2Key: string, documentId: string): Promise<string> {
    try {
      // Use the same key as R2 - no need for different paths
      const s3Key = r2Key;

      console.log(`[S3Bridge] Copying ${r2Key} from R2 to S3 as ${s3Key}`);

      // Download from R2
      const getCommand = new GetObjectCommand({
        Bucket: this.config.r2BucketName,
        Key: r2Key,
      });

      const r2Response = await r2Client.send(getCommand);

      if (!r2Response.Body) {
        throw new Error('Failed to retrieve document from R2');
      }

      // Convert stream to buffer
      const bodyBuffer = await this.streamToBuffer(r2Response.Body as Readable);

      // Upload to S3
      const putCommand = new PutObjectCommand({
        Bucket: this.config.s3BucketName,
        Key: s3Key,
        Body: bodyBuffer,
        ContentType: r2Response.ContentType || 'application/pdf',
        Metadata: {
          documentId,
          originalR2Key: r2Key,
          uploadedAt: new Date().toISOString(),
        },
      });

      await s3Client.send(putCommand);

      // Note: S3 key is the same as the original s3Key - no database update needed
      // The same key works for both R2 and S3 storage

      console.log(`[S3Bridge] Successfully copied to S3: ${s3Key}`);
      return s3Key;
    } catch (error) {
      console.error(`[S3Bridge] Failed to copy ${r2Key} to S3:`, error);
      throw new Error(
        `S3 Bridge copy failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Immediately cleanup S3 file
   * @param s3Key The S3 object key to delete
   * @param documentId Optional document ID for database cleanup
   */
  async cleanupS3File(s3Key: string): Promise<void> {
    try {
      console.log(`[S3Bridge] Cleaning up S3 file: ${s3Key}`);

      const deleteCommand = new DeleteObjectCommand({
        Bucket: this.config.s3BucketName,
        Key: s3Key,
      });

      await s3Client.send(deleteCommand);

      // Note: No database cleanup needed since we reuse the same s3Key
      // The document still exists in R2 with the same key

      console.log(`[S3Bridge] Successfully cleaned up S3 file: ${s3Key}`);
    } catch (error) {
      console.error(`[S3Bridge] Failed to cleanup S3 file ${s3Key}:`, error);
      // Don't throw - cleanup failures shouldn't break the main flow
    }
  }

  /**
   * Check if S3 file exists
   * @param s3Key The S3 object key to check
   */
  async fileExists(s3Key: string): Promise<boolean> {
    try {
      const headCommand = new HeadObjectCommand({
        Bucket: this.config.s3BucketName,
        Key: s3Key,
      });

      await s3Client.send(headCommand);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get S3 file info
   * @param s3Key The S3 object key
   */
  async getFileInfo(s3Key: string) {
    try {
      const headCommand = new HeadObjectCommand({
        Bucket: this.config.s3BucketName,
        Key: s3Key,
      });

      const response = await s3Client.send(headCommand);

      return {
        size: response.ContentLength,
        lastModified: response.LastModified,
        contentType: response.ContentType,
        metadata: response.Metadata,
      };
    } catch (error) {
      console.error(`[S3Bridge] Failed to get file info for ${s3Key}:`, error);
      return null;
    }
  }

  /**
   * Convert a readable stream to buffer
   */
  private async streamToBuffer(stream: Readable): Promise<Buffer> {
    const chunks: Buffer[] = [];

    return new Promise((resolve, reject) => {
      stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
      stream.on('error', (err) => reject(err));
      stream.on('end', () => resolve(Buffer.concat(chunks)));
    });
  }
}

// Export singleton instance
export const s3Bridge = new S3Bridge();

// Export utility functions
export async function copyDocumentToS3(r2Key: string, documentId: string): Promise<string> {
  return s3Bridge.copyFromR2ToS3(r2Key, documentId);
}

export async function cleanupS3Document(s3Key: string): Promise<void> {
  return s3Bridge.cleanupS3File(s3Key);
}
