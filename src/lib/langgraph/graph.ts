import { END, START, StateGraph } from '@langchain/langgraph';
import {
  checkHistoryRelevance,
  classifyQueryIntent,
  createResearchPlan,
  evaluateResults,
  generateAnswer,
  generateDirectResponse,
  handleFailure,
  refineQuery,
  resolveMaterialContext,
  retrieveContent,
  retrieveContext,
  retrieveSummaryContext,
} from './nodes';
import { AgentState, type AgentStateType } from './types';

/**
 * Routing function to determine next step after query classification
 */
function routeAfterClassification(state: AgentStateType): string {
  console.log(`🎯 Query classified as: ${state.queryIntent}, skip research: ${state.skipResearch}`);
  console.log(
    `🔍 Context needed: ${state.needSelectedContext}, History needed: ${state.needsHistory}`
  );

  if (state.skipResearch) {
    // For direct responses, the generateDirectResponse function will handle both history and context
    return 'generate_direct_response';
  }
  // Proceed directly to material resolution since history was already checked
  return 'resolve_material_context';
}

/**
 * Routing function to determine next step after history relevance check
 */
function routeAfterHistoryCheck(state: AgentStateType): string {
  console.log(`🔍 History needed: ${state.needsHistory}, reasoning: ${state.historyReasoning}`);

  // Always proceed to classification to determine both research needs AND context needs
  // This ensures needSelectedContext is properly set even for history queries
  return 'classify_query';
}

/**
 * Routing function to determine next step after research plan creation
 */
function routeAfterPlanCreation(state: AgentStateType): string {
  console.log(`🎯 Research plan created: ${state.planType}, target action: ${state.targetAction}`);

  // Execute the plan using the consolidated retrieval node
  switch (state.targetAction) {
    case 'retrieve_summary_context':
    case 'retrieve_context':
      return 'retrieve_content'; // Use consolidated node
    case 'generate_answer':
      return 'generate_answer';
    default:
      return 'retrieve_content'; // Default to consolidated node
  }
}

/**
 * Routing function to determine next step after material context resolution
 */
function routeAfterMaterialResolution(): string {
  console.log(`🎯 Material context resolved, creating informed research plan`);

  // Now that we know what materials are available, create an informed research plan
  return 'create_research_plan';
}

/**
 * Routing function to determine next step after evaluation
 */
function routeAfterEvaluation(state: AgentStateType): string {
  console.log(
    `🎯 Routing decision based on: ${state.evaluationResult}, retryCount: ${state.retryCount}, maxRetries: ${state.maxRetries}`
  );

  switch (state.evaluationResult) {
    case 'sufficient':
      return 'generate_answer';
    case 'need_refinement':
      return 'refine_query';
    case 'need_summary':
      // Check if we've already tried summary retrieval or hit max retries
      if (state.retryCount >= state.maxRetries) {
        console.log('🚫 Max retries reached, failing gracefully');
        return 'handle_failure';
      }

      // Check if we've already attempted retrieval multiple times
      if (state.retrievalAttempts < 2) {
        console.log('🔄 Attempting summary retrieval (limited attempts)');
        return 'retrieve_content'; // Use consolidated node with summaries_only strategy
      }

      // If we already tried retrieval multiple times, try query refinement instead
      console.log('🔄 Multiple retrieval attempts made, trying query refinement');
      return 'refine_query';
    case 'failed':
      return 'handle_failure';
    default:
      return 'handle_failure';
  }
}

/**
 * Routing function to determine next step after query refinement
 */
function routeAfterRefinement(state: AgentStateType): string {
  if (state.retryCount >= state.maxRetries) {
    return 'handle_failure';
  }
  return 'resolve_material_context';
}

/**
 * Build the LangGraph workflow
 */
export function createRAGAgent() {
  console.log('🏗️ Building RAG Agent with LangGraph...');

  const workflow = new StateGraph(AgentState)
    // Add all nodes
    .addNode('classify_query', classifyQueryIntent)
    .addNode('check_history_relevance', checkHistoryRelevance)
    .addNode('generate_direct_response', generateDirectResponse)
    .addNode('create_research_plan', createResearchPlan)
    .addNode('resolve_material_context', resolveMaterialContext)
    .addNode('retrieve_content', retrieveContent) // New consolidated retrieval node
    .addNode('retrieve_context', retrieveContext) // Legacy compatibility
    .addNode('retrieve_summary_context', retrieveSummaryContext) // Legacy compatibility
    .addNode('evaluate_results', evaluateResults)
    .addNode('refine_query', refineQuery)
    .addNode('generate_answer', generateAnswer)
    .addNode('handle_failure', handleFailure)

    // Set entry point - check history first
    .addEdge(START, 'check_history_relevance')

    // Routing from classification
    .addConditionalEdges('classify_query', routeAfterClassification)

    // Routing from history relevance check
    .addConditionalEdges('check_history_relevance', routeAfterHistoryCheck)

    // Routing from research plan creation
    .addConditionalEdges('create_research_plan', routeAfterPlanCreation)

    // Routing from material context resolution
    .addConditionalEdges('resolve_material_context', routeAfterMaterialResolution)

    // All retrieval paths go to evaluation
    .addEdge('retrieve_content', 'evaluate_results')
    .addEdge('retrieve_context', 'evaluate_results') // Legacy compatibility
    .addEdge('retrieve_summary_context', 'evaluate_results') // Legacy compatibility

    // Conditional routing after evaluation
    .addConditionalEdges('evaluate_results', routeAfterEvaluation)

    // Routing after refinement (back to material resolution)
    .addConditionalEdges('refine_query', routeAfterRefinement)

    // Terminal edges
    .addEdge('generate_answer', END)
    .addEdge('generate_direct_response', END)
    .addEdge('handle_failure', END);

  console.log('✅ RAG Agent workflow created');
  return workflow;
}

/**
 * Initialize and compile the RAG agent
 */
export function compileRAGAgent() {
  const workflow = createRAGAgent();
  const compiledAgent = workflow.compile();

  console.log('🚀 RAG Agent compiled and ready to use');
  return compiledAgent;
}

/**
 * Initialize state for a new conversation
 */
export function initializeAgentState(
  query: string,
  config: {
    spaceIds?: string[];
    documentIds?: string[];
    userId: string;
    chatId?: string;
  },
  selectedContext?: {
    documents?: { id: string; fileName: string }[];
    spaces?: { id: string; name: string; description?: string }[];
    filterScope: 'all' | 'documents' | 'spaces';
  }
): AgentStateType {
  return {
    originalQuery: query,
    currentQuery: query,
    queryIntent: 'research',
    skipResearch: false,
    needSelectedContext: false, // Default to false, will be set by AI classification
    refinedKeywords: [],
    keywordHistory: [], // Initialize empty keyword history
    retrievalAttempts: 0, // Initialize retrieval attempts counter
    reasoning: '',
    retrievedChunks: [],
    retrievedSummaries: [],
    evaluationResult: 'sufficient',
    retryCount: 0,
    maxRetries: 2,
    finalAnswer: '',
    references: [],
    error: null,
    config,
    selectedContext: selectedContext || { filterScope: 'all' },
    retrievalStrategy: 'chunks_only',
    targetMaterials: [],
    materialSelectionReasoning: '',
    needsHistory: false,
    historyReasoning: '',
    chatHistory: '',
    planType: 'keyword_search',
    targetAction: 'retrieve_context',
    needsKeywordGeneration: true,
    estimatedComplexity: 'moderate',
    documentCount: 10, // Default value, will be updated by AI analysis
  };
}

// Define custom event types
export type CustomEvent =
  | { type: 'step_start'; step: string; message: string }
  | { type: 'step_complete'; step: string }
  | { type: 'content_chunk'; content: string }
  | { type: 'step_progress'; step: string; data: Record<string, unknown> };

/**
 * Run the RAG agent with a query and progress tracking
 */
export async function runRAGAgent(
  query: string,
  config: {
    spaceIds?: string[];
    documentIds?: string[];
    userId: string;
    chatId?: string;
  },
  onProgress?: (nodeName: string, state: AgentStateType) => void,
  onCustomEvent?: (event: CustomEvent) => void
) {
  console.log(`🚀 Starting RAG Agent for query: "${query}"`);

  const agent = compileRAGAgent();
  const initialState = initializeAgentState(query, config);

  try {
    // Use both custom and updates stream modes
    const stream = await agent.stream(initialState, {
      streamMode: ['custom', 'updates'],
      recursionLimit: 30,
    });

    let finalState: AgentStateType = initialState;

    for await (const update of stream) {
      // Handle custom events (step starts, content chunks, etc.)
      if (Array.isArray(update) && update[0] === 'custom') {
        const event = update[1];
        if (onCustomEvent) {
          onCustomEvent(event as CustomEvent);
        }
      }

      // Handle state updates
      if (Array.isArray(update) && update[0] === 'updates') {
        console.log('🔍 Update:', update);
        const stateUpdates = update[1];
        const entries = Object.entries(stateUpdates);
        if (entries.length > 0) {
          const [nodeName, nodeState] = entries[0];

          // Merge the state update
          finalState = { ...finalState, ...(nodeState as Partial<AgentStateType>) };

          // Report progress with node name
          if (onProgress) {
            onProgress(nodeName, finalState);
          }

          console.log(`📍 Node: ${nodeName}`);
        }
      }
    }

    console.log('✅ RAG Agent execution completed');
    return finalState;
  } catch (error) {
    console.error('❌ RAG Agent execution failed:', error);

    return {
      ...initialState,
      evaluationResult: 'failed' as const,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      finalAnswer: 'I encountered an error while processing your request. Please try again later.',
    };
  }
}
