import { createHash } from 'crypto';
import type { AgentStateType } from './types';

/**
 * Context cache for storing AI analysis results to avoid redundant calls
 * 
 * This module provides intelligent caching for expensive AI operations like:
 * - Query classification
 * - Keyword generation
 * - Material selection
 * - Result evaluation
 */

interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  expiresAt: Date;
  queryHash: string;
  contextHash?: string;
}

interface QueryClassificationCache {
  queryIntent: 'research' | 'casual' | 'greeting' | 'clarification';
  skipResearch: boolean;
  needSelectedContext: boolean;
  reasoning: string;
}

interface KeywordGenerationCache {
  refinedKeywords: string[];
  reasoning: string;
  strategy: string;
}

interface MaterialSelectionCache {
  selectedMaterials: Array<{ id: string; fileName: string; reasoning: string }>;
  reasoning: string;
}

interface ResultEvaluationCache {
  hasRelevantInfo: boolean;
  informationQuality: 'excellent' | 'good' | 'fair' | 'poor';
  needsSummaryContext: boolean;
  nextAction: 'generate_answer' | 'retrieve_summary' | 'refine_query' | 'fail_gracefully';
  reasoning: string;
}

/**
 * In-memory cache with TTL support
 * In production, this could be replaced with Redis or similar
 */
class ContextCache {
  private queryClassificationCache = new Map<string, CacheEntry<QueryClassificationCache>>();
  private keywordGenerationCache = new Map<string, CacheEntry<KeywordGenerationCache>>();
  private materialSelectionCache = new Map<string, CacheEntry<MaterialSelectionCache>>();
  private resultEvaluationCache = new Map<string, CacheEntry<ResultEvaluationCache>>();

  // Cache TTL settings (in milliseconds)
  private readonly TTL = {
    queryClassification: 30 * 60 * 1000, // 30 minutes
    keywordGeneration: 15 * 60 * 1000,   // 15 minutes
    materialSelection: 10 * 60 * 1000,    // 10 minutes
    resultEvaluation: 5 * 60 * 1000,      // 5 minutes
  };

  /**
   * Generate a hash for cache key based on query and context
   */
  private generateHash(query: string, context?: any): string {
    const input = context ? `${query}:${JSON.stringify(context)}` : query;
    return createHash('sha256').update(input).digest('hex').substring(0, 16);
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    return new Date() > entry.expiresAt;
  }

  /**
   * Clean expired entries from a cache map
   */
  private cleanExpired<T>(cache: Map<string, CacheEntry<T>>): void {
    const now = new Date();
    for (const [key, entry] of cache.entries()) {
      if (now > entry.expiresAt) {
        cache.delete(key);
      }
    }
  }

  /**
   * Cache query classification result
   */
  setQueryClassification(query: string, result: QueryClassificationCache): void {
    const hash = this.generateHash(query);
    const expiresAt = new Date(Date.now() + this.TTL.queryClassification);
    
    this.queryClassificationCache.set(hash, {
      data: result,
      timestamp: new Date(),
      expiresAt,
      queryHash: hash,
    });

    // Clean expired entries periodically
    if (this.queryClassificationCache.size % 10 === 0) {
      this.cleanExpired(this.queryClassificationCache);
    }
  }

  /**
   * Get cached query classification result
   */
  getQueryClassification(query: string): QueryClassificationCache | null {
    const hash = this.generateHash(query);
    const entry = this.queryClassificationCache.get(hash);
    
    if (!entry || this.isExpired(entry)) {
      if (entry) {
        this.queryClassificationCache.delete(hash);
      }
      return null;
    }

    return entry.data;
  }

  /**
   * Cache keyword generation result
   */
  setKeywordGeneration(query: string, context: any, result: KeywordGenerationCache): void {
    const hash = this.generateHash(query, context);
    const expiresAt = new Date(Date.now() + this.TTL.keywordGeneration);
    
    this.keywordGenerationCache.set(hash, {
      data: result,
      timestamp: new Date(),
      expiresAt,
      queryHash: hash,
      contextHash: this.generateHash(JSON.stringify(context)),
    });

    if (this.keywordGenerationCache.size % 10 === 0) {
      this.cleanExpired(this.keywordGenerationCache);
    }
  }

  /**
   * Get cached keyword generation result
   */
  getKeywordGeneration(query: string, context: any): KeywordGenerationCache | null {
    const hash = this.generateHash(query, context);
    const entry = this.keywordGenerationCache.get(hash);
    
    if (!entry || this.isExpired(entry)) {
      if (entry) {
        this.keywordGenerationCache.delete(hash);
      }
      return null;
    }

    return entry.data;
  }

  /**
   * Cache material selection result
   */
  setMaterialSelection(query: string, materials: any[], result: MaterialSelectionCache): void {
    const hash = this.generateHash(query, materials);
    const expiresAt = new Date(Date.now() + this.TTL.materialSelection);
    
    this.materialSelectionCache.set(hash, {
      data: result,
      timestamp: new Date(),
      expiresAt,
      queryHash: hash,
      contextHash: this.generateHash(JSON.stringify(materials)),
    });

    if (this.materialSelectionCache.size % 10 === 0) {
      this.cleanExpired(this.materialSelectionCache);
    }
  }

  /**
   * Get cached material selection result
   */
  getMaterialSelection(query: string, materials: any[]): MaterialSelectionCache | null {
    const hash = this.generateHash(query, materials);
    const entry = this.materialSelectionCache.get(hash);
    
    if (!entry || this.isExpired(entry)) {
      if (entry) {
        this.materialSelectionCache.delete(hash);
      }
      return null;
    }

    return entry.data;
  }

  /**
   * Cache result evaluation
   */
  setResultEvaluation(query: string, results: any, result: ResultEvaluationCache): void {
    const hash = this.generateHash(query, results);
    const expiresAt = new Date(Date.now() + this.TTL.resultEvaluation);
    
    this.resultEvaluationCache.set(hash, {
      data: result,
      timestamp: new Date(),
      expiresAt,
      queryHash: hash,
      contextHash: this.generateHash(JSON.stringify(results)),
    });

    if (this.resultEvaluationCache.size % 10 === 0) {
      this.cleanExpired(this.resultEvaluationCache);
    }
  }

  /**
   * Get cached result evaluation
   */
  getResultEvaluation(query: string, results: any): ResultEvaluationCache | null {
    const hash = this.generateHash(query, results);
    const entry = this.resultEvaluationCache.get(hash);
    
    if (!entry || this.isExpired(entry)) {
      if (entry) {
        this.resultEvaluationCache.delete(hash);
      }
      return null;
    }

    return entry.data;
  }

  /**
   * Clear all caches
   */
  clearAll(): void {
    this.queryClassificationCache.clear();
    this.keywordGenerationCache.clear();
    this.materialSelectionCache.clear();
    this.resultEvaluationCache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      queryClassification: {
        size: this.queryClassificationCache.size,
        ttl: this.TTL.queryClassification,
      },
      keywordGeneration: {
        size: this.keywordGenerationCache.size,
        ttl: this.TTL.keywordGeneration,
      },
      materialSelection: {
        size: this.materialSelectionCache.size,
        ttl: this.TTL.materialSelection,
      },
      resultEvaluation: {
        size: this.resultEvaluationCache.size,
        ttl: this.TTL.resultEvaluation,
      },
    };
  }
}

// Singleton instance
export const contextCache = new ContextCache();

/**
 * Helper function to check if caching should be used
 */
export const shouldUseCache = (state: AgentStateType): boolean => {
  // Don't cache in testing mode to ensure consistent behavior
  if (process.env.NODE_ENV === 'test') {
    return false;
  }
  
  // Don't cache if explicitly disabled
  if (process.env.DISABLE_AI_CACHE === 'true') {
    return false;
  }
  
  // Don't cache for very short queries (likely not worth it)
  if (state.currentQuery.length < 10) {
    return false;
  }
  
  return true;
};

/**
 * Export types for use in other modules
 */
export type {
  QueryClassificationCache,
  KeywordGenerationCache,
  MaterialSelectionCache,
  ResultEvaluationCache,
};
