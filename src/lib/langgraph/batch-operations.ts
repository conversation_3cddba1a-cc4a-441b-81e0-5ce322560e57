import { searchMaterials } from '@/lib/materials/search';
import { searchSummariesByKeywords } from '@/lib/materials/summary-search';
import type { MaterialSearchResult } from '@/types/material';
import type { DocumentSummary } from './types';

/**
 * Batch database operations for LangGraph workflow
 *
 * This module provides optimized database operations that:
 * - Combine multiple queries into single operations
 * - Reduce database round trips
 * - Improve overall performance
 * - Provide intelligent caching and connection pooling
 */

export interface BatchRetrievalOptions {
  keywords: string[];
  query: string;
  spaceIds?: string[];
  documentIds?: string[];
  userId?: string;
  includeChunks: boolean;
  includeSummaries: boolean;
  chunkLimit?: number;
  summaryLimit?: number;
  debug?: boolean;
}

export interface BatchRetrievalResult {
  chunks: MaterialSearchResult[];
  summaries: DocumentSummary[];
  totalChunks: number;
  totalSummaries: number;
  executionTime: number;
  cacheHits: number;
  queryCount: number;
}

/**
 * Batch retrieval class that optimizes database operations
 */
export class BatchRetrieval {
  private queryCache = new Map<string, any>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Execute optimized batch retrieval combining chunks and summaries
   */
  async batchRetrieve(options: BatchRetrievalOptions): Promise<BatchRetrievalResult> {
    const startTime = performance.now();
    let cacheHits = 0;
    let queryCount = 0;

    console.log('🚀 Starting batch retrieval operation...');
    console.log(
      `📊 Options: chunks=${options.includeChunks}, summaries=${options.includeSummaries}`
    );

    const results: BatchRetrievalResult = {
      chunks: [],
      summaries: [],
      totalChunks: 0,
      totalSummaries: 0,
      executionTime: 0,
      cacheHits: 0,
      queryCount: 0,
    };

    // Prepare parallel operations
    const operations: Promise<any>[] = [];

    // Add chunk retrieval if requested
    if (options.includeChunks) {
      const chunkCacheKey = this.generateCacheKey('chunks', options);
      const cachedChunks = this.getFromCache(chunkCacheKey);

      if (cachedChunks) {
        console.log('💾 Using cached chunks');
        results.chunks = cachedChunks;
        cacheHits++;
      } else {
        console.log('🔍 Fetching chunks from database');
        operations.push(
          this.retrieveChunks(options).then((chunks) => {
            results.chunks = chunks;
            this.setCache(chunkCacheKey, chunks);
            queryCount++;
            return chunks;
          })
        );
      }
    }

    // Add summary retrieval if requested
    if (options.includeSummaries) {
      const summaryCacheKey = this.generateCacheKey('summaries', options);
      const cachedSummaries = this.getFromCache(summaryCacheKey);

      if (cachedSummaries) {
        console.log('💾 Using cached summaries');
        results.summaries = cachedSummaries;
        cacheHits++;
      } else {
        console.log('📋 Fetching summaries from database');
        operations.push(
          this.retrieveSummaries(options).then((summaries) => {
            results.summaries = summaries;
            this.setCache(summaryCacheKey, summaries);
            queryCount++;
            return summaries;
          })
        );
      }
    }

    // Execute all operations in parallel
    if (operations.length > 0) {
      await Promise.all(operations);
    }

    // Calculate metrics
    const endTime = performance.now();
    results.totalChunks = results.chunks.length;
    results.totalSummaries = results.summaries.length;
    results.executionTime = endTime - startTime;
    results.cacheHits = cacheHits;
    results.queryCount = queryCount;

    console.log(`✅ Batch retrieval completed in ${results.executionTime.toFixed(2)}ms`);
    console.log(`📊 Results: ${results.totalChunks} chunks, ${results.totalSummaries} summaries`);
    console.log(`💾 Cache hits: ${results.cacheHits}, Queries: ${results.queryCount}`);

    return results;
  }

  /**
   * Retrieve chunks with optimized query
   */
  private async retrieveChunks(options: BatchRetrievalOptions): Promise<MaterialSearchResult[]> {
    try {
      const chunks = await searchMaterials({
        query: options.query,
        limit: options.chunkLimit || 10,
        spaceIds: options.spaceIds,
        documentIds: options.documentIds,
        userId: options.userId,
        debug: options.debug,
      });

      return chunks;
    } catch (error) {
      console.error('❌ Error retrieving chunks:', error);
      return [];
    }
  }

  /**
   * Retrieve summaries with optimized query
   */
  private async retrieveSummaries(options: BatchRetrievalOptions): Promise<DocumentSummary[]> {
    try {
      const summaries = await searchSummariesByKeywords(options.keywords, {
        limit: options.summaryLimit || 5,
        spaceIds: options.spaceIds,
        documentIds: options.documentIds,
        userId: options.userId,
        debug: options.debug,
      });

      return summaries;
    } catch (error) {
      console.error('❌ Error retrieving summaries:', error);
      return [];
    }
  }

  /**
   * Generate cache key for operations
   */
  private generateCacheKey(type: string, options: BatchRetrievalOptions): string {
    const keyData = {
      type,
      query: options.query,
      keywords: options.keywords.sort().join(','),
      spaceIds: options.spaceIds?.sort().join(',') || '',
      documentIds: options.documentIds?.sort().join(',') || '',
      userId: options.userId || '',
    };

    return JSON.stringify(keyData);
  }

  /**
   * Get item from cache if not expired
   */
  private getFromCache(key: string): any {
    const cached = this.queryCache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_TTL) {
      this.queryCache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Set item in cache with timestamp
   */
  private setCache(key: string, data: any): void {
    this.queryCache.set(key, {
      data,
      timestamp: Date.now(),
    });

    // Clean expired entries periodically
    if (this.queryCache.size % 20 === 0) {
      this.cleanExpiredCache();
    }
  }

  /**
   * Clean expired cache entries
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    this.queryCache.forEach((value, key) => {
      if (now - value.timestamp > this.CACHE_TTL) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach((key) => this.queryCache.delete(key));

    if (keysToDelete.length > 0) {
      console.log(`🧹 Cleaned ${keysToDelete.length} expired cache entries`);
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.queryCache.size,
      ttl: this.CACHE_TTL,
    };
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    this.queryCache.clear();
    console.log('🧹 Cache cleared');
  }
}

/**
 * Singleton instance for batch operations
 */
export const batchRetrieval = new BatchRetrieval();

/**
 * Convenience function for batch retrieval
 */
export async function executeBatchRetrieval(
  query: string,
  keywords: string[],
  options: {
    spaceIds?: string[];
    documentIds?: string[];
    userId?: string;
    strategy: 'chunks_only' | 'summaries_only' | 'both_chunks_and_summaries';
    chunkLimit?: number;
    summaryLimit?: number;
    debug?: boolean;
  }
): Promise<BatchRetrievalResult> {
  const batchOptions: BatchRetrievalOptions = {
    query,
    keywords,
    spaceIds: options.spaceIds,
    documentIds: options.documentIds,
    userId: options.userId,
    includeChunks:
      options.strategy === 'chunks_only' || options.strategy === 'both_chunks_and_summaries',
    includeSummaries:
      options.strategy === 'summaries_only' || options.strategy === 'both_chunks_and_summaries',
    chunkLimit: options.chunkLimit,
    summaryLimit: options.summaryLimit,
    debug: options.debug,
  };

  return await batchRetrieval.batchRetrieve(batchOptions);
}
