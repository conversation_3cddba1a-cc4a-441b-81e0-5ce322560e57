import { generateObject } from 'ai';
import { azure } from '@/lib/azure/openai';
import { z } from 'zod';
import type { AgentStateType } from './types';
import { conditionalAICall, isAITestingEnabled, mockResponses } from './testing-config';

/**
 * Adaptive strategy selection for LangGraph workflow
 * 
 * This module provides AI-driven strategy selection that:
 * - Analyzes query characteristics and complexity
 * - Considers available context and resources
 * - Adapts based on previous performance
 * - Makes confidence-based decisions
 * - Optimizes for both quality and performance
 */

/**
 * Schema for adaptive strategy decisions
 */
const AdaptiveStrategySchema = z.object({
  retrievalStrategy: z.enum([
    'chunks_only',
    'summaries_only', 
    'both_chunks_and_summaries'
  ]).describe('Optimal retrieval strategy for this query'),
  
  confidence: z.number().min(0).max(1).describe('Confidence level in this strategy choice (0-1)'),
  
  reasoning: z.string().describe('Detailed reasoning for why this strategy was chosen'),
  
  estimatedQuality: z.enum(['high', 'medium', 'low']).describe('Expected quality of results with this strategy'),
  
  estimatedSpeed: z.enum(['fast', 'medium', 'slow']).describe('Expected response time with this strategy'),
  
  documentCount: z.number().min(3).max(20).describe('Optimal number of documents to retrieve'),
  
  fallbackStrategy: z.enum([
    'chunks_only',
    'summaries_only',
    'both_chunks_and_summaries'
  ]).describe('Fallback strategy if primary choice fails'),
  
  adaptations: z.array(z.string()).describe('Specific adaptations based on query characteristics'),
});

type AdaptiveStrategy = z.infer<typeof AdaptiveStrategySchema>;

/**
 * Performance tracking for strategy effectiveness
 */
interface StrategyPerformance {
  strategy: string;
  queryType: string;
  successRate: number;
  averageQuality: number;
  averageSpeed: number;
  usageCount: number;
  lastUsed: Date;
}

/**
 * Adaptive strategy selector that learns and improves over time
 */
export class AdaptiveStrategySelector {
  private performanceHistory = new Map<string, StrategyPerformance>();
  private readonly PERFORMANCE_WINDOW = 100; // Keep last 100 strategy uses

  /**
   * Select optimal strategy based on query analysis and historical performance
   */
  async selectStrategy(state: AgentStateType): Promise<AdaptiveStrategy> {
    const mockResponse: AdaptiveStrategy = {
      retrievalStrategy: 'chunks_only',
      confidence: 0.8,
      reasoning: 'TESTING: Default chunks-only strategy for testing',
      estimatedQuality: 'medium',
      estimatedSpeed: 'fast',
      documentCount: 10,
      fallbackStrategy: 'both_chunks_and_summaries',
      adaptations: ['standard_retrieval'],
    };

    return await conditionalAICall(mockResponse, async () => {
      // Analyze query characteristics
      const queryAnalysis = this.analyzeQuery(state);
      
      // Get historical performance data
      const performanceContext = this.getPerformanceContext(queryAnalysis.type);
      
      // Generate AI-driven strategy decision
      const result = await generateObject({
        model: azure('gpt-4o-mini'),
        schema: AdaptiveStrategySchema,
        prompt: `
          Analyze this query and select the optimal retrieval strategy.

          Query Analysis:
          - Query: "${state.currentQuery}"
          - Intent: ${state.queryIntent}
          - Type: ${queryAnalysis.type}
          - Complexity: ${queryAnalysis.complexity}
          - Keywords Available: ${state.refinedKeywords?.length || 0}
          - Selected Materials: ${state.targetMaterials?.length || 0}
          - Previous Attempts: ${state.retrievalAttempts || 0}

          Available Context:
          - Documents Selected: ${state.config.documentIds?.length || 0}
          - Spaces Selected: ${state.config.spaceIds?.length || 0}
          - Filter Scope: ${state.selectedContext?.filterScope || 'all'}

          Historical Performance:
          ${performanceContext}

          Strategy Options:
          1. chunks_only - Fast, detailed content, good for specific questions
          2. summaries_only - Fast, high-level overview, good for broad questions
          3. both_chunks_and_summaries - Comprehensive but slower, good for complex analysis

          Consider:
          - Query complexity and specificity
          - User's apparent information need
          - Available context and materials
          - Performance trade-offs (speed vs comprehensiveness)
          - Historical success rates for similar queries

          Select the strategy that will provide the best user experience for this specific query.
        `,
      });

      // Record this decision for future learning
      this.recordStrategyDecision(queryAnalysis.type, result.object);

      return result.object;
    });
  }

  /**
   * Analyze query characteristics to inform strategy selection
   */
  private analyzeQuery(state: AgentStateType): {
    type: string;
    complexity: 'low' | 'medium' | 'high';
    specificity: 'broad' | 'specific' | 'targeted';
    informationNeed: 'overview' | 'details' | 'analysis';
  } {
    const query = state.currentQuery.toLowerCase();
    const queryLength = query.length;
    const wordCount = query.split(/\s+/).length;

    // Determine query type
    let type = 'general';
    if (query.includes('summary') || query.includes('overview')) type = 'summary';
    else if (query.includes('compare') || query.includes('difference')) type = 'comparison';
    else if (query.includes('how') || query.includes('step')) type = 'procedural';
    else if (query.includes('what') || query.includes('define')) type = 'factual';

    // Determine complexity
    let complexity: 'low' | 'medium' | 'high' = 'medium';
    if (wordCount <= 5 && !query.includes('and') && !query.includes('or')) complexity = 'low';
    else if (wordCount > 15 || query.includes('compare') || query.includes('analyze')) complexity = 'high';

    // Determine specificity
    let specificity: 'broad' | 'specific' | 'targeted' = 'specific';
    if (query.includes('overview') || query.includes('general')) specificity = 'broad';
    else if (state.targetMaterials?.length === 1) specificity = 'targeted';

    // Determine information need
    let informationNeed: 'overview' | 'details' | 'analysis' = 'details';
    if (query.includes('summary') || query.includes('overview')) informationNeed = 'overview';
    else if (query.includes('analyze') || query.includes('compare')) informationNeed = 'analysis';

    return { type, complexity, specificity, informationNeed };
  }

  /**
   * Get performance context for similar queries
   */
  private getPerformanceContext(queryType: string): string {
    const relevantPerformance = Array.from(this.performanceHistory.values())
      .filter(p => p.queryType === queryType)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, 3);

    if (relevantPerformance.length === 0) {
      return 'No historical performance data available for this query type.';
    }

    return relevantPerformance
      .map(p => 
        `${p.strategy}: ${(p.successRate * 100).toFixed(0)}% success, ` +
        `quality ${p.averageQuality.toFixed(1)}/5, used ${p.usageCount} times`
      )
      .join('\n');
  }

  /**
   * Record strategy decision for learning
   */
  private recordStrategyDecision(queryType: string, strategy: AdaptiveStrategy): void {
    const key = `${queryType}_${strategy.retrievalStrategy}`;
    
    const existing = this.performanceHistory.get(key);
    if (existing) {
      existing.usageCount++;
      existing.lastUsed = new Date();
    } else {
      this.performanceHistory.set(key, {
        strategy: strategy.retrievalStrategy,
        queryType,
        successRate: 0.5, // Start with neutral assumption
        averageQuality: 3.0, // Start with medium quality
        averageSpeed: 3.0, // Start with medium speed
        usageCount: 1,
        lastUsed: new Date(),
      });
    }

    // Clean old entries if we exceed the window
    if (this.performanceHistory.size > this.PERFORMANCE_WINDOW) {
      this.cleanOldPerformanceData();
    }
  }

  /**
   * Update strategy performance based on results
   */
  updateStrategyPerformance(
    queryType: string,
    strategy: string,
    success: boolean,
    quality: number,
    speed: number
  ): void {
    const key = `${queryType}_${strategy}`;
    const performance = this.performanceHistory.get(key);
    
    if (performance) {
      // Update with exponential moving average
      const alpha = 0.2; // Learning rate
      
      performance.successRate = performance.successRate * (1 - alpha) + (success ? 1 : 0) * alpha;
      performance.averageQuality = performance.averageQuality * (1 - alpha) + quality * alpha;
      performance.averageSpeed = performance.averageSpeed * (1 - alpha) + speed * alpha;
      performance.lastUsed = new Date();
    }
  }

  /**
   * Clean old performance data to maintain window size
   */
  private cleanOldPerformanceData(): void {
    const entries = Array.from(this.performanceHistory.entries())
      .sort(([, a], [, b]) => b.lastUsed.getTime() - a.lastUsed.getTime())
      .slice(0, this.PERFORMANCE_WINDOW);

    this.performanceHistory.clear();
    entries.forEach(([key, value]) => {
      this.performanceHistory.set(key, value);
    });
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(): {
    totalStrategies: number;
    topPerformers: Array<{ strategy: string; queryType: string; successRate: number }>;
    recentActivity: number;
  } {
    const entries = Array.from(this.performanceHistory.values());
    const recentThreshold = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    
    const topPerformers = entries
      .filter(p => p.usageCount >= 3) // Only include strategies used at least 3 times
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, 5)
      .map(p => ({
        strategy: p.strategy,
        queryType: p.queryType,
        successRate: p.successRate,
      }));

    const recentActivity = entries.filter(p => p.lastUsed > recentThreshold).length;

    return {
      totalStrategies: entries.length,
      topPerformers,
      recentActivity,
    };
  }

  /**
   * Reset performance history (for testing or maintenance)
   */
  resetPerformanceHistory(): void {
    this.performanceHistory.clear();
    console.log('🧹 Strategy performance history reset');
  }
}

/**
 * Singleton instance for adaptive strategy selection
 */
export const adaptiveStrategySelector = new AdaptiveStrategySelector();
