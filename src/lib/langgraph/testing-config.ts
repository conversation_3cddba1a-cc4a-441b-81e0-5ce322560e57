import { env } from '@/lib/env.mjs';

/**
 * Centralized testing configuration for LangGraph nodes
 *
 * This module provides a consistent way to handle testing mode across all nodes,
 * replacing the hard-coded TESTING_MODE flag with environment-based configuration.
 */

/**
 * Determines if AI testing mode is enabled
 * When true, nodes will use mock responses instead of real AI calls to save tokens
 */
export const isAITestingEnabled = (): boolean => {
  return env.NODE_ENV === 'development' && env.USE_AI_TESTING === 'true';
};

/**
 * Helper function to simulate AI delays for realistic testing
 * Provides variable delay to simulate real AI response times
 */
export const simulateAIDelay = (): Promise<void> => {
  const baseDelay = 500;
  const randomDelay = Math.random() * 1000;
  return new Promise((resolve) => setTimeout(resolve, baseDelay + randomDelay));
};

/**
 * Testing configuration options
 */
export interface TestingConfig {
  /** Whether to use mock AI responses */
  useMockAI: boolean;
  /** Whether to use real database data in testing */
  useRealData: boolean;
  /** Whether to simulate realistic delays */
  simulateDelays: boolean;
  /** Whether to log detailed testing information */
  verboseLogging: boolean;
}

/**
 * Get current testing configuration
 */
export const getTestingConfig = (): TestingConfig => {
  const isTestingMode = isAITestingEnabled();

  return {
    useMockAI: isTestingMode,
    useRealData: true, // Always use real data as per user preference
    simulateDelays: isTestingMode,
    verboseLogging: isTestingMode,
  };
};

/**
 * Log testing information if verbose logging is enabled
 */
export const logTestingInfo = (message: string, data?: unknown): void => {
  const config = getTestingConfig();
  if (config.verboseLogging) {
    console.log(`🧪 TESTING: ${message}`, data || '');
  }
};

/**
 * Wrapper function for conditional AI calls
 * Uses mock response if testing mode is enabled, otherwise makes real AI call
 */
export const conditionalAICall = async <T>(
  mockResponse: Partial<T>,
  aiCall: () => Promise<T>
): Promise<T> => {
  const config = getTestingConfig();

  if (config.useMockAI) {
    if (config.simulateDelays) {
      await simulateAIDelay();
    }
    logTestingInfo('Using mock AI response');
    return mockResponse as T;
  }

  return await aiCall();
};

/**
 * Mock responses for common AI operations
 */
export const mockResponses = {
  queryClassification: {
    intent: 'research' as const,
    needsResearch: true,
    needSelectedContext: true, // Changed to true for selected document queries
    reasoning: 'TESTING: Query requires document research with selected context',
  },

  historyRelevance: {
    needsHistory: false,
    historyReasoning: 'TESTING: Query is self-contained',
  },

  materialSelection: {
    selectedMaterials: [],
    reasoning: 'TESTING: Using all available materials',
  },

  keywordGeneration: {
    refinedKeywords: ['test', 'keyword', 'example'],
    reasoning: 'TESTING: Generated example keywords',
  },

  resultEvaluation: {
    hasRelevantInfo: true,
    informationQuality: 'good' as const,
    needsSummaryContext: false,
    nextAction: 'generate_answer' as const,
    reasoning: 'TESTING: Sufficient information found',
  },

  queryRefinement: {
    refinedKeywords: ['refined', 'improved', 'better'],
    reasoning: 'TESTING: Using more specific keywords',
    strategy: 'chunks_only' as const,
  },

  researchPlan: {
    planType: 'direct_summary' as const,
    targetAction: 'retrieve_summary_context' as const,
    retrievalStrategy: 'summaries_only' as const,
    needsKeywordGeneration: false,
    reasoning: 'TESTING: Using summary retrieval for overview questions',
    estimatedComplexity: 'simple' as const,
  },
};

/**
 * Helper to create consistent mock data for testing
 */
export const createMockTestData = () => {
  return {
    chunks: [
      {
        id: 1,
        content: '🧪 TESTING: Mock chunk content for testing purposes',
        similarity: 0.95,
        fileName: 'test-document.pdf',
        fileType: 'pdf' as const,
        key: 'test-key-1',
      },
    ],
    summaries: [
      {
        id: 'test-doc-1',
        fileName: 'test-document.pdf',
        summary: '🧪 TESTING: Mock summary content for testing',
        similarity: 0.9,
        key: 'test-key-1',
        fileType: 'pdf' as const,
      },
    ],
  };
};
