import { db } from '@/database/drizzle';
import * as schema from '@/database/schema';
import { DrizzlePostgreSQLAdapter } from '@lucia-auth/adapter-drizzle';
import { verify } from '@node-rs/argon2';
import { Lucia, Session, User } from 'lucia';
import { cookies } from 'next/headers';
import { cache } from 'react';

const adapter = new DrizzlePostgreSQLAdapter(db, schema.sessions, schema.users);

export const lucia = new Lucia(adapter, {
  sessionCookie: {
    attributes: {
      secure: process.env.NODE_ENV === 'production',
    },
    name: 'knowledge_session',
  },
  getUserAttributes: (user) => ({
    name: user.name,
    email: user.email,
    emailVerified: user.emailVerified,
  }),
});

export const verifyPassword = async (hashedPassword: string, password: string) => {
  return await verify(hashedPassword, password);
};

export const validateRequest = async () => {
  const cookieStore = await cookies();
  const sessionId = cookieStore.get(lucia.sessionCookieName)?.value ?? null;
  if (!sessionId) {
    return {
      user: null,
      session: null,
    };
  }

  const result = await lucia.validateSession(sessionId);
  try {
    if (result.session && result.session.fresh) {
      const sessionCookie = lucia.createSessionCookie(result.session.id);
      cookieStore.set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);
    }
    if (!result.session) {
      const sessionCookie = lucia.createBlankSessionCookie();
      cookieStore.set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);
    }
  } catch {}
  return result;
};

export const getUser = cache(async (): Promise<{ user: User | null; session: Session | null }> => {
  const { user, session } = await validateRequest();
  return { user, session };
});

declare module 'lucia' {
  interface Register {
    Lucia: typeof lucia;
    DatabaseUserAttributes: {
      name: string;
      email: string;
      emailVerified: boolean;
    };
  }
}
