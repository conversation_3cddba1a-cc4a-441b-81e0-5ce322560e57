import { MaterialRepository } from '@/database/repository/material';

/**
 * Simple fallback functions for when Upstash workflows are not available
 * These just update the database status - no complex event routing
 */

/**
 * Handle Textract completion - simple database update fallback
 */
export async function triggerTextractCompletionEvent(
  jobId: string,
  documentId: string,
  status: 'SUCCEEDED' | 'FAILED' | 'PARTIAL_SUCCESS',
  snsMessageId?: string
): Promise<void> {
  try {
    console.log(`[Fallback] Processing textract completion for job ${jobId}`);

    // Just update the database status - the workflow should use proper Upstash events
    await MaterialRepository.updateTextractJobStatus(jobId, status, snsMessageId, new Date());

    console.log(`[Fallback] Updated job status in database for ${jobId}: ${status}`);
  } catch (error) {
    console.error(`[Fallback] Failed to process completion for job ${jobId}:`, error);
    throw error;
  }
}

/**
 * Handle Textract failure - simple database update fallback
 */
export async function handleTextractFailureEvent(
  jobId: string,
  documentId: string,
  error: string
): Promise<void> {
  try {
    console.log(`[Fallback] Handling textract failure for job ${jobId}: ${error}`);

    // Update document status to failed
    await MaterialRepository.updateProcessingError(documentId, `Textract job failed: ${error}`);

    // Update job status
    await MaterialRepository.updateTextractJobStatus(jobId, 'FAILED', undefined, new Date());

    console.log(`[Fallback] Successfully handled failure for job ${jobId}`);
  } catch (handlingError) {
    console.error(`[Fallback] Failed to handle textract failure:`, handlingError);
    throw handlingError;
  }
}
