import { db } from '@/database/drizzle';
import { MaterialRepository } from '@/database/repository/material';
import { SpaceRepository } from '@/database/repository/space';
import { documents } from '@/database/schema';
import { cleanupS3Document, copyDocumentToS3 } from '@/lib/aws/s3';
import {
  getAsyncDocumentAnalysis,
  processTextractBlocksToChunks,
  startAsyncDocumentAnalysis,
  validateAsyncConfiguration,
  type TextractResult,
} from '@/lib/aws/textract';
import { generateEmbeddings } from '@/lib/azure/openai';
import { env } from '@/lib/env.mjs';
import { generateDocumentSummary } from '@/lib/materials/summarizer';
import { WorkflowContext } from '@upstash/workflow';
import { eq } from 'drizzle-orm';

export interface DocumentProcessingPayload {
  documentId: string;
  fileKey: string;
  fileName: string;
  fileType: string;
  uploadedBy: string;
  spaceIds: string[];
  debug?: boolean;
}

export interface ChunkData {
  content: string;
  chunkOrder: number;
  metadata: Record<string, unknown>;
  embedding?: number[];
  boundingBox?: unknown;
  polygonCoordinates?: unknown;
  layoutType?: string;
  confidenceScore?: number;
}

export interface ChunkDataWithEmbedding extends ChunkData {
  embedding: number[];
}

export interface SummaryData {
  summary: string;
  summaryEmbedding: number[] | null;
  keyTopics: string[];
  documentType: string;
}

export interface StepResult {
  success: boolean;
  error?: string;
  data?: unknown;
}

interface Step2Data {
  chunks: ChunkData[];
  ocrAnalysis?: unknown;
}

interface Step3Data {
  chunksWithEmbeddings: ChunkDataWithEmbedding[];
}

/**
 * Step 1: Download and prepare document from R2
 * This step verifies the document exists and is accessible
 */
export async function step1PrepareDocument(
  payload: DocumentProcessingPayload
): Promise<StepResult> {
  const { documentId, fileKey, fileName, fileType } = payload;

  // Check if document is already completed to prevent reprocessing
  const documents = await MaterialRepository.getMaterialById(documentId);
  const document = documents[0];

  if (document && document.status === 'completed') {
    throw new Error('Document already processed and completed');
  }

  // Update document status to processing (legacy status, will be changed to granular statuses in subsequent steps)
  await MaterialRepository.updateMaterialStatus(documentId, 'uploaded');

  // Validate file exists and parameters are correct
  if (!fileKey || !fileName || !fileType) {
    throw new Error('Missing required file parameters');
  }

  console.log(`[Workflow] Step 1 completed for ${fileName}`);
  return {
    success: true,
    data: { documentId, fileKey, fileName, fileType },
  };
}

/**
 * Step 2a: Initialize OCR extraction - determine sync vs async and start processing
 */
export async function step2InitOCRExtraction(
  payload: DocumentProcessingPayload
): Promise<StepResult> {
  const { documentId, fileName } = payload;

  // Update status to OCR extracting and record start time
  await MaterialRepository.updateMaterialStatus(documentId, 'ocr_extracting');
  const ocrStartTime = new Date();
  await MaterialRepository.updateProcessingTiming(documentId, 'ocr_extracting', ocrStartTime);

  console.log(`[Workflow] Step 2a: Initializing OCR extraction for ${fileName}`);

  // Always use async processing for consistent workflow
  console.log(
    `[Workflow] Starting async Textract processing for ${fileName} (consistent workflow approach)`
  );

  // Validate async configuration is available
  const asyncConfig = await validateAsyncConfiguration();
  if (!asyncConfig.isValid) {
    throw new Error(
      `Async Textract configuration invalid, missing: ${asyncConfig.missingConfig.join(', ')}`
    );
  }

  const jobId = await startAsyncTextractJob(payload);

  return {
    success: true,
    data: { jobId },
  };
}

/**
 * Step 2c: Process async Textract results after event completion
 */
export async function step2ProcessAsyncResults(
  payload: DocumentProcessingPayload,
  jobId: string,
  eventData: unknown
): Promise<StepResult> {
  const { documentId, fileName } = payload;

  console.log(`[Workflow] Step 2c: Processing async results for ${fileName}, job: ${jobId}`);

  // Check if job succeeded
  const completionEvent = eventData as { status: string; jobId: string; documentId: string };
  if (completionEvent.status !== 'SUCCEEDED') {
    throw new Error(`Textract job failed with status: ${completionEvent.status}`);
  }

  // Get Textract results
  const textractResult = await getAsyncDocumentAnalysis(jobId);

  // Convert Textract results to chunks format
  const chunks = convertTextractToChunks(textractResult);

  // Store OCR analysis
  await MaterialRepository.storeOcrAnalysis(
    documentId,
    textractResult,
    0 // Processing time tracked separately
  );

  // Record OCR completion time
  const ocrEndTime = new Date();
  await MaterialRepository.updateProcessingTiming(
    documentId,
    'ocr_extracting',
    undefined,
    ocrEndTime
  );

  // Cleanup S3 document after successful processing
  const s3Key = `textract-input/${documentId}/${payload.fileName}`;
  try {
    await cleanupS3Document(s3Key);
    console.log(`[Workflow] Successfully cleaned up S3 file for document ${documentId}`);
  } catch (cleanupError) {
    console.warn(`[Workflow] Failed to cleanup S3 file for ${documentId}:`, cleanupError);
    // Don't fail the main workflow if cleanup fails
  }

  console.log(`[Workflow] Step 2c completed for ${fileName}: ${chunks.length} chunks extracted`);

  return {
    success: true,
    data: { chunks },
  };
}

/**
 * Start async Textract job and return job ID
 */
async function startAsyncTextractJob(payload: DocumentProcessingPayload): Promise<string> {
  const { documentId, fileKey, fileName } = payload;

  let s3Key: string | null = null;

  console.log(`[Workflow] Starting async Textract job for ${fileName}`);

  // Copy document from R2 to S3
  s3Key = await copyDocumentToS3(fileKey, documentId);
  console.log(`[Workflow] Copied ${fileName} to S3: ${s3Key}`);

  // Start async Textract job
  const jobId = await startAsyncDocumentAnalysis(
    env.AWS_S3_BUCKET!,
    s3Key,
    env.AWS_SNS_TOPIC_ARN!,
    env.AWS_ROLE_ARN!,
    `${documentId}-${Date.now()}`
  );

  console.log(`[Workflow] Started async Textract job: ${jobId} for ${fileName}`);

  // Store job ID in database
  await MaterialRepository.storeTextractJobId(documentId, jobId);

  return jobId;
}

/**
 * BACKUP: Process document using existing sync method (commented out for consistent workflow)
 */
/* 
async function processSyncTextract(payload: DocumentProcessingPayload): Promise<StepResult> {
  const { documentId, fileKey, fileName, fileType, debug = false } = payload;

  // Use the existing processMaterialFromR2Enhanced function
  const { chunks, ocrAnalysis } = await processMaterialFromR2Enhanced(
    fileKey,
    fileName,
    fileType,
    debug
  );

  // Store OCR analysis if available
  if (ocrAnalysis) {
    await MaterialRepository.storeOcrAnalysis(
      documentId,
      ocrAnalysis,
      0 // Processing time will be tracked separately
    );
  }

  // Record OCR completion time
  const ocrEndTime = new Date();
  await MaterialRepository.updateProcessingTiming(
    documentId,
    'ocr_extracting',
    undefined,
    ocrEndTime
  );

  console.log(`[Workflow] Sync processing completed: extracted ${chunks.length} chunks`);
  return {
    success: true,
    data: { chunks, ocrAnalysis },
  };
}
*/

/**
 * Convert Textract results to chunks format compatible with existing system
 */
function convertTextractToChunks(textractResult: TextractResult): ChunkData[] {
  const chunks = processTextractBlocksToChunks(textractResult);

  return chunks.map(
    (
      chunk: {
        content: string;
        layoutTypes: string[];
        confidenceScore: number;
        geometry: Array<{ boundingBox?: unknown; polygon?: unknown }>;
        pageIndex: number;
      },
      index: number
    ) => ({
      content: chunk.content,
      chunkOrder: index,
      metadata: {
        layoutTypes: chunk.layoutTypes,
        confidenceScore: chunk.confidenceScore,
        source: 'async-textract',
        pageIndex: chunk.pageIndex, // Include pageIndex in metadata
      },
      boundingBox: chunk.geometry.map((g) => g.boundingBox), // Store ALL bounding boxes
      polygonCoordinates: chunk.geometry.map((g) => g.polygon), // Store ALL polygons
      layoutType: chunk.layoutTypes[0] || 'UNKNOWN',
      confidenceScore: Math.round((chunk.confidenceScore || 0) * 100),
    })
  );
}

/**
 * Step 3: Generate embeddings for chunks
 * This step processes all chunks and generates their embeddings
 */
export async function step3GenerateEmbeddings(
  payload: DocumentProcessingPayload,
  chunks: ChunkData[]
): Promise<StepResult> {
  const { documentId, debug = false } = payload;

  // Update status to embedding and record start time
  await MaterialRepository.updateMaterialStatus(documentId, 'embedding');
  const embeddingStartTime = new Date();
  await MaterialRepository.updateProcessingTiming(documentId, 'embedding', embeddingStartTime);

  console.log(`[Workflow] Step 3: Generating embeddings for ${chunks.length} chunks`);

  // Handle empty chunks case
  if (chunks.length === 0) {
    console.warn(`[Workflow] Step 3: No chunks to process for embeddings`);

    // Record embedding completion time even if no chunks to process
    const embeddingEndTime = new Date();
    await MaterialRepository.updateProcessingTiming(
      documentId,
      'embedding',
      undefined,
      embeddingEndTime
    );

    return {
      success: true,
      data: { chunksWithEmbeddings: [] },
    };
  }

  // Extract chunk contents for embedding generation
  const chunkContents = chunks.map((chunk) => chunk.content);

  // Validate chunk contents
  const validChunkContents = chunkContents.filter(
    (content) => content && content.trim().length > 0
  );
  if (validChunkContents.length === 0) {
    console.warn(`[Workflow] Step 3: No valid chunk contents found`);

    // Record embedding completion time even if no valid chunks
    const embeddingEndTime = new Date();
    await MaterialRepository.updateProcessingTiming(
      documentId,
      'embedding',
      undefined,
      embeddingEndTime
    );

    return {
      success: true,
      data: { chunksWithEmbeddings: [] },
    };
  }

  // Generate embeddings in batches
  const embeddings = await generateEmbeddings(validChunkContents, 400, debug);

  // Combine chunks with embeddings (only for chunks with valid content)
  const chunksWithEmbeddings = chunks
    .filter((chunk) => chunk.content && chunk.content.trim().length > 0)
    .map((chunk, index) => ({
      ...chunk,
      embedding: embeddings[index],
    }));

  // Record embedding completion time
  const embeddingEndTime = new Date();
  await MaterialRepository.updateProcessingTiming(
    documentId,
    'embedding',
    undefined,
    embeddingEndTime
  );

  console.log(`[Workflow] Step 3 completed: generated ${embeddings.length} embeddings`);
  return {
    success: true,
    data: { chunksWithEmbeddings },
  };
}

/**
 * Step 4: Generate document summary
 * This step creates a comprehensive summary of the document
 */
export async function step4GenerateSummary(
  payload: DocumentProcessingPayload,
  chunksWithEmbeddings: ChunkDataWithEmbedding[]
): Promise<StepResult> {
  const { documentId, fileName, debug = false } = payload;

  // Update status to summarizing and record start time
  await MaterialRepository.updateMaterialStatus(documentId, 'summarizing');
  const summarizingStartTime = new Date();
  await MaterialRepository.updateProcessingTiming(documentId, 'summarizing', summarizingStartTime);

  console.log(`[Workflow] Step 4: Generating summary for ${fileName}`);

  // Handle empty chunks case
  if (chunksWithEmbeddings.length === 0) {
    console.warn(`[Workflow] Step 4: No chunks to process for summary generation`);

    // Record summarizing completion time even if no chunks to process
    const summarizingEndTime = new Date();
    await MaterialRepository.updateProcessingTiming(
      documentId,
      'summarizing',
      undefined,
      summarizingEndTime
    );

    return {
      success: true,
      data: null,
    };
  }

  // Generate document summary - let errors bubble up for non-critical step
  try {
    const summaryData = await generateDocumentSummary(
      chunksWithEmbeddings.map((chunk) => ({
        content: chunk.content,
        chunkOrder: chunk.chunkOrder,
      })),
      fileName,
      debug
    );

    // Record summarizing completion time
    const summarizingEndTime = new Date();
    await MaterialRepository.updateProcessingTiming(
      payload.documentId,
      'summarizing',
      undefined,
      summarizingEndTime
    );

    console.log(`[Workflow] Step 4 completed: generated summary for ${fileName}`);
    return {
      success: true,
      data: summaryData,
    };
  } catch (error) {
    // Record summarizing completion time even if failed
    const summarizingEndTime = new Date();
    await MaterialRepository.updateProcessingTiming(
      payload.documentId,
      'summarizing',
      undefined,
      summarizingEndTime
    );

    console.warn(
      `[Workflow] Step 4: Summary generation failed, continuing without summary:`,
      error
    );
    // Summary generation is non-critical - return null instead of throwing
    return {
      success: true,
      data: null,
    };
  }
}

/**
 * Step 5: Finalize document processing
 * This step stores all data and marks the document as completed
 */
export async function step5FinalizeDocument(
  payload: DocumentProcessingPayload,
  chunksWithEmbeddings: ChunkDataWithEmbedding[],
  summaryData: SummaryData | null
): Promise<StepResult> {
  const { documentId, spaceIds } = payload;

  console.log(`[Workflow] Step 5: Finalizing document ${documentId}`);

  // Handle empty chunks case - still mark as completed but with warning
  if (chunksWithEmbeddings.length === 0) {
    console.warn(`[Workflow] Step 5: No chunks to store for document ${documentId}`);

    // Still mark as completed even if no chunks were extracted
    await MaterialRepository.updateMaterialStatus(documentId, 'completed');

    console.log(
      `[Workflow] Step 5 completed: document ${documentId} marked as completed (no chunks extracted)`
    );
    return {
      success: true,
      data: { documentId, status: 'completed' },
    };
  }

  // Clean chunks content (remove null bytes)
  const cleanedChunks = chunksWithEmbeddings.map((chunk) => ({
    ...chunk,
    content: chunk.content.replace(/\x00/g, ''),
  }));

  // Store chunks with enhanced OCR data
  await MaterialRepository.storeChunksEnhanced({
    documentId,
    chunks: cleanedChunks,
  });

  // Update document with summary data if available
  if (summaryData?.summary) {
    await db
      .update(documents)
      .set({
        summary: summaryData.summary,
        summaryEmbedding: summaryData.summaryEmbedding || null,
      })
      .where(eq(documents.id, documentId));
  }

  // Add document to spaces if specified
  if (spaceIds && spaceIds.length > 0) {
    await SpaceRepository.addDocumentsToSpace([documentId], spaceIds);
  }

  // Mark document as completed
  await MaterialRepository.updateMaterialStatus(documentId, 'completed');

  console.log(`[Workflow] Step 5 completed: document ${documentId} finalized`);
  return {
    success: true,
    data: { documentId, status: 'completed' },
  };
}

/**
 * Main workflow function that orchestrates all steps
 */
export default async function documentProcessingWorkflow(context: WorkflowContext): Promise<void> {
  const payload = context.requestPayload as DocumentProcessingPayload;
  console.log(`[Workflow] Starting document processing for ${payload.fileName}`);

  // Step 1: Prepare document
  await context.run('step-1-prepare-document', async () => await step1PrepareDocument(payload));

  // Step 2a: Start OCR extraction (check if async is needed)
  const step2InitResult = await context.run(
    'step-2a-init-ocr-extraction',
    async () => await step2InitOCRExtraction(payload)
  );

  // Step 2b: Wait for async Textract completion (waitForEvent at root level)
  const step2InitData = step2InitResult.data as { jobId: string };
  const eventId = `textract-completion-${step2InitData.jobId}`;

  const { eventData, timeout } = await context.waitForEvent(
    `Wait for Textract job ${step2InitData.jobId} completion`,
    eventId,
    {
      timeout: 1800, // 30 minutes timeout
    }
  );

  if (timeout) {
    throw new Error(
      `Timeout waiting for Textract job completion: ${step2InitData.jobId} after 30 minutes`
    );
  }

  // Step 2c: Process async Textract results
  const step2Result = await context.run(
    'step-2c-process-async-results',
    async () => await step2ProcessAsyncResults(payload, step2InitData.jobId, eventData)
  );

  // Step 3: Generate embeddings
  const step2Data = step2Result.data as Step2Data;
  const step3Result = await context.run(
    'step-3-generate-embeddings',
    async () => await step3GenerateEmbeddings(payload, step2Data.chunks)
  );

  // Step 4: Generate summary
  const step3Data = step3Result.data as Step3Data;
  const step4Result = await context.run(
    'step-4-generate-summary',
    async () => await step4GenerateSummary(payload, step3Data.chunksWithEmbeddings)
  );

  // Step 5: Finalize document
  const summaryData = step4Result.success ? (step4Result.data as SummaryData) : null;
  await context.run(
    'step-5-finalize-document',
    async () => await step5FinalizeDocument(payload, step3Data.chunksWithEmbeddings, summaryData)
  );

  console.log(`[Workflow] Document processing completed successfully for ${payload.fileName}`);
}

export async function handleDocumentProcessingFailure(
  context: WorkflowContext,
  failStatus: number,
  failResponse: string,
  failHeaders: Record<string, string[]>
): Promise<void> {
  const payload = context.requestPayload as DocumentProcessingPayload;
  console.error(
    `[Workflow] Document processing failed for ${payload.fileName}:`,
    failResponse,
    failStatus,
    failHeaders
  );
  await MaterialRepository.updateMaterialStatus(payload.documentId, 'failed');
}
