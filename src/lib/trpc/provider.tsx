'use client';

import { clientApi } from '@/lib/trpc/client-api';
import { getUrl } from '@/lib/trpc/utils';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import SuperJSONTransformer from 'superjson';
import React, { useState } from 'react';

function TRPCProvider({
  children,
  cookies,
}: Readonly<{
  children: React.ReactNode;
  cookies: string;
}>) {
  const [queryClient] = useState(() => new QueryClient({}));
  const [trpcClient] = useState(() =>
    clientApi.createClient({
      links: [
        // loggerLink({
        //   enabled: (op) =>
        //     process.env.NODE_ENV === 'development' ||
        //     (op.direction === 'down' && op.result instanceof Error),
        // }),
        httpBatchLink({
          // unstable_httpBatchStreamLink({
          transformer: SuperJSONTransformer,
          url: getUrl(),
          headers() {
            return {
              cookie: cookies,
              'x-trpc-source': 'react',
            };
          },
        }),
      ],
    })
  );

  return (
    <clientApi.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </clientApi.Provider>
  );
}

export { TRPCProvider };
