import 'server-only';
import { AppRouter } from '@/server/trpc/routers';
import { createTR<PERSON><PERSON>lient, httpBatchLink } from '@trpc/client';
import SuperJSONTransformer from 'superjson';
import { headers } from 'next/headers';

export const serverApi = createTRPCClient<AppRouter>({
  links: [
    // loggerLink({
    //   enabled: (opts) =>
    //     process.env.NODE_ENV === 'development' ||
    //     (opts.direction === 'down' && opts.result instanceof Error),
    // }),
    httpBatchLink({
      url: `https://${process.env.VERCEL_PROJECT_PRODUCTION_URL || 'localhost:3000'}/api/trpc`,
      headers: () => {
        const h = new Map(headers());
        h.delete('connection');
        h.delete('transfer-encoding');
        h.set('x-trpc-source', 'server');
        return Object.fromEntries(h.entries());
      },
      transformer: SuperJSONTransformer,
    }),
  ],
});
