import dotenv from 'dotenv';
import dotenvExpand from 'dotenv-expand';
import { defineConfig } from 'drizzle-kit';

dotenvExpand.expand(dotenv.config());

// Read the .env file if it exists, or a file specified by the
// dotenv_config_path parameter that's passed to Node.js

let connectionString = process.env.DATABASE_URL;

console.log('connect sting:', connectionString);

if (process.env.NODE_ENV === 'test') {
  console.log('current ENV:', process.env.NODE_ENV);
  connectionString = process.env.DATABASE_TEST_URL;
}

if (!connectionString)
  throw new Error('`DATABASE_URL` or `DATABASE_TEST_URL` not found in environment');

export default defineConfig({
  dialect: 'postgresql',
  schema: './src/database/schema/index.ts',
  out: './src/database/migrations',

  dbCredentials: {
    url: connectionString,
  },
  verbose: true,
  strict: true,
});
